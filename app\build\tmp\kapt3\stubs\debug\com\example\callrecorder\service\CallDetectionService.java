package com.example.callrecorder.service;

/**
 * Service for detecting incoming and outgoing calls
 *
 * Android Version Limitations:
 * - Android 9+ (API 28+): Background execution limits affect call detection
 * - Android 10+ (API 29+): Scoped storage affects call log access
 * - Android 11+ (API 30+): Package visibility restrictions
 * - Android 12+ (API 31+): Exact alarm restrictions
 * - Android 13+ (API 33+): Runtime notification permission required
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\u0018\u0000 A2\u00020\u0001:\u0001AB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010/\u001a\u00020#H\u0002J\b\u00100\u001a\u00020#H\u0002J\u0006\u00101\u001a\u00020#J\u0012\u00102\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u001f\u001a\u00020\u0015H\u0002J\u000e\u00103\u001a\u00020#H\u0082@\u00a2\u0006\u0002\u00104J\u001a\u00105\u001a\u00020#2\u0006\u00106\u001a\u00020\u00112\b\u0010\u001f\u001a\u0004\u0018\u00010\u0015H\u0002J\u0010\u00107\u001a\u00020#2\u0006\u0010\u001f\u001a\u00020\u0015H\u0002J\b\u00108\u001a\u00020#H\u0002J\b\u00109\u001a\u00020#H\u0002J\u00aa\u0001\u0010:\u001a\u00020#2\u0006\u0010\u0012\u001a\u00020\u00132M\u0010\u001b\u001aI\u0012\u0013\u0012\u00110\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\u001f\u0012\u0013\u0012\u00110 \u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(!\u0012\u0015\u0012\u0013\u0018\u00010\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\"\u0012\u0004\u0012\u00020#0\u001c2K\u0010$\u001aG\u0012\u0013\u0012\u00110\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\u001f\u0012\u0013\u0012\u00110 \u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(!\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(%\u0012\u0004\u0012\u00020#0\u001cJ\b\u0010;\u001a\u00020#H\u0002J\b\u0010<\u001a\u00020#H\u0002J\b\u0010=\u001a\u00020#H\u0002J\b\u0010>\u001a\u00020#H\u0002J\u0006\u0010?\u001a\u00020#J\u0006\u0010@\u001a\u00020#R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\t\u001a\n\u0018\u00010\nj\u0004\u0018\u0001`\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000RW\u0010\u001b\u001aK\u0012\u0013\u0012\u00110\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\u001f\u0012\u0013\u0012\u00110 \u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(!\u0012\u0015\u0012\u0013\u0018\u00010\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\"\u0012\u0004\u0012\u00020#\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000RU\u0010$\u001aI\u0012\u0013\u0012\u00110\u0015\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(\u001f\u0012\u0013\u0012\u00110 \u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(!\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\f\b\u001d\u0012\b\b\u001e\u0012\u0004\b\b(%\u0012\u0004\u0012\u00020#\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010)X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010,\u001a\u0004\u0018\u00010-X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lcom/example/callrecorder/service/CallDetectionService;", "", "()V", "audioFocusChangeListener", "Landroid/media/AudioManager$OnAudioFocusChangeListener;", "audioManager", "Landroid/media/AudioManager;", "audioModeMonitor", "Landroid/os/Handler;", "audioModeRunnable", "Ljava/lang/Runnable;", "Lkotlinx/coroutines/Runnable;", "callLogObserver", "Landroid/database/ContentObserver;", "callStartTime", "", "consecutiveVoipModeCount", "", "context", "Landroid/content/Context;", "currentPhoneNumber", "", "isListening", "", "isVoIPCallActive", "lastAudioMode", "lastCallState", "onCallDetected", "Lkotlin/Function3;", "Lkotlin/ParameterName;", "name", "phoneNumber", "Lcom/example/callrecorder/data/CallType;", "callType", "contactName", "", "onCallEnded", "duration", "outgoingCallReceiver", "Landroid/content/BroadcastReceiver;", "phoneStateListener", "Landroid/telephony/PhoneStateListener;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "telephonyManager", "Landroid/telephony/TelephonyManager;", "voipCallDetectionCount", "checkAudioModeChanges", "checkForVoIPCall", "cleanup", "getContactName", "handleCallLogChange", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleCallStateChange", "state", "handleOutgoingCall", "handleVoIPCallEnd", "handleVoIPCallStart", "initialize", "setupCallLogObserver", "setupOutgoingCallReceiver", "setupPhoneStateListener", "setupVoIPCallDetection", "startListening", "stopListening", "Companion", "app_debug"})
public final class CallDetectionService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CallDetectionService";
    @org.jetbrains.annotations.Nullable()
    private android.content.Context context;
    @org.jetbrains.annotations.Nullable()
    private android.telephony.TelephonyManager telephonyManager;
    @org.jetbrains.annotations.Nullable()
    private android.telephony.PhoneStateListener phoneStateListener;
    @org.jetbrains.annotations.Nullable()
    private android.database.ContentObserver callLogObserver;
    @org.jetbrains.annotations.Nullable()
    private android.content.BroadcastReceiver outgoingCallReceiver;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioManager audioManager;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioManager.OnAudioFocusChangeListener audioFocusChangeListener;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler audioModeMonitor;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable audioModeRunnable;
    private boolean isListening = false;
    private int lastCallState = android.telephony.TelephonyManager.CALL_STATE_IDLE;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentPhoneNumber;
    private long callStartTime = 0L;
    private boolean isVoIPCallActive = false;
    private int lastAudioMode = android.media.AudioManager.MODE_NORMAL;
    private int voipCallDetectionCount = 0;
    private int consecutiveVoipModeCount = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function3<? super java.lang.String, ? super com.example.callrecorder.data.CallType, ? super java.lang.String, kotlin.Unit> onCallDetected;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function3<? super java.lang.String, ? super com.example.callrecorder.data.CallType, ? super java.lang.Long, kotlin.Unit> onCallEnded;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.service.CallDetectionService.Companion Companion = null;
    
    public CallDetectionService() {
        super();
    }
    
    /**
     * Initialize call detection service
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super com.example.callrecorder.data.CallType, ? super java.lang.String, kotlin.Unit> onCallDetected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super com.example.callrecorder.data.CallType, ? super java.lang.Long, kotlin.Unit> onCallEnded) {
    }
    
    /**
     * Start listening for calls
     */
    public final void startListening() {
    }
    
    /**
     * Stop listening for calls
     */
    public final void stopListening() {
    }
    
    /**
     * Setup phone state listener for call state changes
     */
    private final void setupPhoneStateListener() {
    }
    
    /**
     * Setup call log observer for detecting call details
     */
    private final void setupCallLogObserver() {
    }
    
    /**
     * Setup broadcast receiver for outgoing calls
     */
    private final void setupOutgoingCallReceiver() {
    }
    
    /**
     * Setup VoIP call detection using multiple methods
     */
    private final void setupVoIPCallDetection() {
    }
    
    /**
     * Check for VoIP call based on audio mode and running apps
     */
    private final void checkForVoIPCall() {
    }
    
    /**
     * Enhanced audio mode change detection for VoIP calls
     */
    private final void checkAudioModeChanges() {
    }
    
    /**
     * Handle VoIP call start with enhanced detection
     */
    private final void handleVoIPCallStart() {
    }
    
    /**
     * Handle VoIP call end with proper cleanup
     */
    private final void handleVoIPCallEnd() {
    }
    
    /**
     * Handle call state changes
     */
    private final void handleCallStateChange(int state, java.lang.String phoneNumber) {
    }
    
    /**
     * Handle outgoing call detection
     */
    private final void handleOutgoingCall(java.lang.String phoneNumber) {
    }
    
    /**
     * Handle call log changes
     */
    private final java.lang.Object handleCallLogChange(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Get contact name for phone number
     */
    private final java.lang.String getContactName(java.lang.String phoneNumber) {
        return null;
    }
    
    /**
     * Cleanup resources including VoIP detection
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/callrecorder/service/CallDetectionService$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}