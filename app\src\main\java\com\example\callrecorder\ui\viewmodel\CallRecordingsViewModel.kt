package com.example.callrecorder.ui.viewmodel

import android.content.Context
import android.media.MediaPlayer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.callrecorder.data.CallRecordingDisplay
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.storage.FileManager
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.io.File

class CallRecordingsViewModel(
    private val repository: CallRecordingRepository
) : ViewModel() {

    private val _recordings = MutableStateFlow<List<CallRecordingDisplay>>(emptyList())
    val recordings: StateFlow<List<CallRecordingDisplay>> = _recordings.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private var mediaPlayer: MediaPlayer? = null
    private var fileManager: FileManager? = null

    fun initialize(context: Context) {
        fileManager = FileManager(context)
        loadRecordings()
    }

    fun loadRecordings() {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null

            try {
                repository.getAllRecordings().collect { recordingsList ->
                    _recordings.value = recordingsList
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to load recordings: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun playRecording(recording: CallRecordingDisplay) {
        viewModelScope.launch {
            try {
                stopCurrentPlayback()

                val file = File(recording.filePath)
                if (!file.exists()) {
                    _errorMessage.value = "Recording file not found"
                    return@launch
                }

                mediaPlayer = MediaPlayer().apply {
                    setDataSource(file.absolutePath)
                    prepareAsync()
                    setOnPreparedListener { start() }
                    setOnCompletionListener {
                        release()
                        mediaPlayer = null
                    }
                    setOnErrorListener { _, _, _ ->
                        _errorMessage.value = "Error playing recording"
                        release()
                        mediaPlayer = null
                        true
                    }
                }

            } catch (e: Exception) {
                _errorMessage.value = "Failed to play recording: ${e.message}"
            }
        }
    }

    fun stopCurrentPlayback() {
        mediaPlayer?.let { player ->
            if (player.isPlaying) {
                player.stop()
            }
            player.release()
            mediaPlayer = null
        }
    }

    fun shareRecording(recording: CallRecordingDisplay) {
        // This will be handled by the UI layer with context
    }

    fun deleteRecording(recording: CallRecordingDisplay) {
        viewModelScope.launch {
            try {
                val file = File(recording.filePath)
                val deleted = fileManager?.deleteRecording(file) ?: false

                if (deleted) {
                    repository.deleteRecordingById(recording.id)
                    loadRecordings() // Refresh the list
                } else {
                    _errorMessage.value = "Failed to delete recording file"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Failed to delete recording: ${e.message}"
            }
        }
    }

    fun viewTranscript(recording: CallRecordingDisplay) {
        // This will be handled by the UI layer
    }

    fun clearError() {
        _errorMessage.value = null
    }

    override fun onCleared() {
        super.onCleared()
        stopCurrentPlayback()
    }
}
