package com.example.callrecorder.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.domain.CallRecording
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class CallRecordingsViewModel(
    private val repository: CallRecordingRepository
) : ViewModel() {
    private val _recordings = MutableStateFlow<List<CallRecording>>(emptyList())
    val recordings: StateFlow<List<CallRecording>> = _recordings

    fun loadRecordings() {
        viewModelScope.launch {
            _recordings.value = repository.getAllRecordings()
        }
    }

    fun deleteRecording(id: Long) {
        viewModelScope.launch {
            repository.deleteRecording(id)
            loadRecordings()
        }
    }
}
