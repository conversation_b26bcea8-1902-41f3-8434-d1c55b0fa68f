package com.example.callrecorder.permission

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Comprehensive permission manager for call recording app
 * 
 * Android Version Specific Permissions:
 * - API 24-28: Basic permissions work normally
 * - API 29+: MediaProjection required for call recording
 * - API 30+: MANAGE_EXTERNAL_STORAGE for full storage access
 * - API 31+: Exact alarm permission for scheduling
 * - API 33+: POST_NOTIFICATIONS for foreground service
 * - API 34+: Enhanced foreground service restrictions
 */
class PermissionManager(private val context: Context) {
    
    companion object {
        const val PERMISSION_REQUEST_CODE = 1001
        const val MANAGE_EXTERNAL_STORAGE_REQUEST_CODE = 1002
        const val SYSTEM_ALERT_WINDOW_REQUEST_CODE = 1003
        const val ACCESSIBILITY_SERVICE_REQUEST_CODE = 1004
        
        // Core permissions required for basic functionality
        val CORE_PERMISSIONS = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.READ_CALL_LOG,
            Manifest.permission.READ_CONTACTS
        )
        
        // Service permissions
        val SERVICE_PERMISSIONS = arrayOf(
            Manifest.permission.FOREGROUND_SERVICE,
            Manifest.permission.WAKE_LOCK
        )
        
        // Storage permissions (version dependent)
        val STORAGE_PERMISSIONS_LEGACY = arrayOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
        
        // Network permissions for AI features
        val NETWORK_PERMISSIONS = arrayOf(
            Manifest.permission.INTERNET,
            Manifest.permission.ACCESS_NETWORK_STATE
        )
    }
    
    /**
     * Check if all required permissions are granted
     */
    fun areAllPermissionsGranted(): Boolean {
        val requiredPermissions = getRequiredPermissions()
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Get list of required permissions based on Android version
     */
    fun getRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        // Core permissions (always required)
        permissions.addAll(CORE_PERMISSIONS)
        permissions.addAll(SERVICE_PERMISSIONS)
        permissions.addAll(NETWORK_PERMISSIONS)
        
        // Version-specific permissions
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> { // API 34+
                permissions.add(Manifest.permission.FOREGROUND_SERVICE_MICROPHONE)
                permissions.add(Manifest.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION)
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> { // API 33+
                permissions.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
        
        // Storage permissions for older versions
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) { // API 28 and below
            permissions.addAll(STORAGE_PERMISSIONS_LEGACY)
        }
        
        return permissions.toTypedArray()
    }
    
    /**
     * Get list of missing permissions
     */
    fun getMissingPermissions(): List<String> {
        return getRequiredPermissions().filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Request all missing permissions
     */
    fun requestMissingPermissions(activity: Activity) {
        val missingPermissions = getMissingPermissions()
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                activity,
                missingPermissions.toTypedArray(),
                PERMISSION_REQUEST_CODE
            )
        }
    }
    
    /**
     * Check if MANAGE_EXTERNAL_STORAGE permission is needed and granted
     */
    fun isManageExternalStorageGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            android.os.Environment.isExternalStorageManager()
        } else {
            true // Not needed for older versions
        }
    }
    
    /**
     * Request MANAGE_EXTERNAL_STORAGE permission
     */
    fun requestManageExternalStorage(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
            }
            activity.startActivityForResult(intent, MANAGE_EXTERNAL_STORAGE_REQUEST_CODE)
        }
    }
    
    /**
     * Check if System Alert Window permission is granted
     */
    fun isSystemAlertWindowGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Not needed for older versions
        }
    }
    
    /**
     * Request System Alert Window permission
     */
    fun requestSystemAlertWindow(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
            }
            activity.startActivityForResult(intent, SYSTEM_ALERT_WINDOW_REQUEST_CODE)
        }
    }
    
    /**
     * Check if Accessibility Service is enabled
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        val accessibilityEnabled = Settings.Secure.getInt(
            context.contentResolver,
            Settings.Secure.ACCESSIBILITY_ENABLED,
            0
        )
        
        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            val serviceName = "${context.packageName}/.service.CallAccessibilityService"
            return services?.contains(serviceName) == true
        }
        
        return false
    }
    
    /**
     * Open Accessibility Settings
     */
    fun openAccessibilitySettings(activity: Activity) {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        activity.startActivityForResult(intent, ACCESSIBILITY_SERVICE_REQUEST_CODE)
    }
    
    /**
     * Get permission explanation text
     */
    fun getPermissionExplanation(permission: String): String {
        return when (permission) {
            Manifest.permission.RECORD_AUDIO -> 
                "Record Audio permission is required to capture call audio during phone conversations."
            
            Manifest.permission.READ_PHONE_STATE -> 
                "Phone State permission is needed to detect when calls start and end."
            
            Manifest.permission.READ_CALL_LOG -> 
                "Call Log permission allows the app to identify caller information and call details."
            
            Manifest.permission.READ_CONTACTS -> 
                "Contacts permission helps display caller names instead of just phone numbers."
            
            Manifest.permission.FOREGROUND_SERVICE -> 
                "Foreground Service permission is required to record calls in the background."
            
            Manifest.permission.POST_NOTIFICATIONS -> 
                "Notification permission is required to show recording status and controls."
            
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> 
                "Storage permission is needed to save call recordings to your device."
            
            else -> "This permission is required for the app to function properly."
        }
    }
    
    /**
     * Handle permission request result
     */
    fun handlePermissionResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onAllGranted: () -> Unit,
        onSomeGranted: (granted: List<String>, denied: List<String>) -> Unit,
        onAllDenied: () -> Unit
    ) {
        when (requestCode) {
            PERMISSION_REQUEST_CODE -> {
                val granted = mutableListOf<String>()
                val denied = mutableListOf<String>()
                
                permissions.forEachIndexed { index, permission ->
                    if (grantResults[index] == PackageManager.PERMISSION_GRANTED) {
                        granted.add(permission)
                    } else {
                        denied.add(permission)
                    }
                }
                
                when {
                    denied.isEmpty() -> onAllGranted()
                    granted.isEmpty() -> onAllDenied()
                    else -> onSomeGranted(granted, denied)
                }
            }
        }
    }
}
