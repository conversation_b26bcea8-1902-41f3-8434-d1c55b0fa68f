[{"merged": "com.example.callrecorder.app-release-69:/xml_accessibility_service_config.xml.flat", "source": "com.example.callrecorder.app-main-70:/xml/accessibility_service_config.xml"}, {"merged": "com.example.callrecorder.app-release-69:/mipmap_ic_launcher_round.xml.flat", "source": "com.example.callrecorder.app-main-70:/mipmap/ic_launcher_round.xml"}, {"merged": "com.example.callrecorder.app-release-69:/xml_file_paths.xml.flat", "source": "com.example.callrecorder.app-main-70:/xml/file_paths.xml"}, {"merged": "com.example.callrecorder.app-release-69:/mipmap_ic_launcher.xml.flat", "source": "com.example.callrecorder.app-main-70:/mipmap/ic_launcher.xml"}]