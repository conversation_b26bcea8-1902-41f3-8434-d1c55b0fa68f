{"logs": [{"outputFile": "com.example.callrecorder.app-mergeDebugResources-70:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4732a0661345d7666aa559ef67f47092\\transformed\\jetified-foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "17164,17249", "endColumns": "84,84", "endOffsets": "17244,17329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\fba63af1cc2824d2492e5346a43ea817\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,535,641,730,835,956,1039,1121,1212,1305,1399,1493,1593,1686,1781,1875,1966,2057,2143,2253,2357,2460,2568,2676,2781,2946,16632", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "424,530,636,725,830,951,1034,1116,1207,1300,1394,1488,1588,1681,1776,1870,1961,2052,2138,2248,2352,2455,2563,2671,2776,2941,3046,16714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\a8ffa01484e5d0b2260db025a53ae0cf\\transformed\\jetified-ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4549,4643,4727,4817,4915,5224,5303,16077,16164,16253,16323,16473,16551,16719,16903,16978,17046", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "4638,4722,4812,4910,4996,5298,5404,16159,16248,16318,16388,16546,16627,16797,16973,17041,17159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\f481f24d3159e23b2c7b2fc7d3454301\\transformed\\jetified-material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4756,4846,4949,5029,5114,5215,5319,5412,5517,5604,5710,5809,5917,6035,6115,6215", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4751,4841,4944,5024,5109,5210,5314,5407,5512,5599,5705,5804,5912,6030,6110,6210,6304"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5470,5608,5745,5864,5998,6115,6214,6330,6472,6593,6735,6820,6926,7020,7121,7250,7379,7490,7619,7746,7876,8056,8178,8298,8420,8551,8646,8741,8874,9021,9118,9223,9333,9460,9592,9699,9800,9877,9980,10080,10171,10261,10364,10444,10529,10630,10734,10827,10932,11019,11125,11224,11332,11450,11530,11630", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "5603,5740,5859,5993,6110,6209,6325,6467,6588,6730,6815,6921,7015,7116,7245,7374,7485,7614,7741,7871,8051,8173,8293,8415,8546,8641,8736,8869,9016,9113,9218,9328,9455,9587,9694,9795,9872,9975,10075,10166,10256,10359,10439,10524,10625,10729,10822,10927,11014,11120,11219,11327,11445,11525,11625,11719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ae1fe1024e90fb6ad9a5df9a70112b97\\transformed\\core-1.13.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3518,3617,3719,3821,3924,4025,4127,16802", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3612,3714,3816,3919,4020,4122,4242,16898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d6004821a46f58cb297f64956cd160cd\\transformed\\material-1.11.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1106,1197,1263,1324,1414,1478,1545,1606,1675,1737,1791,1898,1957,2018,2072,2146,2266,2351,2435,2570,2641,2711,2843,2930,3013,3071,3127,3193,3266,3346,3441,3523,3592,3668,3748,3817,3926,4021,4104,4194,4289,4363,4437,4530,4584,4669,4736,4822,4907,4969,5033,5096,5162,5264,5363,5456,5555,5617,5677", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "266,364,457,540,641,733,837,954,1035,1101,1192,1258,1319,1409,1473,1540,1601,1670,1732,1786,1893,1952,2013,2067,2141,2261,2346,2430,2565,2636,2706,2838,2925,3008,3066,3122,3188,3261,3341,3436,3518,3587,3663,3743,3812,3921,4016,4099,4189,4284,4358,4432,4525,4579,4664,4731,4817,4902,4964,5028,5091,5157,5259,5358,5451,5550,5612,5672,5752"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3051,3149,3242,3325,3426,4247,4351,4468,5001,5067,5158,5409,11724,11814,11878,11945,12006,12075,12137,12191,12298,12357,12418,12472,12546,12666,12751,12835,12970,13041,13111,13243,13330,13413,13471,13527,13593,13666,13746,13841,13923,13992,14068,14148,14217,14326,14421,14504,14594,14689,14763,14837,14930,14984,15069,15136,15222,15307,15369,15433,15496,15562,15664,15763,15856,15955,16017,16393", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,97,92,82,100,91,103,116,80,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,83,134,70,69,131,86,82,57,55,65,72,79,94,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79", "endOffsets": "316,3144,3237,3320,3421,3513,4346,4463,4544,5062,5153,5219,5465,11809,11873,11940,12001,12070,12132,12186,12293,12352,12413,12467,12541,12661,12746,12830,12965,13036,13106,13238,13325,13408,13466,13522,13588,13661,13741,13836,13918,13987,14063,14143,14212,14321,14416,14499,14589,14684,14758,14832,14925,14979,15064,15131,15217,15302,15364,15428,15491,15557,15659,15758,15851,15950,16012,16072,16468"}}]}]}