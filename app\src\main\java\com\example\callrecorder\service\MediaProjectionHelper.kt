package com.example.callrecorder.service

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

/**
 * Helper for requesting MediaProjection permission and managing MediaProjection instance.
 * Note: MediaProjection can only capture device audio, not always call audio (Android 10+ limitation).
 */
class MediaProjectionHelper(private val app: Application) : AndroidViewModel(app) {
    private var mediaProjection: MediaProjection? = null
    private var mediaProjectionManager: MediaProjectionManager? = null

    fun requestMediaProjectionPermission(activity: Activity, launcher: ActivityResultLauncher<Intent>) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mediaProjectionManager = activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val intent = mediaProjectionManager?.createScreenCaptureIntent()
            launcher.launch(intent)
        }
    }

    fun onMediaProjectionResult(resultCode: Int, data: Intent?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && resultCode == Activity.RESULT_OK && data != null) {
            mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, data!!)
            // TODO: Pass mediaProjection to recording service
        }
    }

    override fun onCleared() {
        super.onCleared()
        mediaProjection?.stop()
        mediaProjection = null
    }
}
