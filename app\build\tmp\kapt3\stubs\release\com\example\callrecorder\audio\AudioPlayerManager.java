package com.example.callrecorder.audio;

/**
 * Manager for audio playback functionality
 * Handles MediaPlayer lifecycle and provides playback state
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u001b\u001a\u00020\u001cJ\u0006\u0010\u0012\u001a\u00020\tJ\u0006\u0010\u0014\u001a\u00020\tJ\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u001cJ\u000e\u0010 \u001a\u00020\u001c2\u0006\u0010!\u001a\u00020\"J\u000e\u0010#\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020%J\u0006\u0010&\u001a\u00020\u001cJ\u000e\u0010\'\u001a\u00020\u001c2\u0006\u0010(\u001a\u00020\tJ\b\u0010)\u001a\u00020\u001cH\u0002J\u0006\u0010*\u001a\u00020\u001cJ\b\u0010+\u001a\u00020\u001cH\u0002R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0010R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/example/callrecorder/audio/AudioPlayerManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_currentFile", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_currentPosition", "", "_duration", "_playbackState", "Lcom/example/callrecorder/audio/PlaybackState;", "currentFile", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentFile", "()Lkotlinx/coroutines/flow/StateFlow;", "currentPosition", "getCurrentPosition", "duration", "getDuration", "mediaPlayer", "Landroid/media/MediaPlayer;", "playbackState", "getPlaybackState", "positionUpdateRunnable", "Ljava/lang/Runnable;", "cleanup", "", "isPlaying", "", "pause", "playFile", "file", "Ljava/io/File;", "playUri", "uri", "Landroid/net/Uri;", "resume", "seekTo", "position", "startPositionUpdates", "stop", "stopPositionUpdates", "app_release"})
public final class AudioPlayerManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer mediaPlayer;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.callrecorder.audio.PlaybackState> _playbackState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.callrecorder.audio.PlaybackState> playbackState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _currentPosition = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> currentPosition = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _duration = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> duration = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _currentFile = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> currentFile = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable positionUpdateRunnable;
    
    public AudioPlayerManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.callrecorder.audio.PlaybackState> getPlaybackState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getCurrentPosition() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getDuration() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getCurrentFile() {
        return null;
    }
    
    /**
     * Play audio file
     */
    public final void playFile(@org.jetbrains.annotations.NotNull()
    java.io.File file) {
    }
    
    /**
     * Play audio from URI
     */
    public final void playUri(@org.jetbrains.annotations.NotNull()
    android.net.Uri uri) {
    }
    
    /**
     * Pause playback
     */
    public final void pause() {
    }
    
    /**
     * Resume playback
     */
    public final void resume() {
    }
    
    /**
     * Stop playback
     */
    public final void stop() {
    }
    
    /**
     * Seek to position
     */
    public final void seekTo(int position) {
    }
    
    /**
     * Get current playback position
     */
    public final int getCurrentPosition() {
        return 0;
    }
    
    /**
     * Get duration
     */
    public final int getDuration() {
        return 0;
    }
    
    /**
     * Check if currently playing
     */
    public final boolean isPlaying() {
        return false;
    }
    
    /**
     * Start position updates
     */
    private final void startPositionUpdates() {
    }
    
    /**
     * Stop position updates
     */
    private final void stopPositionUpdates() {
    }
    
    /**
     * Cleanup resources
     */
    public final void cleanup() {
    }
}