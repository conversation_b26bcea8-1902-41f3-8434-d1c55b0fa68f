package com.example.callrecorder.service;

/**
 * AccessibilityService for call recording triggers (for VoIP or UI automation only).
 * Note: Cannot capture native call audio, but can be used to detect call state or automate MediaProjection.
 * Always inform the user and get consent before recording.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u0016J\b\u0010\u0007\u001a\u00020\u0004H\u0016\u00a8\u0006\b"}, d2 = {"Lcom/example/callrecorder/service/CallAccessibilityService;", "Landroid/accessibilityservice/AccessibilityService;", "()V", "onAccessibilityEvent", "", "event", "Landroid/view/accessibility/AccessibilityEvent;", "onInterrupt", "app_debug"})
public final class CallAccessibilityService extends android.accessibilityservice.AccessibilityService {
    
    public CallAccessibilityService() {
        super();
    }
    
    @java.lang.Override()
    public void onAccessibilityEvent(@org.jetbrains.annotations.Nullable()
    android.view.accessibility.AccessibilityEvent event) {
    }
    
    @java.lang.Override()
    public void onInterrupt() {
    }
}