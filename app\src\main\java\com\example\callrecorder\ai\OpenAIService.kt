package com.example.callrecorder.ai

import android.content.Context
import android.util.Log
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.storage.FileManager
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.*
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * OpenAI Whisper API integration for call transcription
 * 
 * Features:
 * - Audio transcription using Whisper API
 * - Support for multiple audio formats
 * - Error handling and retry logic
 * - Cost estimation and usage tracking
 */
class OpenAIService(private val context: Context) {
    
    companion object {
        private const val TAG = "OpenAIService"
        private const val BASE_URL = "https://api.openai.com/v1/"
        private const val MAX_FILE_SIZE = 25 * 1024 * 1024 // 25MB limit
        private const val TIMEOUT_SECONDS = 60L
    }
    
    private var apiKey: String? = null
    private var apiService: OpenAIApiService? = null
    
    /**
     * Initialize the service with API key
     */
    fun initialize(apiKey: String) {
        this.apiKey = apiKey
        
        val okHttpClient = OkHttpClient.Builder()
            .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("Authorization", "Bearer $apiKey")
                    .build()
                chain.proceed(request)
            }
            .build()
        
        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        apiService = retrofit.create(OpenAIApiService::class.java)
    }
    
    /**
     * Transcribe audio file using Whisper API
     */
    suspend fun transcribeAudio(
        audioFile: File,
        language: String? = null,
        prompt: String? = null
    ): TranscriptionResult = withContext(Dispatchers.IO) {
        
        try {
            // Validate API key
            if (apiKey.isNullOrBlank()) {
                return@withContext TranscriptionResult.Error("OpenAI API key not configured")
            }
            
            // Validate file
            if (!audioFile.exists()) {
                return@withContext TranscriptionResult.Error("Audio file not found")
            }
            
            if (audioFile.length() > MAX_FILE_SIZE) {
                return@withContext TranscriptionResult.Error(
                    "File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB"
                )
            }
            
            // Prepare request
            val fileRequestBody = audioFile.asRequestBody("audio/*".toMediaType())
            val filePart = MultipartBody.Part.createFormData("file", audioFile.name, fileRequestBody)
            val modelPart = RequestBody.create("text/plain".toMediaType(), "whisper-1")
            
            val languagePart = language?.let { 
                RequestBody.create("text/plain".toMediaType(), it) 
            }
            val promptPart = prompt?.let { 
                RequestBody.create("text/plain".toMediaType(), it) 
            }
            
            Log.d(TAG, "Starting transcription for file: ${audioFile.name}")
            
            // Make API call
            val response = apiService?.transcribeAudio(
                file = filePart,
                model = modelPart,
                language = languagePart,
                prompt = promptPart,
                responseFormat = RequestBody.create("text/plain".toMediaType(), "json"),
                temperature = RequestBody.create("text/plain".toMediaType(), "0")
            )
            
            if (response?.isSuccessful == true) {
                val transcriptionResponse = response.body()
                if (transcriptionResponse != null) {
                    Log.d(TAG, "Transcription successful")
                    TranscriptionResult.Success(
                        text = transcriptionResponse.text,
                        language = transcriptionResponse.language,
                        duration = transcriptionResponse.duration
                    )
                } else {
                    TranscriptionResult.Error("Empty response from API")
                }
            } else {
                val errorBody = response?.errorBody()?.string()
                Log.e(TAG, "Transcription failed: ${response?.code()} - $errorBody")
                
                val errorMessage = when (response?.code()) {
                    401 -> "Invalid API key"
                    429 -> "Rate limit exceeded. Please try again later"
                    413 -> "File too large"
                    400 -> "Invalid request. Check file format"
                    else -> "API error: ${response?.code()} - $errorBody"
                }
                
                TranscriptionResult.Error(errorMessage)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Transcription error", e)
            TranscriptionResult.Error("Network error: ${e.message}")
        }
    }
    
    /**
     * Estimate transcription cost
     */
    fun estimateTranscriptionCost(audioFile: File): CostEstimate {
        val fileSizeMB = audioFile.length() / (1024.0 * 1024.0)
        val estimatedDurationMinutes = fileSizeMB * 2 // Rough estimate: 1MB ≈ 2 minutes
        
        // OpenAI Whisper pricing: $0.006 per minute
        val estimatedCost = estimatedDurationMinutes * 0.006
        
        return CostEstimate(
            fileSizeMB = fileSizeMB,
            estimatedDurationMinutes = estimatedDurationMinutes,
            estimatedCostUSD = estimatedCost
        )
    }
    
    /**
     * Check if API key is valid
     */
    suspend fun validateApiKey(): Boolean = withContext(Dispatchers.IO) {
        try {
            // Create a small test file
            val testFile = File(context.cacheDir, "test_audio.txt")
            testFile.writeText("test")
            
            val result = transcribeAudio(testFile)
            testFile.delete()
            
            // If we get a specific error about file format, the API key is valid
            when (result) {
                is TranscriptionResult.Error -> {
                    !result.message.contains("Invalid API key") && 
                    !result.message.contains("401")
                }
                is TranscriptionResult.Success -> true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get supported audio formats
     */
    fun getSupportedFormats(): List<String> {
        return listOf(
            "mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm"
        )
    }
    
    /**
     * Check if file format is supported
     */
    fun isFormatSupported(file: File): Boolean {
        val extension = file.extension.lowercase()
        return getSupportedFormats().contains(extension)
    }
}

/**
 * OpenAI API service interface
 */
interface OpenAIApiService {
    @Multipart
    @POST("audio/transcriptions")
    suspend fun transcribeAudio(
        @Part file: MultipartBody.Part,
        @Part("model") model: RequestBody,
        @Part("language") language: RequestBody? = null,
        @Part("prompt") prompt: RequestBody? = null,
        @Part("response_format") responseFormat: RequestBody? = null,
        @Part("temperature") temperature: RequestBody? = null
    ): Response<TranscriptionResponse>
}

/**
 * API response data classes
 */
data class TranscriptionResponse(
    @SerializedName("text")
    val text: String,
    @SerializedName("language")
    val language: String? = null,
    @SerializedName("duration")
    val duration: Double? = null
)

/**
 * Transcription result sealed class
 */
sealed class TranscriptionResult {
    data class Success(
        val text: String,
        val language: String?,
        val duration: Double?
    ) : TranscriptionResult()
    
    data class Error(
        val message: String
    ) : TranscriptionResult()
}

/**
 * Cost estimation data class
 */
data class CostEstimate(
    val fileSizeMB: Double,
    val estimatedDurationMinutes: Double,
    val estimatedCostUSD: Double
) {
    fun getFormattedCost(): String {
        return String.format("$%.4f", estimatedCostUSD)
    }
    
    fun getFormattedDuration(): String {
        val minutes = estimatedDurationMinutes.toInt()
        val seconds = ((estimatedDurationMinutes - minutes) * 60).toInt()
        return String.format("%d:%02d", minutes, seconds)
    }
}

/**
 * Transcription manager for handling batch transcriptions and queue
 */
class TranscriptionManager(
    private val context: Context,
    private val openAIService: OpenAIService,
    private val repository: CallRecordingRepository,
    private val fileManager: com.example.callrecorder.storage.FileManager
) {

    private val transcriptionQueue = mutableListOf<Long>() // Recording IDs
    private var isProcessing = false

    /**
     * Add recording to transcription queue
     */
    suspend fun queueForTranscription(recordingId: Long) {
        if (!transcriptionQueue.contains(recordingId)) {
            transcriptionQueue.add(recordingId)
            processQueue()
        }
    }

    /**
     * Process transcription queue
     */
    private suspend fun processQueue() {
        if (isProcessing || transcriptionQueue.isEmpty()) return

        isProcessing = true

        try {
            while (transcriptionQueue.isNotEmpty()) {
                val recordingId = transcriptionQueue.removeAt(0)
                transcribeRecording(recordingId)
            }
        } finally {
            isProcessing = false
        }
    }

    /**
     * Transcribe a single recording
     */
    private suspend fun transcribeRecording(recordingId: Long) {
        try {
            val recording = repository.getRecordingById(recordingId) ?: return
            val audioFile = File(recording.filePath)

            if (!audioFile.exists()) {
                Log.e("TranscriptionManager", "Audio file not found: ${recording.filePath}")
                return
            }

            val result = openAIService.transcribeAudio(audioFile)

            when (result) {
                is TranscriptionResult.Success -> {
                    // Save transcript to file
                    val transcriptFile = fileManager.createTranscriptFile(audioFile)
                    val success = fileManager.writeTranscript(transcriptFile, result.text)

                    if (success) {
                        // Update database
                        repository.updateTranscriptionStatus(
                            recordingId,
                            true,
                            transcriptFile.absolutePath
                        )
                        Log.d("TranscriptionManager", "Transcription completed for recording $recordingId")
                    }
                }

                is TranscriptionResult.Error -> {
                    Log.e("TranscriptionManager", "Transcription failed for recording $recordingId: ${result.message}")
                }
            }

        } catch (e: Exception) {
            Log.e("TranscriptionManager", "Error transcribing recording $recordingId", e)
        }
    }

    /**
     * Get queue status
     */
    fun getQueueStatus(): TranscriptionQueueStatus {
        return TranscriptionQueueStatus(
            queueSize = transcriptionQueue.size,
            isProcessing = isProcessing
        )
    }
}

data class TranscriptionQueueStatus(
    val queueSize: Int,
    val isProcessing: Boolean
)
