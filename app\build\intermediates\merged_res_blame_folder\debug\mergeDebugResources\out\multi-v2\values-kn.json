{"logs": [{"outputFile": "com.example.callrecorder.app-mergeDebugResources-70:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\fba63af1cc2824d2492e5346a43ea817\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,433,545,658,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,16827", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "428,540,653,741,848,974,1052,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2147,2263,2373,2472,2585,2690,2804,2968,3068,16905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\a8ffa01484e5d0b2260db025a53ae0cf\\transformed\\jetified-ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4582,4679,4763,4859,4959,5271,5355,16259,16350,16435,16506,16659,16741,16910,17090,17167,17236", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "4674,4758,4854,4954,5043,5350,5443,16345,16430,16501,16572,16736,16822,16984,17162,17231,17349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4732a0661345d7666aa559ef67f47092\\transformed\\jetified-foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "17354,17442", "endColumns": "87,94", "endOffsets": "17437,17532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d6004821a46f58cb297f64956cd160cd\\transformed\\material-1.11.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,353,436,518,633,728,835,948,1033,1096,1190,1256,1318,1421,1487,1558,1617,1693,1758,1812,1925,1983,2044,2098,2177,2293,2376,2467,2609,2688,2767,2896,2984,3068,3125,3177,3243,3323,3413,3497,3576,3653,3730,3807,3876,3993,4092,4169,4262,4357,4431,4512,4608,4659,4743,4811,4897,4985,5048,5113,5176,5244,5349,5454,5549,5652,5713,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "264,348,431,513,628,723,830,943,1028,1091,1185,1251,1313,1416,1482,1553,1612,1688,1753,1807,1920,1978,2039,2093,2172,2288,2371,2462,2604,2683,2762,2891,2979,3063,3120,3172,3238,3318,3408,3492,3571,3648,3725,3802,3871,3988,4087,4164,4257,4352,4426,4507,4603,4654,4738,4806,4892,4980,5043,5108,5171,5239,5344,5449,5544,5647,5708,5764,5846"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3073,3157,3240,3322,3437,4277,4384,4497,5048,5111,5205,5448,11808,11911,11977,12048,12107,12183,12248,12302,12415,12473,12534,12588,12667,12783,12866,12957,13099,13178,13257,13386,13474,13558,13615,13667,13733,13813,13903,13987,14066,14143,14220,14297,14366,14483,14582,14659,14752,14847,14921,15002,15098,15149,15233,15301,15387,15475,15538,15603,15666,15734,15839,15944,16039,16142,16203,16577", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "314,3152,3235,3317,3432,3527,4379,4492,4577,5106,5200,5266,5505,11906,11972,12043,12102,12178,12243,12297,12410,12468,12529,12583,12662,12778,12861,12952,13094,13173,13252,13381,13469,13553,13610,13662,13728,13808,13898,13982,14061,14138,14215,14292,14361,14478,14577,14654,14747,14842,14916,14997,15093,15144,15228,15296,15382,15470,15533,15598,15661,15729,15834,15939,16034,16137,16198,16254,16654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ae1fe1024e90fb6ad9a5df9a70112b97\\transformed\\core-1.13.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3630,3733,3834,3940,4041,4149,16989", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3625,3728,3829,3935,4036,4144,4272,17085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\f481f24d3159e23b2c7b2fc7d3454301\\transformed\\jetified-material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4765,4854,4965,5045,5129,5229,5337,5437,5538,5625,5738,5840,5945,6066,6146,6256", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4760,4849,4960,5040,5124,5224,5332,5432,5533,5620,5733,5835,5940,6061,6141,6251,6348"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5510,5634,5758,5880,6003,6102,6200,6315,6472,6602,6754,6840,6946,7042,7144,7260,7393,7504,7643,7778,7911,8089,8213,8331,8452,8579,8676,8773,8895,9033,9139,9248,9354,9493,9638,9748,9857,9933,10033,10133,10220,10309,10420,10500,10584,10684,10792,10892,10993,11080,11193,11295,11400,11521,11601,11711", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "5629,5753,5875,5998,6097,6195,6310,6467,6597,6749,6835,6941,7037,7139,7255,7388,7499,7638,7773,7906,8084,8208,8326,8447,8574,8671,8768,8890,9028,9134,9243,9349,9488,9633,9743,9852,9928,10028,10128,10215,10304,10415,10495,10579,10679,10787,10887,10988,11075,11188,11290,11395,11516,11596,11706,11803"}}]}]}