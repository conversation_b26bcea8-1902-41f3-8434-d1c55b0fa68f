package com.example.callrecorder

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.example.callrecorder.data.CallType
import com.example.callrecorder.service.CallDetectionService
import com.example.callrecorder.service.CallRecordingService
import com.example.callrecorder.ui.CallRecorderApp

class MainActivity : ComponentActivity() {

    private var callDetectionService: CallDetectionService? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize call detection service
        initializeCallDetection()

        setContent {
            CallRecorderApp()
        }
    }

    private fun initializeCallDetection() {
        callDetectionService = CallDetectionService()
        callDetectionService?.initialize(
            context = this,
            onCallDetected = { phoneNumber, callType, contactName ->
                Log.d("MainActivity", "Call detected: $phoneNumber, type: $callType")
                startCallRecording(phoneNumber, callType, contactName)
            },
            onCallEnded = { phoneNumber, callType, duration ->
                Log.d("MainActivity", "Call ended: $phoneNumber, duration: $duration")
                stopCallRecording()
            }
        )
        callDetectionService?.startListening()
    }

    private fun startCallRecording(phoneNumber: String, callType: CallType, contactName: String?) {
        val intent = Intent(this, CallRecordingService::class.java).apply {
            action = CallRecordingService.ACTION_START_RECORDING
            putExtra(CallRecordingService.EXTRA_PHONE_NUMBER, phoneNumber)
            putExtra(CallRecordingService.EXTRA_CALL_TYPE, callType.name)
            putExtra(CallRecordingService.EXTRA_CONTACT_NAME, contactName)
        }

        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.d("MainActivity", "Recording service started")
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to start recording service", e)
        }
    }

    private fun stopCallRecording() {
        val intent = Intent(this, CallRecordingService::class.java).apply {
            action = CallRecordingService.ACTION_STOP_RECORDING
        }
        startService(intent)
        Log.d("MainActivity", "Recording service stopped")
    }

    override fun onDestroy() {
        super.onDestroy()
        callDetectionService?.cleanup()
    }
}
