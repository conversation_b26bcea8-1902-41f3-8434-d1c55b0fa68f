package com.example.callrecorder.util;

/**
 * Error types enum
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0015\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/example/callrecorder/util/ErrorType;", "", "(Ljava/lang/String;I)V", "CRITICAL_PERMISSION_MISSING", "OPTIONAL_PERMISSION_MISSING", "STORAGE_PERMISSION_DENIED", "ANDROID_10_LIMITATION", "UNSUPPORTED_ANDROID_VERSION", "UNKNOWN_COMPATIBILITY", "AUDIO_RECORD_FAILED", "MEDIA_PROJECTION_FAILED", "UNKNOWN_RECORDING_ERROR", "INSUFFICIENT_STORAGE", "FILE_NOT_FOUND", "STORAGE_ERROR", "UNKNOWN_FILE_ERROR", "NETWORK_TIMEOUT", "NO_NETWORK", "API_AUTHENTICATION_FAILED", "API_RATE_LIMIT", "UNKNOWN_NETWORK_ERROR", "DATABASE_ERROR", "app_debug"})
public enum ErrorType {
    /*public static final*/ CRITICAL_PERMISSION_MISSING /* = new CRITICAL_PERMISSION_MISSING() */,
    /*public static final*/ OPTIONAL_PERMISSION_MISSING /* = new OPTIONAL_PERMISSION_MISSING() */,
    /*public static final*/ STORAGE_PERMISSION_DENIED /* = new STORAGE_PERMISSION_DENIED() */,
    /*public static final*/ ANDROID_10_LIMITATION /* = new ANDROID_10_LIMITATION() */,
    /*public static final*/ UNSUPPORTED_ANDROID_VERSION /* = new UNSUPPORTED_ANDROID_VERSION() */,
    /*public static final*/ UNKNOWN_COMPATIBILITY /* = new UNKNOWN_COMPATIBILITY() */,
    /*public static final*/ AUDIO_RECORD_FAILED /* = new AUDIO_RECORD_FAILED() */,
    /*public static final*/ MEDIA_PROJECTION_FAILED /* = new MEDIA_PROJECTION_FAILED() */,
    /*public static final*/ UNKNOWN_RECORDING_ERROR /* = new UNKNOWN_RECORDING_ERROR() */,
    /*public static final*/ INSUFFICIENT_STORAGE /* = new INSUFFICIENT_STORAGE() */,
    /*public static final*/ FILE_NOT_FOUND /* = new FILE_NOT_FOUND() */,
    /*public static final*/ STORAGE_ERROR /* = new STORAGE_ERROR() */,
    /*public static final*/ UNKNOWN_FILE_ERROR /* = new UNKNOWN_FILE_ERROR() */,
    /*public static final*/ NETWORK_TIMEOUT /* = new NETWORK_TIMEOUT() */,
    /*public static final*/ NO_NETWORK /* = new NO_NETWORK() */,
    /*public static final*/ API_AUTHENTICATION_FAILED /* = new API_AUTHENTICATION_FAILED() */,
    /*public static final*/ API_RATE_LIMIT /* = new API_RATE_LIMIT() */,
    /*public static final*/ UNKNOWN_NETWORK_ERROR /* = new UNKNOWN_NETWORK_ERROR() */,
    /*public static final*/ DATABASE_ERROR /* = new DATABASE_ERROR() */;
    
    ErrorType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.callrecorder.util.ErrorType> getEntries() {
        return null;
    }
}