C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PermissionAndConsentScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\screens\PermissionScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ai\OpenAIService.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\MediaProjectionHelper.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\MainActivity.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\screens\RecordingControlScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\screens\RecordingsListScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\domain\CallRecording.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\InMemoryCallRecordingRepository.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PrivacyPolicyScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\viewmodel\CallRecordingsViewModel.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\CallDetectionService.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\MainScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\util\ErrorHandler.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\util\RecordingServiceStarter.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\CallRecordingDatabase.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\storage\FileManager.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PlaybackControls.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\permission\PermissionManager.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\StartRecordingButton.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\Converters.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\CallRecorderApp.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\CallRecordingService.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\CallRecordingDao.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\audio\AudioPlayerManager.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\CallAccessibilityService.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\CallRecording.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\dialogs\ConsentDialog.kt