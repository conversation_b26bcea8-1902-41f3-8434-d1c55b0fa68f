C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PermissionAndConsentScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\MediaProjectionHelper.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\viewmodel\CallRecordingsViewModel.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\CallRecordingRepository.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\CallRecordingsListScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\MainActivity.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\MainScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\util\RecordingServiceStarter.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PlaybackControls.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\StartRecordingButton.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\CallRecorderApp.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\domain\CallRecording.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\CallRecordingService.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\data\InMemoryCallRecordingRepository.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\ui\PrivacyPolicyScreen.kt
C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\java\com\example\callrecorder\service\CallAccessibilityService.kt