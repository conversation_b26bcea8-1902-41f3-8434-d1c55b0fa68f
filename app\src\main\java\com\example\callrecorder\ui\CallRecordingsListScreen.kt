package com.example.callrecorder.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import android.content.Intent
import android.net.Uri
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.callrecorder.domain.CallRecording
import com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel

@Composable
fun CallRecordingsListScreen(viewModel: CallRecordingsViewModel) {
    val recordings = viewModel.recordings.collectAsState().value
    Column(modifier = Modifier.fillMaxSize().padding(16.dp)) {
        Text("Recorded Calls", style = MaterialTheme.typography.titleLarge)
        Spacer(modifier = Modifier.height(8.dp))
        LazyColumn(modifier = Modifier.weight(1f)) {
            items(recordings) { recording ->
                CallRecordingItem(recording = recording, onDelete = { viewModel.deleteRecording(recording.id) })
                Divider()
            }
        }
    }
}

@Composable
fun CallRecordingItem(recording: CallRecording, onDelete: () -> Unit) {
    val context = LocalContext.current
    Column(modifier = Modifier.fillMaxWidth().padding(8.dp)) {
        Text("${recording.callerName ?: recording.phoneNumber ?: "Unknown"}", style = MaterialTheme.typography.bodyLarge)
        Text("${recording.callType} - ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.US).format(java.util.Date(recording.date))}", style = MaterialTheme.typography.bodySmall)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            PlaybackControls(recording)
            Button(onClick = {
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "audio/*"
                    putExtra(Intent.EXTRA_STREAM, Uri.parse(recording.filePath))
                }
                context.startActivity(Intent.createChooser(shareIntent, "Share Recording"))
            }) { Text("Share") }
            Button(onClick = onDelete, colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)) { Text("Delete") }
        }
        if (!recording.transcription.isNullOrEmpty()) {
            Text("Transcript: ${recording.transcription}", style = MaterialTheme.typography.bodySmall)
        }
    }
}
