package com.example.callrecorder.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0007\u00a8\u0006\u0005"}, d2 = {"ConsentScreen", "", "onConsentGiven", "Lkotlin/Function0;", "MainScreen", "app_release"})
public final class MainScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void MainScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ConsentScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConsentGiven) {
    }
}