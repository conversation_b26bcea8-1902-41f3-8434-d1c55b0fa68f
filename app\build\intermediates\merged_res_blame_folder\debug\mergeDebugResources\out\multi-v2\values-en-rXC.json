{"logs": [{"outputFile": "com.example.callrecorder.app-mergeDebugResources-61:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\fba63af1cc2824d2492e5346a43ea817\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,21086", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,21267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\a8ffa01484e5d0b2260db025a53ae0cf\\transformed\\jetified-ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "36,37,38,39,40,41,42,99,100,101,102,103,104,106,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6955,7146,7331,7528,7730,7917,8102,20013,20201,20388,20553,20720,20901,21272,21642,21821,21988", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "7141,7326,7523,7725,7912,8097,8290,20196,20383,20548,20715,20896,21081,21433,21816,21983,22221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4732a0661345d7666aa559ef67f47092\\transformed\\jetified-foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "22226,22414", "endColumns": "187,186", "endOffsets": "22409,22596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ae1fe1024e90fb6ad9a5df9a70112b97\\transformed\\core-1.13.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "29,30,31,32,33,34,35,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5528,5724,5929,6130,6331,6538,6743,21438", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "5719,5924,6125,6326,6533,6738,6950,21637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\f481f24d3159e23b2c7b2fc7d3454301\\transformed\\jetified-material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8587,8777,8982,9164,9350,9554,9758,9960,10162,10352,10561,10765,10972,11191,11374,11575", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8582,8772,8977,9159,9345,9549,9753,9955,10157,10347,10556,10760,10967,11186,11369,11570,11768"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8295,8515,8732,8946,9163,9364,9562,9772,10011,10229,10468,10654,10854,11048,11248,11469,11697,11904,12134,12360,12589,12850,13072,13291,13512,13737,13932,14132,14350,14576,14775,14978,15183,15413,15654,15863,16064,16243,16441,16637,16827,17017,17222,17404,17590,17794,17998,18200,18402,18592,18801,19005,19212,19431,19614,19815", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "8510,8727,8941,9158,9359,9557,9767,10006,10224,10463,10649,10849,11043,11243,11464,11692,11899,12129,12355,12584,12845,13067,13286,13507,13732,13927,14127,14345,14571,14770,14973,15178,15408,15649,15858,16059,16238,16436,16632,16822,17012,17217,17399,17585,17789,17993,18195,18397,18587,18796,19000,19207,19426,19609,19810,20008"}}]}]}