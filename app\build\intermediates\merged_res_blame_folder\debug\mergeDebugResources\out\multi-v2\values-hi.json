{"logs": [{"outputFile": "com.example.callrecorder.app-mergeDebugResources-61:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ae1fe1024e90fb6ad9a5df9a70112b97\\transformed\\core-1.13.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3527,3630,3735,3836,3949,4055,16728", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3522,3625,3730,3831,3944,4050,4177,16824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\a8ffa01484e5d0b2260db025a53ae0cf\\transformed\\jetified-ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4504,4599,4682,4775,4873,5190,5268,16017,16106,16191,16259,16407,16488,16655,16829,16910,16976", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "4594,4677,4770,4868,4957,5263,5360,16101,16186,16254,16323,16483,16568,16723,16905,16971,17091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4732a0661345d7666aa559ef67f47092\\transformed\\jetified-foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "17096,17181", "endColumns": "84,85", "endOffsets": "17176,17262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\fba63af1cc2824d2492e5346a43ea817\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,16573", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,16650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d6004821a46f58cb297f64956cd160cd\\transformed\\material-1.11.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2812,2909,3007,3064,3119,3185,3255,3332,3418,3503,3571,3647,3728,3806,3907,3993,4080,4177,4276,4350,4420,4524,4578,4665,4732,4822,4914,4976,5040,5103,5169,5274,5384,5485,5592,5653,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2807,2904,3002,3059,3114,3180,3250,3327,3413,3498,3566,3642,3723,3801,3902,3988,4075,4172,4271,4345,4415,4519,4573,4660,4727,4817,4909,4971,5035,5098,5164,5269,5379,5480,5587,5648,5707,5786"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,3333,4182,4289,4421,4962,5027,5121,5365,11611,11696,11759,11822,11880,11945,12006,12067,12173,12231,12291,12350,12420,12536,12615,12695,12829,12904,12980,13117,13214,13312,13369,13424,13490,13560,13637,13723,13808,13876,13952,14033,14111,14212,14298,14385,14482,14581,14655,14725,14829,14883,14970,15037,15127,15219,15281,15345,15408,15474,15579,15689,15790,15897,15958,16328", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "304,3064,3140,3221,3328,3424,4284,4416,4499,5022,5116,5185,5419,11691,11754,11817,11875,11940,12001,12062,12168,12226,12286,12345,12415,12531,12610,12690,12824,12899,12975,13112,13209,13307,13364,13419,13485,13555,13632,13718,13803,13871,13947,14028,14106,14207,14293,14380,14477,14576,14650,14720,14824,14878,14965,15032,15122,15214,15276,15340,15403,15469,15574,15684,15785,15892,15953,16012,16402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\f481f24d3159e23b2c7b2fc7d3454301\\transformed\\jetified-material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4707,4797,4902,4982,5066,5166,5266,5361,5463,5549,5651,5749,5853,5968,6048,6148", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4702,4792,4897,4977,5061,5161,5261,5356,5458,5544,5646,5744,5848,5963,6043,6143,6237"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5424,5542,5660,5784,5900,5995,6091,6204,6342,6462,6612,6697,6800,6891,6988,7118,7238,7346,7491,7637,7767,7956,8083,8201,8323,8449,8541,8636,8764,8890,8989,9091,9203,9349,9501,9615,9715,9791,9891,9990,10076,10166,10271,10351,10435,10535,10635,10730,10832,10918,11020,11118,11222,11337,11417,11517", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "5537,5655,5779,5895,5990,6086,6199,6337,6457,6607,6692,6795,6886,6983,7113,7233,7341,7486,7632,7762,7951,8078,8196,8318,8444,8536,8631,8759,8885,8984,9086,9198,9344,9496,9610,9710,9786,9886,9985,10071,10161,10266,10346,10430,10530,10630,10725,10827,10913,11015,11113,11217,11332,11412,11512,11606"}}]}]}