package com.example.callrecorder.util

import android.content.Context
import android.content.Intent
import android.os.Build
import com.example.callrecorder.service.CallRecordingService

object RecordingServiceStarter {
    fun start(context: Context, mediaProjectionData: Intent? = null) {
        val intent = Intent(context, CallRecordingService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mediaProjectionData != null) {
            intent.putExtra("MEDIA_PROJECTION_DATA", mediaProjectionData)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }

    fun stop(context: Context) {
        val intent = Intent(context, CallRecordingService::class.java)
        context.stopService(intent)
    }
}
