Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktKapp/src/main/java/com/example/callrecorder/service/MediaProjectionHelper.ktRapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktIapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJapp/src/main/java/com/example/callrecorder/data/CallRecordingRepository.kt:app/src/main/java/com/example/callrecorder/MainActivity.kt;app/src/main/java/com/example/callrecorder/ui/MainScreen.ktJapp/src/main/java/com/example/callrecorder/util/RecordingServiceStarter.ktAapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktEapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.kt@app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktJapp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktBapp/src/main/java/com/example/callrecorder/domain/CallRecording.ktRapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktDapp/src/main/java/com/example/callrecorder/ui/PrivacyPolicyScreen.ktNapp/src/main/java/com/example/callrecorder/service/CallAccessibilityService.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                