<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="accessibility_service_description">Call Recorder Accessibility Service helps detect incoming and outgoing calls as a fallback method when other detection methods are not available. This service does not collect or transmit any personal data.</string>
    <string name="action_delete">Delete</string>
    <string name="action_pause">Pause</string>
    <string name="action_play">Play</string>
    <string name="action_retry">Retry</string>
    <string name="action_settings">Settings</string>
    <string name="action_share">Share</string>
    <string name="action_stop">Stop</string>
    <string name="action_transcribe">Transcribe</string>
    <string name="app_name">Call Recorder</string>
    <string name="dialog_delete_cancel">Cancel</string>
    <string name="dialog_delete_confirm">Delete</string>
    <string name="dialog_delete_message">Are you sure you want to delete this recording? This action cannot be undone.</string>
    <string name="dialog_delete_title">Delete Recording</string>
    <string name="error_file_not_found">Recording file not found. It may have been deleted.</string>
    <string name="error_network_failed">Network error. Check your internet connection.</string>
    <string name="error_permission_denied">Permission denied. Some features may not work properly.</string>
    <string name="error_playback_failed">Failed to play recording. File may be corrupted.</string>
    <string name="error_recording_failed">Failed to start recording. Please try again.</string>
    <string name="error_transcription_failed">Transcription failed. Please try again later.</string>
    <string name="notification_channel_description">Shows when call recording is active</string>
    <string name="notification_channel_name">Call Recording</string>
    <string name="notification_recording_text">Call recording in progress</string>
    <string name="notification_recording_title">Recording Call</string>
    <string name="permission_call_log_description">Required to get caller information and call details</string>
    <string name="permission_call_log_title">Call Log Access</string>
    <string name="permission_contacts_description">Required to display caller names instead of just numbers</string>
    <string name="permission_contacts_title">Contacts Access</string>
    <string name="permission_phone_state_description">Required to detect when calls start and end</string>
    <string name="permission_phone_state_title">Phone State Access</string>
    <string name="permission_record_audio_description">Required to record call audio</string>
    <string name="permission_record_audio_title">Microphone Access</string>
    <string name="share_recording_text">Sharing call recording from Call Recorder app</string>
    <string name="share_recording_title">Share Call Recording</string>
    <string name="status_ready">Ready</string>
    <string name="status_recording">Recording…</string>
    <string name="status_stopped">Stopped</string>
    <string name="status_transcribed">Transcribed</string>
    <string name="status_transcribing">Transcribing…</string>
    <style name="Theme.CallRecorder" parent="Theme.Material3.DayNight.NoActionBar">
        
    </style>
</resources>