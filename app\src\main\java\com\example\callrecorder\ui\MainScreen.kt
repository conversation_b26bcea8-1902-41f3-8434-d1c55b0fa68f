package com.example.callrecorder.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel
import com.example.callrecorder.ui.screens.PermissionScreen
import com.example.callrecorder.ui.screens.RecordingsListScreen
import com.example.callrecorder.ui.screens.RecordingControlScreen

@Composable
fun MainScreen() {
    val context = LocalContext.current
    val repository = remember { CallRecordingRepository(context) }
    val viewModel: CallRecordingsViewModel = viewModel(factory = object : androidx.lifecycle.ViewModelProvider.Factory {
        override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
            @Suppress("UNCHECKED_CAST")
            return CallRecordingsViewModel(repository) as T
        }
    })

    var permissionsGranted by remember { mutableStateOf(false) }
    var consentGiven by remember { mutableStateOf(false) }

    MaterialTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            when {
                !permissionsGranted -> {
                    PermissionScreen(
                        onPermissionsGranted = {
                            permissionsGranted = true
                        }
                    )
                }
                !consentGiven -> {
                    ConsentScreen(
                        onConsentGiven = {
                            consentGiven = true
                            viewModel.loadRecordings()
                        }
                    )
                }
                else -> {
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // Recording control section
                        RecordingControlScreen(
                            modifier = Modifier.fillMaxWidth()
                        )

                        Divider()

                        // Recordings list section
                        RecordingsListScreen(
                            viewModel = viewModel,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ConsentScreen(
    onConsentGiven: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Privacy Notice & Consent",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Text(
                    text = "This app will record phone calls for your personal use. " +
                            "Please ensure you comply with local laws regarding call recording. " +
                            "In many jurisdictions, you must inform all parties that the call is being recorded.",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    OutlinedButton(
                        onClick = { /* Handle decline */ }
                    ) {
                        Text("Decline")
                    }

                    Button(
                        onClick = onConsentGiven
                    ) {
                        Text("I Understand & Agree")
                    }
                }
            }
        }
    }
}
