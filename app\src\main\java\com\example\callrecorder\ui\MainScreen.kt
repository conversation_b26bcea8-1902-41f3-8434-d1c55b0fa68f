package com.example.callrecorder.ui

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.callrecorder.data.InMemoryCallRecordingRepository
import com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel

@Composable
fun MainScreen() {
    val repository = remember { InMemoryCallRecordingRepository() }
    val viewModel: CallRecordingsViewModel = viewModel(factory = object : androidx.lifecycle.ViewModelProvider.Factory {
        override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
            @Suppress("UNCHECKED_CAST")
            return CallRecordingsViewModel(repository) as T
        }
    })
    var permissionsGranted by remember { mutableStateOf(false) }

    PermissionAndConsentScreen(onPermissionsGranted = {
        permissionsGranted = true
        viewModel.loadRecordings()
    })

    if (permissionsGranted) {
        StartRecordingButton(onStart = {
            // TODO: Start CallRecordingService with MediaProjection if available
        })
        CallRecordingsListScreen(viewModel)
    }
}
