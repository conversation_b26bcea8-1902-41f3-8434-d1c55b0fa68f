com.example.callrecorder.app-jetified-lifecycle-runtime-compose-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.10\transforms\00edfec3e4c3756f1270fb729bb6eb13\transformed\jetified-lifecycle-runtime-compose-2.7.0\res
com.example.callrecorder.app-jetified-material-release-1 C:\Users\<USER>\.gradle\caches\8.10\transforms\024ed04d30f412b603f78a641454e7df\transformed\jetified-material-release\res
com.example.callrecorder.app-jetified-constraintlayout-compose-release-2 C:\Users\<USER>\.gradle\caches\8.10\transforms\04dcd93478568785e2fe2399acbfeb7b\transformed\jetified-constraintlayout-compose-release\res
com.example.callrecorder.app-coordinatorlayout-1.1.0-3 C:\Users\<USER>\.gradle\caches\8.10\transforms\087f22a6d2c84d6aacabbb00b143edf4\transformed\coordinatorlayout-1.1.0\res
com.example.callrecorder.app-jetified-customview-poolingcontainer-1.0.0-4 C:\Users\<USER>\.gradle\caches\8.10\transforms\0a1726711e317e7bdaa82d794c4597c3\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.example.callrecorder.app-jetified-foundation-layout-release-5 C:\Users\<USER>\.gradle\caches\8.10\transforms\0b15a12852f7ab2270bf3e1acbc9952f\transformed\jetified-foundation-layout-release\res
com.example.callrecorder.app-jetified-runtime-release-6 C:\Users\<USER>\.gradle\caches\8.10\transforms\0dc72252246cf1ece88e48a4c85d08f9\transformed\jetified-runtime-release\res
com.example.callrecorder.app-core-runtime-2.2.0-7 C:\Users\<USER>\.gradle\caches\8.10\transforms\0e29bf595e02081ba297284238420822\transformed\core-runtime-2.2.0\res
com.example.callrecorder.app-transition-1.2.0-8 C:\Users\<USER>\.gradle\caches\8.10\transforms\14ea4fbb813013b1290d6a0012e59ead\transformed\transition-1.2.0\res
com.example.callrecorder.app-jetified-animation-core-release-9 C:\Users\<USER>\.gradle\caches\8.10\transforms\18026ccf177b770611de475615964afc\transformed\jetified-animation-core-release\res
com.example.callrecorder.app-fragment-1.3.6-10 C:\Users\<USER>\.gradle\caches\8.10\transforms\1a1e6485fa667389bf301596aad53276\transformed\fragment-1.3.6\res
com.example.callrecorder.app-jetified-emoji2-views-helper-1.3.0-11 C:\Users\<USER>\.gradle\caches\8.10\transforms\1c0faa1b502066af25b6bc0063dc9ba1\transformed\jetified-emoji2-views-helper-1.3.0\res
com.example.callrecorder.app-drawerlayout-1.1.1-12 C:\Users\<USER>\.gradle\caches\8.10\transforms\1f1eb3fad1c5a7b30b780b63b085e730\transformed\drawerlayout-1.1.1\res
com.example.callrecorder.app-jetified-core-ktx-1.13.0-13 C:\Users\<USER>\.gradle\caches\8.10\transforms\203ee40ff80a01b21bb751d905487a95\transformed\jetified-core-ktx-1.13.0\res
com.example.callrecorder.app-jetified-profileinstaller-1.3.1-14 C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\res
com.example.callrecorder.app-sqlite-2.4.0-15 C:\Users\<USER>\.gradle\caches\8.10\transforms\23b17963b87f0b0f8e0f59ca997548b5\transformed\sqlite-2.4.0\res
com.example.callrecorder.app-jetified-lifecycle-livedata-core-ktx-2.7.0-16 C:\Users\<USER>\.gradle\caches\8.10\transforms\24a415696fb99ae483e32c50d9d9b920\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.callrecorder.app-jetified-ui-tooling-preview-release-17 C:\Users\<USER>\.gradle\caches\8.10\transforms\277a4823f3abdb862139558dc2022506\transformed\jetified-ui-tooling-preview-release\res
com.example.callrecorder.app-jetified-annotation-experimental-1.4.0-18 C:\Users\<USER>\.gradle\caches\8.10\transforms\27c6702847efbee43fa380332a187019\transformed\jetified-annotation-experimental-1.4.0\res
com.example.callrecorder.app-lifecycle-livedata-core-2.7.0-19 C:\Users\<USER>\.gradle\caches\8.10\transforms\28a138994d51d2cfdda9d8109ba87a41\transformed\lifecycle-livedata-core-2.7.0\res
com.example.callrecorder.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-20 C:\Users\<USER>\.gradle\caches\8.10\transforms\3372269eaa45fea798835e46d26c8aa7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.callrecorder.app-jetified-runtime-saveable-release-21 C:\Users\<USER>\.gradle\caches\8.10\transforms\3a92b880b0e0a518f0ce0ea506fd8762\transformed\jetified-runtime-saveable-release\res
com.example.callrecorder.app-jetified-foundation-release-22 C:\Users\<USER>\.gradle\caches\8.10\transforms\4732a0661345d7666aa559ef67f47092\transformed\jetified-foundation-release\res
com.example.callrecorder.app-jetified-animation-release-23 C:\Users\<USER>\.gradle\caches\8.10\transforms\592e4287f6f7d7a00050272a6ef06307\transformed\jetified-animation-release\res
com.example.callrecorder.app-jetified-material-ripple-release-24 C:\Users\<USER>\.gradle\caches\8.10\transforms\5c57cda1d8ff7d6bb2cf72738f1ad6fd\transformed\jetified-material-ripple-release\res
com.example.callrecorder.app-lifecycle-livedata-2.7.0-25 C:\Users\<USER>\.gradle\caches\8.10\transforms\5c7747767b52f9c62679736508bfe988\transformed\lifecycle-livedata-2.7.0\res
com.example.callrecorder.app-jetified-navigation-compose-2.7.7-26 C:\Users\<USER>\.gradle\caches\8.10\transforms\6a328adb251609b0a25c3e2722fc4c91\transformed\jetified-navigation-compose-2.7.7\res
com.example.callrecorder.app-recyclerview-1.1.0-27 C:\Users\<USER>\.gradle\caches\8.10\transforms\6c6dad1bc3fc36f582301d53e61490cf\transformed\recyclerview-1.1.0\res
com.example.callrecorder.app-jetified-lifecycle-viewmodel-compose-2.7.0-28 C:\Users\<USER>\.gradle\caches\8.10\transforms\6ec2aa96b84baf41774004ee5b446661\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\res
com.example.callrecorder.app-jetified-ui-unit-release-29 C:\Users\<USER>\.gradle\caches\8.10\transforms\74312a8f365a5b6834204ab8a7e4415d\transformed\jetified-ui-unit-release\res
com.example.callrecorder.app-jetified-savedstate-ktx-1.2.1-30 C:\Users\<USER>\.gradle\caches\8.10\transforms\773ab555ec04fc00a9404af776f65324\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.callrecorder.app-jetified-activity-1.9.0-31 C:\Users\<USER>\.gradle\caches\8.10\transforms\83426123506a9f00085f2a3d0abb887c\transformed\jetified-activity-1.9.0\res
com.example.callrecorder.app-jetified-savedstate-1.2.1-32 C:\Users\<USER>\.gradle\caches\8.10\transforms\851300842e4c4bc1868a18752108c809\transformed\jetified-savedstate-1.2.1\res
com.example.callrecorder.app-jetified-ui-text-release-33 C:\Users\<USER>\.gradle\caches\8.10\transforms\8b80ce3ea448fe64042c2ec19d868381\transformed\jetified-ui-text-release\res
com.example.callrecorder.app-jetified-ui-geometry-release-34 C:\Users\<USER>\.gradle\caches\8.10\transforms\8b966d6ff224f3f10650495ce3dff1ad\transformed\jetified-ui-geometry-release\res
com.example.callrecorder.app-jetified-ui-graphics-release-35 C:\Users\<USER>\.gradle\caches\8.10\transforms\8bdfd49a2a1a91d0dcef2081465e8ee5\transformed\jetified-ui-graphics-release\res
com.example.callrecorder.app-lifecycle-viewmodel-2.7.0-36 C:\Users\<USER>\.gradle\caches\8.10\transforms\93e355649c5cd71e96940de0a2295a61\transformed\lifecycle-viewmodel-2.7.0\res
com.example.callrecorder.app-lifecycle-runtime-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.10\transforms\94ca1f3a5b853c4919c3f9574ef1a3b0\transformed\lifecycle-runtime-2.7.0\res
com.example.callrecorder.app-jetified-lifecycle-livedata-ktx-2.7.0-38 C:\Users\<USER>\.gradle\caches\8.10\transforms\9e7f7d011f16540aebcb51879cc7dd62\transformed\jetified-lifecycle-livedata-ktx-2.7.0\res
com.example.callrecorder.app-jetified-ui-util-release-39 C:\Users\<USER>\.gradle\caches\8.10\transforms\a4a1a2591784a6d537d4996287664fc0\transformed\jetified-ui-util-release\res
com.example.callrecorder.app-jetified-ui-release-40 C:\Users\<USER>\.gradle\caches\8.10\transforms\a8ffa01484e5d0b2260db025a53ae0cf\transformed\jetified-ui-release\res
com.example.callrecorder.app-jetified-lifecycle-runtime-ktx-2.7.0-41 C:\Users\<USER>\.gradle\caches\8.10\transforms\ac911541b54f69b5439f11267356d8a6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.callrecorder.app-sqlite-framework-2.4.0-42 C:\Users\<USER>\.gradle\caches\8.10\transforms\acdd455cd6a36b15552f687e179716f4\transformed\sqlite-framework-2.4.0\res
com.example.callrecorder.app-jetified-activity-ktx-1.9.0-43 C:\Users\<USER>\.gradle\caches\8.10\transforms\add3946acdc6e9e6fd979649704e79d9\transformed\jetified-activity-ktx-1.9.0\res
com.example.callrecorder.app-core-1.13.0-44 C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\res
com.example.callrecorder.app-navigation-runtime-ktx-2.7.7-45 C:\Users\<USER>\.gradle\caches\8.10\transforms\ae27a955873c3325260ee5c5d3200b94\transformed\navigation-runtime-ktx-2.7.7\res
com.example.callrecorder.app-jetified-appcompat-resources-1.6.1-46 C:\Users\<USER>\.gradle\caches\8.10\transforms\b7d33c864b7bb128bdefcc5a6ca29856\transformed\jetified-appcompat-resources-1.6.1\res
com.example.callrecorder.app-media-1.7.0-47 C:\Users\<USER>\.gradle\caches\8.10\transforms\c08fe4abe250583505a7f1d81de1f38f\transformed\media-1.7.0\res
com.example.callrecorder.app-jetified-room-ktx-2.6.1-48 C:\Users\<USER>\.gradle\caches\8.10\transforms\c29f333d4ee4537c685f5f165879c21f\transformed\jetified-room-ktx-2.6.1\res
com.example.callrecorder.app-jetified-activity-compose-1.9.0-49 C:\Users\<USER>\.gradle\caches\8.10\transforms\c950e91bf8e4335fbe5da69159f4f7b5\transformed\jetified-activity-compose-1.9.0\res
com.example.callrecorder.app-jetified-emoji2-1.3.0-50 C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\res
com.example.callrecorder.app-material-1.11.0-51 C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\res
com.example.callrecorder.app-navigation-runtime-2.7.7-52 C:\Users\<USER>\.gradle\caches\8.10\transforms\daf4f9cd6fb4c668a14c72918564b691\transformed\navigation-runtime-2.7.7\res
com.example.callrecorder.app-navigation-common-ktx-2.7.7-53 C:\Users\<USER>\.gradle\caches\8.10\transforms\ddc0f7b1d36315427b3b94245daed209\transformed\navigation-common-ktx-2.7.7\res
com.example.callrecorder.app-room-runtime-2.6.1-54 C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\res
com.example.callrecorder.app-jetified-lifecycle-viewmodel-ktx-2.7.0-55 C:\Users\<USER>\.gradle\caches\8.10\transforms\e58cca23a5c8ba8f7bc63535eec5691a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.callrecorder.app-jetified-viewpager2-1.0.0-56 C:\Users\<USER>\.gradle\caches\8.10\transforms\eba5d52d81a090156fc480df4b436622\transformed\jetified-viewpager2-1.0.0\res
com.example.callrecorder.app-jetified-material-icons-core-release-57 C:\Users\<USER>\.gradle\caches\8.10\transforms\ed3af94b8739c3888456890e53de0ec9\transformed\jetified-material-icons-core-release\res
com.example.callrecorder.app-jetified-startup-runtime-1.1.1-58 C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\res
com.example.callrecorder.app-jetified-material3-release-59 C:\Users\<USER>\.gradle\caches\8.10\transforms\f481f24d3159e23b2c7b2fc7d3454301\transformed\jetified-material3-release\res
com.example.callrecorder.app-cardview-1.0.0-60 C:\Users\<USER>\.gradle\caches\8.10\transforms\f5b6a837ec86c67337f23a14a0f91bf4\transformed\cardview-1.0.0\res
com.example.callrecorder.app-navigation-common-2.7.7-61 C:\Users\<USER>\.gradle\caches\8.10\transforms\fb97f9c2319f7a9720c0cc20f156d813\transformed\navigation-common-2.7.7\res
com.example.callrecorder.app-appcompat-1.6.1-62 C:\Users\<USER>\.gradle\caches\8.10\transforms\fba63af1cc2824d2492e5346a43ea817\transformed\appcompat-1.6.1\res
com.example.callrecorder.app-constraintlayout-2.0.1-63 C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\res
com.example.callrecorder.app-jetified-lifecycle-process-2.7.0-64 C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\res
com.example.callrecorder.app-pngs-65 C:\Users\<USER>\AndroidStudioProjects\Call record\app\build\generated\res\pngs\release
com.example.callrecorder.app-resValues-66 C:\Users\<USER>\AndroidStudioProjects\Call record\app\build\generated\res\resValues\release
com.example.callrecorder.app-packageReleaseResources-67 C:\Users\<USER>\AndroidStudioProjects\Call record\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.example.callrecorder.app-packageReleaseResources-68 C:\Users\<USER>\AndroidStudioProjects\Call record\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.example.callrecorder.app-release-69 C:\Users\<USER>\AndroidStudioProjects\Call record\app\build\intermediates\merged_res\release\mergeReleaseResources
com.example.callrecorder.app-main-70 C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\res
com.example.callrecorder.app-release-71 C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\release\res
