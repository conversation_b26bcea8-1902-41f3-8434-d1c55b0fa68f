package com.example.callrecorder.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.callrecorder.R
import com.example.callrecorder.data.CallRecording
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.data.CallType
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * Foreground service for call recording using MediaProjection API
 *
 * Android Version Limitations:
 * - Android 10+ (API 29+): Cannot use AudioSource.VOICE_CALL, must use MediaProjection
 * - Android 11+ (API 30+): Additional privacy restrictions on audio capture
 * - Android 12+ (API 31+): Stricter background service limitations
 * - Android 13+ (API 33+): Runtime notification permission required
 * - Android 14+ (API 34+): Enhanced foreground service type restrictions
 */
class CallRecordingService : Service() {

    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "call_recording_channel"
        const val ACTION_START_RECORDING = "start_recording"
        const val ACTION_STOP_RECORDING = "stop_recording"
        const val EXTRA_MEDIA_PROJECTION_DATA = "media_projection_data"
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_CALL_TYPE = "call_type"
        const val EXTRA_CONTACT_NAME = "contact_name"
    }

    private var mediaProjection: MediaProjection? = null
    private var audioRecord: AudioRecord? = null
    private var isRecording = false
    private var recordingJob: Job? = null
    private var wakeLock: PowerManager.WakeLock? = null
    private var currentRecordingFile: File? = null
    private var recordingStartTime: Long = 0

    private lateinit var repository: CallRecordingRepository
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun onCreate() {
        super.onCreate()
        repository = CallRecordingRepository(this)
        createNotificationChannel()
        acquireWakeLock()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_RECORDING -> {
                val mediaProjectionData = intent.getParcelableExtra<Intent>(EXTRA_MEDIA_PROJECTION_DATA)
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: "Unknown"
                val callTypeString = intent.getStringExtra(EXTRA_CALL_TYPE) ?: "INCOMING"
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                val callType = CallType.valueOf(callTypeString)

                startRecording(mediaProjectionData, phoneNumber, callType, contactName)
            }
            ACTION_STOP_RECORDING -> {
                stopRecording()
            }
        }

        return START_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Call Recording Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows when call recording is active"
                setSound(null, null)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "CallRecorder::RecordingWakeLock"
        )
        wakeLock?.acquire(10 * 60 * 1000L) // 10 minutes max
    }

    private fun startRecording(
        mediaProjectionData: Intent?,
        phoneNumber: String,
        callType: CallType,
        contactName: String?
    ) {
        if (isRecording) return

        try {
            // Initialize MediaProjection for Android 10+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mediaProjectionData != null) {
                val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
                mediaProjection = mediaProjectionManager.getMediaProjection(Activity.RESULT_OK, mediaProjectionData)
            }

            // Create recording file
            currentRecordingFile = createRecordingFile(phoneNumber, callType)
            recordingStartTime = System.currentTimeMillis()

            // Start foreground service with notification
            startForeground(NOTIFICATION_ID, createRecordingNotification(phoneNumber, callType))

            // Start actual recording (works even without MediaProjection on older Android versions)
            startAudioRecording()

            isRecording = true

            Log.d("CallRecordingService", "Recording started for $phoneNumber")

        } catch (e: Exception) {
            Log.e("CallRecordingService", "Failed to start recording", e)
            stopSelf()
        }
    }

    private fun createRecordingFile(phoneNumber: String, callType: CallType): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val callTypePrefix = when (callType) {
            CallType.INCOMING -> "IN"
            CallType.OUTGOING -> "OUT"
            CallType.MISSED -> "MISSED"
        }
        val fileName = "${callTypePrefix}_${phoneNumber}_$timestamp.m4a"

        // Use app-specific external storage (no permission required for API 19+)
        val recordingsDir = File(getExternalFilesDir(null), "recordings")
        if (!recordingsDir.exists()) {
            recordingsDir.mkdirs()
        }

        return File(recordingsDir, fileName)
    }

    private fun createRecordingNotification(phoneNumber: String, callType: CallType): Notification {
        val callTypeText = when (callType) {
            CallType.INCOMING -> "Incoming"
            CallType.OUTGOING -> "Outgoing"
            CallType.MISSED -> "Missed"
        }

        val stopIntent = Intent(this, CallRecordingService::class.java).apply {
            action = ACTION_STOP_RECORDING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Recording $callTypeText Call")
            .setContentText("Recording call with $phoneNumber")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setOngoing(true)
            .addAction(R.mipmap.ic_launcher, "Stop", stopPendingIntent)
            .build()
    }

    private fun startAudioRecording() {
        recordingJob = serviceScope.launch {
            try {
                val sampleRate = 44100
                val channelConfig = AudioFormat.CHANNEL_IN_MONO
                val audioFormat = AudioFormat.ENCODING_PCM_16BIT
                val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

                // For Android 10+, try to use MediaProjection audio capture
                val audioSource = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mediaProjection != null) {
                    // Note: MediaProjection audio capture is complex and may not capture call audio
                    // This is a simplified implementation
                    MediaRecorder.AudioSource.MIC
                } else {
                    MediaRecorder.AudioSource.MIC
                }

                audioRecord = AudioRecord(audioSource, sampleRate, channelConfig, audioFormat, bufferSize)

                if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                    throw IllegalStateException("AudioRecord not initialized")
                }

                audioRecord?.startRecording()

                val buffer = ByteArray(bufferSize)
                val outputStream = FileOutputStream(currentRecordingFile)

                while (isRecording && !Thread.currentThread().isInterrupted) {
                    val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                    if (bytesRead > 0) {
                        outputStream.write(buffer, 0, bytesRead)
                    }
                }

                outputStream.close()

            } catch (e: Exception) {
                e.printStackTrace()
                stopRecording()
            }
        }
    }

    private fun stopRecording() {
        if (!isRecording) return

        isRecording = false
        recordingJob?.cancel()

        try {
            audioRecord?.apply {
                if (state == AudioRecord.STATE_INITIALIZED) {
                    stop()
                }
                release()
            }
            audioRecord = null

            mediaProjection?.stop()
            mediaProjection = null

            // Save recording to database
            currentRecordingFile?.let { file ->
                if (file.exists() && file.length() > 0) {
                    saveRecordingToDatabase(file)
                } else {
                    file.delete() // Delete empty file
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            stopForeground(true)
            stopSelf()
        }
    }

    private fun saveRecordingToDatabase(file: File) {
        serviceScope.launch {
            try {
                val recording = CallRecording(
                    phoneNumber = "Unknown", // Will be updated by caller
                    callType = CallType.INCOMING, // Will be updated by caller
                    callDate = Date(recordingStartTime),
                    duration = System.currentTimeMillis() - recordingStartTime,
                    filePath = file.absolutePath,
                    fileSize = file.length(),
                    recordingDuration = System.currentTimeMillis() - recordingStartTime
                )

                repository.insertRecording(recording)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopRecording()

        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }

        serviceScope.cancel()
    }
}
