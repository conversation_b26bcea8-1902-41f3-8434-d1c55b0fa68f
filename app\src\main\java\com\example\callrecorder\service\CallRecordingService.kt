package com.example.callrecorder.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.callrecorder.R
import com.example.callrecorder.data.CallRecording
import com.example.callrecorder.data.CallRecordingRepository
import com.example.callrecorder.data.CallType
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * Foreground service for call recording using MediaProjection API
 *
 * Android Version Limitations:
 * - Android 10+ (API 29+): Cannot use AudioSource.VOICE_CALL, must use MediaProjection
 * - Android 11+ (API 30+): Additional privacy restrictions on audio capture
 * - Android 12+ (API 31+): Stricter background service limitations
 * - Android 13+ (API 33+): Runtime notification permission required
 * - Android 14+ (API 34+): Enhanced foreground service type restrictions
 */
class CallRecordingService : Service() {

    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "call_recording_channel"
        const val ACTION_START_RECORDING = "start_recording"
        const val ACTION_STOP_RECORDING = "stop_recording"
        const val EXTRA_MEDIA_PROJECTION_DATA = "media_projection_data"
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_CALL_TYPE = "call_type"
        const val EXTRA_CONTACT_NAME = "contact_name"
    }

    private var mediaProjection: MediaProjection? = null
    private var audioRecord: AudioRecord? = null
    private var isRecording = false
    private var recordingJob: Job? = null
    private var wakeLock: PowerManager.WakeLock? = null
    private var currentRecordingFile: File? = null
    private var recordingStartTime: Long = 0

    private lateinit var repository: CallRecordingRepository
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun onCreate() {
        super.onCreate()
        repository = CallRecordingRepository(this)
        createNotificationChannel()
        acquireWakeLock()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_RECORDING -> {
                val mediaProjectionData = intent.getParcelableExtra<Intent>(EXTRA_MEDIA_PROJECTION_DATA)
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: "Unknown"
                val callTypeString = intent.getStringExtra(EXTRA_CALL_TYPE) ?: "INCOMING"
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                val callType = CallType.valueOf(callTypeString)

                startRecording(mediaProjectionData, phoneNumber, callType, contactName)
            }
            ACTION_STOP_RECORDING -> {
                stopRecording()
            }
        }

        return START_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Call Recording Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows when call recording is active"
                setSound(null, null)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "CallRecorder::RecordingWakeLock"
        )
        wakeLock?.acquire(10 * 60 * 1000L) // 10 minutes max
    }

    private fun startRecording(
        mediaProjectionData: Intent?,
        phoneNumber: String,
        callType: CallType,
        contactName: String?
    ) {
        if (isRecording) return

        try {
            // Initialize MediaProjection for Android 10+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mediaProjectionData != null) {
                val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
                mediaProjection = mediaProjectionManager.getMediaProjection(Activity.RESULT_OK, mediaProjectionData)
            }

            // Create recording file
            currentRecordingFile = createRecordingFile(phoneNumber, callType)
            recordingStartTime = System.currentTimeMillis()

            // Start foreground service with notification
            startForeground(NOTIFICATION_ID, createRecordingNotification(phoneNumber, callType))

            // Start actual recording with VoIP support
            startAudioRecording(phoneNumber.contains("VoIP"))

            isRecording = true

            Log.d("CallRecordingService", "Recording started for $phoneNumber")

        } catch (e: Exception) {
            Log.e("CallRecordingService", "Failed to start recording", e)
            stopSelf()
        }
    }

    private fun createRecordingFile(phoneNumber: String, callType: CallType): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val dateFolder = SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(Date())

        // Detect if it's a VoIP call
        val isVoIPCall = phoneNumber.startsWith("WhatsApp_") || phoneNumber.contains("VoIP")

        val callTypePrefix = when (callType) {
            CallType.INCOMING -> if (isVoIPCall) "WHATSAPP_IN" else "IN"
            CallType.OUTGOING -> if (isVoIPCall) "WHATSAPP_OUT" else "OUT"
            CallType.MISSED -> if (isVoIPCall) "WHATSAPP_MISSED" else "MISSED"
        }

        // Enhanced file naming for WhatsApp calls
        val sanitizedNumber = if (isVoIPCall) {
            if (phoneNumber.startsWith("WhatsApp_")) {
                "WhatsApp_Call_${phoneNumber.substringAfter("WhatsApp_")}"
            } else {
                "VoIP_Call"
            }
        } else {
            phoneNumber.replace(Regex("[^\\w\\d+]"), "_")
        }

        val fileName = "${callTypePrefix}_${sanitizedNumber}_${timestamp}.wav"

        // Create organized folder structure
        val recordingsDir = File(getExternalFilesDir(null), "CallRecordings")
        val monthDir = File(recordingsDir, dateFolder)
        val typeDir = File(monthDir, if (isVoIPCall) "WhatsApp" else "Phone")

        // Ensure directories exist
        if (!typeDir.exists()) {
            typeDir.mkdirs()
            Log.d("CallRecordingService", "Created directory: ${typeDir.absolutePath}")
        }

        val file = File(typeDir, fileName)
        Log.d("CallRecordingService", "Recording file created: ${file.absolutePath}")

        return file
    }

    private fun createRecordingNotification(phoneNumber: String, callType: CallType): Notification {
        val callTypeText = when (callType) {
            CallType.INCOMING -> "Incoming"
            CallType.OUTGOING -> "Outgoing"
            CallType.MISSED -> "Missed"
        }

        val stopIntent = Intent(this, CallRecordingService::class.java).apply {
            action = ACTION_STOP_RECORDING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Recording $callTypeText Call")
            .setContentText("Recording call with $phoneNumber")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setOngoing(true)
            .addAction(R.mipmap.ic_launcher, "Stop", stopPendingIntent)
            .build()
    }

    private fun startAudioRecording(isVoIPCall: Boolean = false) {
        recordingJob = serviceScope.launch {
            try {
                val sampleRate = 44100
                val channelConfig = AudioFormat.CHANNEL_IN_MONO
                val audioFormat = AudioFormat.ENCODING_PCM_16BIT
                val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

                // Enhanced audio source selection for VoIP calls
                val audioSources = if (isVoIPCall) {
                    // Priority list for VoIP calls
                    listOf(
                        MediaRecorder.AudioSource.VOICE_COMMUNICATION,
                        MediaRecorder.AudioSource.MIC,
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) MediaRecorder.AudioSource.UNPROCESSED else MediaRecorder.AudioSource.MIC,
                        MediaRecorder.AudioSource.CAMCORDER
                    )
                } else {
                    // Priority list for regular calls
                    listOf(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) MediaRecorder.AudioSource.VOICE_COMMUNICATION else MediaRecorder.AudioSource.MIC,
                        MediaRecorder.AudioSource.MIC,
                        MediaRecorder.AudioSource.VOICE_CALL
                    )
                }

                var audioRecord: AudioRecord? = null
                var selectedAudioSource = MediaRecorder.AudioSource.MIC

                // Try each audio source until one works
                for (audioSource in audioSources) {
                    try {
                        val testRecord = AudioRecord(audioSource, sampleRate, channelConfig, audioFormat, bufferSize)
                        if (testRecord.state == AudioRecord.STATE_INITIALIZED) {
                            audioRecord = testRecord
                            selectedAudioSource = audioSource
                            Log.d("CallRecordingService", "Successfully initialized audio source: $audioSource for VoIP: $isVoIPCall")
                            break
                        } else {
                            testRecord.release()
                        }
                    } catch (e: Exception) {
                        Log.w("CallRecordingService", "Failed to initialize audio source $audioSource: ${e.message}")
                    }
                }

                if (audioRecord == null) {
                    throw IllegalStateException("Could not initialize any audio source")
                }

                <EMAIL> = audioRecord

                // Start recording with enhanced error handling
                try {
                    audioRecord.startRecording()
                    Log.d("CallRecordingService", "Audio recording started with source: $selectedAudioSource")
                } catch (e: Exception) {
                    Log.e("CallRecordingService", "Failed to start recording", e)
                    throw e
                }

                val buffer = ByteArray(bufferSize)
                val outputStream = FileOutputStream(currentRecordingFile)

                // Add WAV header for better compatibility
                writeWavHeader(outputStream, sampleRate, channelConfig, audioFormat)

                var totalBytesWritten = 0
                var silentSamples = 0
                val maxSilentSamples = sampleRate * 2 // 2 seconds of silence threshold

                while (isRecording && !Thread.currentThread().isInterrupted) {
                    val bytesRead = audioRecord.read(buffer, 0, buffer.size)

                    if (bytesRead > 0) {
                        outputStream.write(buffer, 0, bytesRead)
                        totalBytesWritten += bytesRead

                        // Check for audio activity (simple volume detection)
                        val hasAudio = buffer.any { Math.abs(it.toInt()) > 10 }
                        if (hasAudio) {
                            silentSamples = 0
                        } else {
                            silentSamples += bytesRead / 2 // 16-bit samples
                        }

                        // Log progress every 5 seconds
                        if (totalBytesWritten % (sampleRate * 10) == 0) {
                            Log.d("CallRecordingService", "Recording progress: ${totalBytesWritten / 1024}KB")
                        }
                    } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                        Log.e("CallRecordingService", "AudioRecord invalid operation")
                        break
                    }
                }

                // Update WAV header with actual data size
                updateWavHeader(outputStream, totalBytesWritten)
                outputStream.close()

                Log.d("CallRecordingService", "Recording completed. Total bytes: $totalBytesWritten, Silent samples: $silentSamples")

            } catch (e: Exception) {
                e.printStackTrace()
                stopRecording()
            }
        }
    }

    /**
     * Write WAV file header for better audio compatibility
     */
    private fun writeWavHeader(outputStream: FileOutputStream, sampleRate: Int, channelConfig: Int, audioFormat: Int) {
        val channels = if (channelConfig == AudioFormat.CHANNEL_IN_MONO) 1 else 2
        val bitsPerSample = if (audioFormat == AudioFormat.ENCODING_PCM_16BIT) 16 else 8
        val byteRate = sampleRate * channels * bitsPerSample / 8

        val header = ByteArray(44)

        // RIFF header
        header[0] = 'R'.code.toByte()
        header[1] = 'I'.code.toByte()
        header[2] = 'F'.code.toByte()
        header[3] = 'F'.code.toByte()

        // File size (will be updated later)
        header[4] = 0
        header[5] = 0
        header[6] = 0
        header[7] = 0

        // WAVE header
        header[8] = 'W'.code.toByte()
        header[9] = 'A'.code.toByte()
        header[10] = 'V'.code.toByte()
        header[11] = 'E'.code.toByte()

        // fmt subchunk
        header[12] = 'f'.code.toByte()
        header[13] = 'm'.code.toByte()
        header[14] = 't'.code.toByte()
        header[15] = ' '.code.toByte()

        // Subchunk1Size (16 for PCM)
        header[16] = 16
        header[17] = 0
        header[18] = 0
        header[19] = 0

        // AudioFormat (1 for PCM)
        header[20] = 1
        header[21] = 0

        // NumChannels
        header[22] = channels.toByte()
        header[23] = 0

        // SampleRate
        header[24] = (sampleRate and 0xff).toByte()
        header[25] = ((sampleRate shr 8) and 0xff).toByte()
        header[26] = ((sampleRate shr 16) and 0xff).toByte()
        header[27] = ((sampleRate shr 24) and 0xff).toByte()

        // ByteRate
        header[28] = (byteRate and 0xff).toByte()
        header[29] = ((byteRate shr 8) and 0xff).toByte()
        header[30] = ((byteRate shr 16) and 0xff).toByte()
        header[31] = ((byteRate shr 24) and 0xff).toByte()

        // BlockAlign
        val blockAlign = channels * bitsPerSample / 8
        header[32] = blockAlign.toByte()
        header[33] = 0

        // BitsPerSample
        header[34] = bitsPerSample.toByte()
        header[35] = 0

        // data subchunk
        header[36] = 'd'.code.toByte()
        header[37] = 'a'.code.toByte()
        header[38] = 't'.code.toByte()
        header[39] = 'a'.code.toByte()

        // Subchunk2Size (will be updated later)
        header[40] = 0
        header[41] = 0
        header[42] = 0
        header[43] = 0

        outputStream.write(header)
    }

    /**
     * Update WAV header with actual file size
     */
    private fun updateWavHeader(outputStream: FileOutputStream, dataSize: Int) {
        try {
            val fileSize = dataSize + 36
            val channel = outputStream.channel

            // Update file size
            channel.position(4)
            val fileSizeBytes = ByteArray(4)
            fileSizeBytes[0] = (fileSize and 0xff).toByte()
            fileSizeBytes[1] = ((fileSize shr 8) and 0xff).toByte()
            fileSizeBytes[2] = ((fileSize shr 16) and 0xff).toByte()
            fileSizeBytes[3] = ((fileSize shr 24) and 0xff).toByte()
            channel.write(java.nio.ByteBuffer.wrap(fileSizeBytes))

            // Update data size
            channel.position(40)
            val dataSizeBytes = ByteArray(4)
            dataSizeBytes[0] = (dataSize and 0xff).toByte()
            dataSizeBytes[1] = ((dataSize shr 8) and 0xff).toByte()
            dataSizeBytes[2] = ((dataSize shr 16) and 0xff).toByte()
            dataSizeBytes[3] = ((dataSize shr 24) and 0xff).toByte()
            channel.write(java.nio.ByteBuffer.wrap(dataSizeBytes))

        } catch (e: Exception) {
            Log.w("CallRecordingService", "Failed to update WAV header: ${e.message}")
        }
    }

    private fun stopRecording() {
        if (!isRecording) return

        isRecording = false
        recordingJob?.cancel()

        try {
            audioRecord?.apply {
                if (state == AudioRecord.STATE_INITIALIZED) {
                    stop()
                }
                release()
            }
            audioRecord = null

            mediaProjection?.stop()
            mediaProjection = null

            // Save recording to database
            currentRecordingFile?.let { file ->
                if (file.exists() && file.length() > 0) {
                    saveRecordingToDatabase(file)
                } else {
                    file.delete() // Delete empty file
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            stopForeground(true)
            stopSelf()
        }
    }

    private fun saveRecordingToDatabase(file: File) {
        serviceScope.launch {
            try {
                val recording = CallRecording(
                    phoneNumber = "Unknown", // Will be updated by caller
                    callType = CallType.INCOMING, // Will be updated by caller
                    callDate = Date(recordingStartTime),
                    duration = System.currentTimeMillis() - recordingStartTime,
                    filePath = file.absolutePath,
                    fileSize = file.length(),
                    recordingDuration = System.currentTimeMillis() - recordingStartTime
                )

                repository.insertRecording(recording)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopRecording()

        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }

        serviceScope.cancel()
    }
}
