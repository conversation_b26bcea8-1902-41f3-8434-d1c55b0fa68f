package com.example.callrecorder.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.MediaRecorder
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.example.callrecorder.R
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Foreground service for call recording using MediaProjection (Android 10+) or fallback.
 * Note: MediaProjection can only record device audio, not always call audio due to platform limitations.
 * AccessibilityService can be used for VoIP or UI automation, but not for native call audio.
 * Always inform the user and get consent before recording.
 */
class CallRecordingService : Service() {
    private var recorder: MediaRecorder? = null
    private var outputFile: String? = null
    private var isRecording = false

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForegroundService()
        val mediaProjectionData = intent?.getParcelableExtra<Intent>("MEDIA_PROJECTION_DATA")
        startRecording(mediaProjectionData)
        return START_STICKY
    }

    private fun startForegroundService() {
        val channelId = "call_recording_channel"
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Call Recording",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
        val notification: Notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle("Call Recording")
            .setContentText("Recording in progress...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
        startForeground(1, notification)
    }

    private fun startRecording(mediaProjectionData: Intent?) {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(Date())
        val fileName = "CallRecord_$timestamp.m4a"
        val file = File(getExternalFilesDir(null), fileName)
        outputFile = file.absolutePath
        recorder = MediaRecorder().apply {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && mediaProjectionData != null) {
                // TODO: Use MediaProjection to capture device audio (requires additional setup)
                setAudioSource(MediaRecorder.AudioSource.MIC) // Placeholder: replace with REMOTE_SUBMIX if available
            } else {
                setAudioSource(MediaRecorder.AudioSource.MIC)
            }
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            setAudioEncodingBitRate(128000)
            setAudioSamplingRate(44100)
            setOutputFile(outputFile)
            try {
                prepare()
                start()
                isRecording = true
            } catch (e: Exception) {
                e.printStackTrace()
                stopSelf()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            if (isRecording) {
                recorder?.apply {
                    stop()
                    release()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        recorder = null
        isRecording = false
    }
}
