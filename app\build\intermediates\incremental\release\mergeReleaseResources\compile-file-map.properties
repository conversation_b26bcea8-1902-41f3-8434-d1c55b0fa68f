#Sun Jul 20 20:21:37 BDT 2025
com.example.callrecorder.app-main-70\:/mipmap/ic_launcher_round.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap_ic_launcher_round.xml.flat
com.example.callrecorder.app-main-70\:/xml/file_paths.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_file_paths.xml.flat
com.example.callrecorder.app-main-69\:/xml/accessibility_service_config.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_accessibility_service_config.xml.flat
com.example.callrecorder.app-main-70\:/xml/accessibility_service_config.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_accessibility_service_config.xml.flat
com.example.callrecorder.app-main-69\:/xml/file_paths.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_file_paths.xml.flat
com.example.callrecorder.app-main-69\:/mipmap/ic_launcher.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap_ic_launcher.xml.flat
com.example.callrecorder.app-main-70\:/mipmap/ic_launcher.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap_ic_launcher.xml.flat
com.example.callrecorder.app-main-69\:/mipmap/ic_launcher_round.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap_ic_launcher_round.xml.flat
