package com.example.callrecorder.service

import android.accessibilityservice.AccessibilityService
import android.view.accessibility.AccessibilityEvent

/**
 * AccessibilityService for call recording triggers (for VoIP or UI automation only).
 * Note: Cannot capture native call audio, but can be used to detect call state or automate MediaProjection.
 * Always inform the user and get consent before recording.
 */
class CallAccessibilityService : AccessibilityService() {
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // TODO: Implement logic to detect call state changes for VoIP apps
    }

    override fun onInterrupt() {}
}
