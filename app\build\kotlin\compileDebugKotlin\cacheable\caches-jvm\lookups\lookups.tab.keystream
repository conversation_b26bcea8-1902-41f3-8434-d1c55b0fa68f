  Manifest android  
permission android.Manifest  FOREGROUND_SERVICE android.Manifest.permission  
READ_CALL_LOG android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  AccessibilityService android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  Activity android.app  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  Bundle android.app.Activity  CallRecorderApp android.app.Activity  	RESULT_OK android.app.Activity  finish android.app.Activity  getSystemService android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  AccessibilityEvent android.app.Service  Build android.app.Service  Context android.app.Service  Date android.app.Service  	Exception android.app.Service  File android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  Locale android.app.Service  
MediaRecorder android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  R android.app.Service  START_STICKY android.app.Service  SimpleDateFormat android.app.Service  String android.app.Service  apply android.app.Service  getExternalFilesDir android.app.Service  getSystemService android.app.Service  isRecording android.app.Service  	onDestroy android.app.Service  
outputFile android.app.Service  startForeground android.app.Service  startForegroundService android.app.Service  startRecording android.app.Service  stopSelf android.app.Service  
ComponentName android.content  Context android.content  Intent android.content  AccessibilityEvent android.content.Context  Build android.content.Context  Bundle android.content.Context  CallRecorderApp android.content.Context  Context android.content.Context  Date android.content.Context  	Exception android.content.Context  File android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  Locale android.content.Context  MEDIA_PROJECTION_SERVICE android.content.Context  
MediaRecorder android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  R android.content.Context  START_STICKY android.content.Context  SimpleDateFormat android.content.Context  String android.content.Context  apply android.content.Context  finish android.content.Context  getExternalFilesDir android.content.Context  getSystemService android.content.Context  isRecording android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  
outputFile android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startRecording android.content.Context  startService android.content.Context  stopSelf android.content.Context  stopService android.content.Context  AccessibilityEvent android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  CallRecorderApp android.content.ContextWrapper  Context android.content.ContextWrapper  Date android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Locale android.content.ContextWrapper  
MediaRecorder android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  R android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  String android.content.ContextWrapper  apply android.content.ContextWrapper  finish android.content.ContextWrapper  getExternalFilesDir android.content.ContextWrapper  getSystemService android.content.ContextWrapper  isRecording android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  
outputFile android.content.ContextWrapper  
setContent android.content.ContextWrapper  startForeground android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startRecording android.content.ContextWrapper  stopSelf android.content.ContextWrapper  ACTION_SEND android.content.Intent  EXTRA_STREAM android.content.Intent  Intent android.content.Intent  Uri android.content.Intent  apply android.content.Intent  
createChooser android.content.Intent  equals android.content.Intent  getAPPLY android.content.Intent  getApply android.content.Intent  getLET android.content.Intent  getLet android.content.Intent  getParcelableExtra android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  let android.content.Intent  putExtra android.content.Intent  setType android.content.Intent  type android.content.Intent  MediaPlayer 
android.media  
MediaRecorder 
android.media  apply android.media.MediaPlayer  getAPPLY android.media.MediaPlayer  getApply android.media.MediaPlayer  prepare android.media.MediaPlayer  release android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  AudioEncoder android.media.MediaRecorder  AudioSource android.media.MediaRecorder  Build android.media.MediaRecorder  
MediaRecorder android.media.MediaRecorder  OutputFormat android.media.MediaRecorder  apply android.media.MediaRecorder  getAPPLY android.media.MediaRecorder  getApply android.media.MediaRecorder  getISRecording android.media.MediaRecorder  getIsRecording android.media.MediaRecorder  
getOUTPUTFile android.media.MediaRecorder  
getOutputFile android.media.MediaRecorder  getSTOPSelf android.media.MediaRecorder  getStopSelf android.media.MediaRecorder  isRecording android.media.MediaRecorder  
outputFile android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioEncodingBitRate android.media.MediaRecorder  setAudioSamplingRate android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  stopSelf android.media.MediaRecorder  AAC (android.media.MediaRecorder.AudioEncoder  MIC 'android.media.MediaRecorder.AudioSource  MPEG_4 (android.media.MediaRecorder.OutputFormat  MediaProjection android.media.projection  MediaProjectionManager android.media.projection  stop (android.media.projection.MediaProjection  createScreenCaptureIntent /android.media.projection.MediaProjectionManager  getMediaProjection /android.media.projection.MediaProjectionManager  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  IBinder 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  Settings android.provider  Bundle  android.view.ContextThemeWrapper  CallRecorderApp  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  AccessibilityEvent android.view.accessibility  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  CallRecorderApp #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  CallRecorderApp -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  invoke Zandroidx.activity.result.contract.ActivityResultContracts.StartActivityForResult.Companion  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  CallRecordingItem "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  PlaybackControls "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  
SideEffect "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  all "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNullOrEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  toTypedArray "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Arrangement .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  CallRecordingItem .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  PlaybackControls .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  Uri .androidx.compose.foundation.layout.ColumnScope  apply .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getAPPLY .androidx.compose.foundation.layout.ColumnScope  getApply .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  getISNullOrEmpty .androidx.compose.foundation.layout.ColumnScope  getIsNullOrEmpty .androidx.compose.foundation.layout.ColumnScope  getJAVA .androidx.compose.foundation.layout.ColumnScope  getJava .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNullOrEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Intent +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  MediaPlayer +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  PlaybackControls +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Uri +androidx.compose.foundation.layout.RowScope  apply +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  getAPPLY +androidx.compose.foundation.layout.RowScope  getApply +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  CallRecordingItem .androidx.compose.foundation.lazy.LazyItemScope  Divider .androidx.compose.foundation.lazy.LazyItemScope  CallRecordingItem .androidx.compose.foundation.lazy.LazyListScope  Divider .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  Arrangement androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  CallRecordingItem androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Divider androidx.compose.material3  Intent androidx.compose.material3  
LazyColumn androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  PlaybackControls androidx.compose.material3  Row androidx.compose.material3  
SideEffect androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  Uri androidx.compose.material3  all androidx.compose.material3  apply androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNullOrEmpty androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  toTypedArray androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  error &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  Build androidx.compose.runtime  Button androidx.compose.runtime  CallRecordingsViewModel androidx.compose.runtime  Class androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  Manifest androidx.compose.runtime  MediaPlayer androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  Text androidx.compose.runtime  all androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  toTypedArray androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  value androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  NotificationCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  CallRecorderApp #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  viewModelScope androidx.lifecycle  Activity #androidx.lifecycle.AndroidViewModel  ActivityResultLauncher #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Build #androidx.lifecycle.AndroidViewModel  Context #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  Intent #androidx.lifecycle.AndroidViewModel  MediaProjection #androidx.lifecycle.AndroidViewModel  MediaProjectionManager #androidx.lifecycle.AndroidViewModel  let #androidx.lifecycle.AndroidViewModel  	onCleared #androidx.lifecycle.AndroidViewModel  Activity androidx.lifecycle.ViewModel  ActivityResultLauncher androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Build androidx.lifecycle.ViewModel  
CallRecording androidx.lifecycle.ViewModel  CallRecordingRepository androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Intent androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MediaProjection androidx.lifecycle.ViewModel  MediaProjectionManager androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  _recordings androidx.lifecycle.ViewModel  deleteRecording androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  let androidx.lifecycle.ViewModel  loadRecordings androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  Factory $androidx.lifecycle.ViewModelProvider  	viewModel $androidx.lifecycle.viewmodel.compose  CallRecorderApp com.example.callrecorder  MainActivity com.example.callrecorder  R com.example.callrecorder  
setContent com.example.callrecorder  Bundle %com.example.callrecorder.MainActivity  CallRecorderApp %com.example.callrecorder.MainActivity  
getSETContent %com.example.callrecorder.MainActivity  
getSetContent %com.example.callrecorder.MainActivity  
setContent %com.example.callrecorder.MainActivity  mipmap com.example.callrecorder.R  ic_launcher !com.example.callrecorder.R.mipmap  CallRecordingRepository com.example.callrecorder.data  Dispatchers com.example.callrecorder.data  InMemoryCallRecordingRepository com.example.callrecorder.data  List com.example.callrecorder.data  Long com.example.callrecorder.data  String com.example.callrecorder.data  Unit com.example.callrecorder.data  find com.example.callrecorder.data  indexOfFirst com.example.callrecorder.data  
mutableListOf com.example.callrecorder.data  nextId com.example.callrecorder.data  
recordings com.example.callrecorder.data  	removeAll com.example.callrecorder.data  toList com.example.callrecorder.data  withContext com.example.callrecorder.data  
CallRecording 5com.example.callrecorder.data.CallRecordingRepository  List 5com.example.callrecorder.data.CallRecordingRepository  Long 5com.example.callrecorder.data.CallRecordingRepository  String 5com.example.callrecorder.data.CallRecordingRepository  deleteRecording 5com.example.callrecorder.data.CallRecordingRepository  getAllRecordings 5com.example.callrecorder.data.CallRecordingRepository  
CallRecording =com.example.callrecorder.data.InMemoryCallRecordingRepository  Dispatchers =com.example.callrecorder.data.InMemoryCallRecordingRepository  List =com.example.callrecorder.data.InMemoryCallRecordingRepository  Long =com.example.callrecorder.data.InMemoryCallRecordingRepository  String =com.example.callrecorder.data.InMemoryCallRecordingRepository  Unit =com.example.callrecorder.data.InMemoryCallRecordingRepository  find =com.example.callrecorder.data.InMemoryCallRecordingRepository  getFIND =com.example.callrecorder.data.InMemoryCallRecordingRepository  getFind =com.example.callrecorder.data.InMemoryCallRecordingRepository  getINDEXOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getIndexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMUTABLEListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getREMOVEAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  getRemoveAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getTOList =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getToList =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWITHContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWithContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  indexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  
mutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  nextId =com.example.callrecorder.data.InMemoryCallRecordingRepository  
recordings =com.example.callrecorder.data.InMemoryCallRecordingRepository  	removeAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  toList =com.example.callrecorder.data.InMemoryCallRecordingRepository  withContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  
CallRecording com.example.callrecorder.domain  Long com.example.callrecorder.domain  String com.example.callrecorder.domain  Long -com.example.callrecorder.domain.CallRecording  String -com.example.callrecorder.domain.CallRecording  callType -com.example.callrecorder.domain.CallRecording  
callerName -com.example.callrecorder.domain.CallRecording  copy -com.example.callrecorder.domain.CallRecording  date -com.example.callrecorder.domain.CallRecording  filePath -com.example.callrecorder.domain.CallRecording  id -com.example.callrecorder.domain.CallRecording  phoneNumber -com.example.callrecorder.domain.CallRecording  
transcription -com.example.callrecorder.domain.CallRecording  Activity  com.example.callrecorder.service  Build  com.example.callrecorder.service  CallAccessibilityService  com.example.callrecorder.service  CallRecordingService  com.example.callrecorder.service  Context  com.example.callrecorder.service  Date  com.example.callrecorder.service  	Exception  com.example.callrecorder.service  File  com.example.callrecorder.service  Int  com.example.callrecorder.service  Locale  com.example.callrecorder.service  MediaProjectionHelper  com.example.callrecorder.service  
MediaRecorder  com.example.callrecorder.service  NotificationChannel  com.example.callrecorder.service  NotificationCompat  com.example.callrecorder.service  NotificationManager  com.example.callrecorder.service  R  com.example.callrecorder.service  START_STICKY  com.example.callrecorder.service  SimpleDateFormat  com.example.callrecorder.service  String  com.example.callrecorder.service  apply  com.example.callrecorder.service  isRecording  com.example.callrecorder.service  let  com.example.callrecorder.service  
outputFile  com.example.callrecorder.service  stopSelf  com.example.callrecorder.service  AccessibilityEvent 9com.example.callrecorder.service.CallAccessibilityService  Build 5com.example.callrecorder.service.CallRecordingService  Context 5com.example.callrecorder.service.CallRecordingService  Date 5com.example.callrecorder.service.CallRecordingService  	Exception 5com.example.callrecorder.service.CallRecordingService  File 5com.example.callrecorder.service.CallRecordingService  IBinder 5com.example.callrecorder.service.CallRecordingService  Int 5com.example.callrecorder.service.CallRecordingService  Intent 5com.example.callrecorder.service.CallRecordingService  Locale 5com.example.callrecorder.service.CallRecordingService  
MediaRecorder 5com.example.callrecorder.service.CallRecordingService  Notification 5com.example.callrecorder.service.CallRecordingService  NotificationChannel 5com.example.callrecorder.service.CallRecordingService  NotificationCompat 5com.example.callrecorder.service.CallRecordingService  NotificationManager 5com.example.callrecorder.service.CallRecordingService  R 5com.example.callrecorder.service.CallRecordingService  START_STICKY 5com.example.callrecorder.service.CallRecordingService  SimpleDateFormat 5com.example.callrecorder.service.CallRecordingService  String 5com.example.callrecorder.service.CallRecordingService  apply 5com.example.callrecorder.service.CallRecordingService  getAPPLY 5com.example.callrecorder.service.CallRecordingService  getApply 5com.example.callrecorder.service.CallRecordingService  getExternalFilesDir 5com.example.callrecorder.service.CallRecordingService  getSystemService 5com.example.callrecorder.service.CallRecordingService  isRecording 5com.example.callrecorder.service.CallRecordingService  
outputFile 5com.example.callrecorder.service.CallRecordingService  recorder 5com.example.callrecorder.service.CallRecordingService  startForeground 5com.example.callrecorder.service.CallRecordingService  startForegroundService 5com.example.callrecorder.service.CallRecordingService  startRecording 5com.example.callrecorder.service.CallRecordingService  stopSelf 5com.example.callrecorder.service.CallRecordingService  Activity 6com.example.callrecorder.service.MediaProjectionHelper  ActivityResultLauncher 6com.example.callrecorder.service.MediaProjectionHelper  Application 6com.example.callrecorder.service.MediaProjectionHelper  Build 6com.example.callrecorder.service.MediaProjectionHelper  Context 6com.example.callrecorder.service.MediaProjectionHelper  Int 6com.example.callrecorder.service.MediaProjectionHelper  Intent 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjection 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjectionManager 6com.example.callrecorder.service.MediaProjectionHelper  getLET 6com.example.callrecorder.service.MediaProjectionHelper  getLet 6com.example.callrecorder.service.MediaProjectionHelper  let 6com.example.callrecorder.service.MediaProjectionHelper  mediaProjection 6com.example.callrecorder.service.MediaProjectionHelper  mediaProjectionManager 6com.example.callrecorder.service.MediaProjectionHelper  Activity com.example.callrecorder.ui  ActivityResultContracts com.example.callrecorder.ui  AlertDialog com.example.callrecorder.ui  Arrangement com.example.callrecorder.ui  Build com.example.callrecorder.ui  Button com.example.callrecorder.ui  ButtonDefaults com.example.callrecorder.ui  CallRecorderApp com.example.callrecorder.ui  CallRecordingItem com.example.callrecorder.ui  CallRecordingsListScreen com.example.callrecorder.ui  CallRecordingsViewModel com.example.callrecorder.ui  Class com.example.callrecorder.ui  Column com.example.callrecorder.ui  
Composable com.example.callrecorder.ui  Context com.example.callrecorder.ui  DisposableEffect com.example.callrecorder.ui  Divider com.example.callrecorder.ui  Intent com.example.callrecorder.ui  
LazyColumn com.example.callrecorder.ui  
MainScreen com.example.callrecorder.ui  Manifest com.example.callrecorder.ui  
MaterialTheme com.example.callrecorder.ui  MediaPlayer com.example.callrecorder.ui  Modifier com.example.callrecorder.ui  PermissionAndConsentScreen com.example.callrecorder.ui  PlaybackControls com.example.callrecorder.ui  PreviewCallRecorderApp com.example.callrecorder.ui  PrivacyPolicyScreen com.example.callrecorder.ui  Row com.example.callrecorder.ui  
SideEffect com.example.callrecorder.ui  Spacer com.example.callrecorder.ui  StartRecordingButton com.example.callrecorder.ui  Suppress com.example.callrecorder.ui  Text com.example.callrecorder.ui  Unit com.example.callrecorder.ui  Uri com.example.callrecorder.ui  all com.example.callrecorder.ui  android com.example.callrecorder.ui  androidx com.example.callrecorder.ui  apply com.example.callrecorder.ui  fillMaxSize com.example.callrecorder.ui  fillMaxWidth com.example.callrecorder.ui  getValue com.example.callrecorder.ui  height com.example.callrecorder.ui  
isNullOrEmpty com.example.callrecorder.ui  items com.example.callrecorder.ui  java com.example.callrecorder.ui  listOf com.example.callrecorder.ui  mutableStateOf com.example.callrecorder.ui  padding com.example.callrecorder.ui  provideDelegate com.example.callrecorder.ui  remember com.example.callrecorder.ui  setValue com.example.callrecorder.ui  toTypedArray com.example.callrecorder.ui  width com.example.callrecorder.ui  CallRecordingsViewModel %com.example.callrecorder.ui.viewmodel  List %com.example.callrecorder.ui.viewmodel  Long %com.example.callrecorder.ui.viewmodel  MutableStateFlow %com.example.callrecorder.ui.viewmodel  _recordings %com.example.callrecorder.ui.viewmodel  	emptyList %com.example.callrecorder.ui.viewmodel  launch %com.example.callrecorder.ui.viewmodel  loadRecordings %com.example.callrecorder.ui.viewmodel  
repository %com.example.callrecorder.ui.viewmodel  viewModelScope %com.example.callrecorder.ui.viewmodel  
CallRecording =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  CallRecordingRepository =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  List =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Long =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  MutableStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	StateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  _recordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  deleteRecording =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	emptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEMPTYList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEmptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	getLAUNCH =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	getLaunch =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getVIEWModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getViewModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  launch =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  loadRecordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
recordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
repository =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  viewModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Build com.example.callrecorder.util  CallRecordingService com.example.callrecorder.util  Intent com.example.callrecorder.util  RecordingServiceStarter com.example.callrecorder.util  java com.example.callrecorder.util  Build 5com.example.callrecorder.util.RecordingServiceStarter  CallRecordingService 5com.example.callrecorder.util.RecordingServiceStarter  Context 5com.example.callrecorder.util.RecordingServiceStarter  Intent 5com.example.callrecorder.util.RecordingServiceStarter  java 5com.example.callrecorder.util.RecordingServiceStarter  start 5com.example.callrecorder.util.RecordingServiceStarter  File java.io  absolutePath java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  setAbsolutePath java.io.File  Activity 	java.lang  ActivityResultContracts 	java.lang  Arrangement 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  CallRecorderApp 	java.lang  CallRecordingItem 	java.lang  CallRecordingService 	java.lang  CallRecordingsViewModel 	java.lang  Class 	java.lang  Context 	java.lang  Date 	java.lang  Dispatchers 	java.lang  Divider 	java.lang  	Exception 	java.lang  File 	java.lang  Intent 	java.lang  
LazyColumn 	java.lang  Locale 	java.lang  Manifest 	java.lang  
MaterialTheme 	java.lang  MediaPlayer 	java.lang  
MediaRecorder 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  PlaybackControls 	java.lang  R 	java.lang  Row 	java.lang  START_STICKY 	java.lang  SimpleDateFormat 	java.lang  Spacer 	java.lang  Text 	java.lang  Unit 	java.lang  Uri 	java.lang  _recordings 	java.lang  all 	java.lang  android 	java.lang  androidx 	java.lang  apply 	java.lang  	emptyList 	java.lang  fillMaxWidth 	java.lang  find 	java.lang  height 	java.lang  indexOfFirst 	java.lang  
isNullOrEmpty 	java.lang  isRecording 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  loadRecordings 	java.lang  
mutableListOf 	java.lang  nextId 	java.lang  
outputFile 	java.lang  provideDelegate 	java.lang  
recordings 	java.lang  	removeAll 	java.lang  
repository 	java.lang  stopSelf 	java.lang  toList 	java.lang  toTypedArray 	java.lang  width 	java.lang  withContext 	java.lang  printStackTrace java.lang.Exception  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Build 	java.util  Context 	java.util  Date 	java.util  	Exception 	java.util  File 	java.util  Locale 	java.util  
MediaRecorder 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  R 	java.util  START_STICKY 	java.util  SimpleDateFormat 	java.util  apply 	java.util  isRecording 	java.util  
outputFile 	java.util  stopSelf 	java.util  US java.util.Locale  Activity kotlin  ActivityResultContracts kotlin  Any kotlin  Arrangement kotlin  Array kotlin  Boolean kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  CallRecorderApp kotlin  CallRecordingItem kotlin  CallRecordingService kotlin  CallRecordingsViewModel kotlin  Class kotlin  Context kotlin  Date kotlin  Dispatchers kotlin  Divider kotlin  	Exception kotlin  File kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Intent kotlin  
LazyColumn kotlin  Locale kotlin  Long kotlin  Manifest kotlin  
MaterialTheme kotlin  MediaPlayer kotlin  
MediaRecorder kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  PlaybackControls kotlin  R kotlin  Row kotlin  START_STICKY kotlin  SimpleDateFormat kotlin  Spacer kotlin  String kotlin  Suppress kotlin  Text kotlin  Unit kotlin  Uri kotlin  _recordings kotlin  all kotlin  android kotlin  androidx kotlin  apply kotlin  	emptyList kotlin  fillMaxWidth kotlin  find kotlin  height kotlin  indexOfFirst kotlin  
isNullOrEmpty kotlin  isRecording kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  loadRecordings kotlin  
mutableListOf kotlin  nextId kotlin  
outputFile kotlin  provideDelegate kotlin  
recordings kotlin  	removeAll kotlin  
repository kotlin  stopSelf kotlin  toList kotlin  toTypedArray kotlin  width kotlin  withContext kotlin  getDP 
kotlin.Int  getDp 
kotlin.Int  getISNullOrEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  Activity kotlin.annotation  ActivityResultContracts kotlin.annotation  Arrangement kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  CallRecorderApp kotlin.annotation  CallRecordingItem kotlin.annotation  CallRecordingService kotlin.annotation  CallRecordingsViewModel kotlin.annotation  Class kotlin.annotation  Context kotlin.annotation  Date kotlin.annotation  Dispatchers kotlin.annotation  Divider kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  Intent kotlin.annotation  
LazyColumn kotlin.annotation  Locale kotlin.annotation  Manifest kotlin.annotation  
MaterialTheme kotlin.annotation  MediaPlayer kotlin.annotation  
MediaRecorder kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  PlaybackControls kotlin.annotation  R kotlin.annotation  Row kotlin.annotation  START_STICKY kotlin.annotation  SimpleDateFormat kotlin.annotation  Spacer kotlin.annotation  Text kotlin.annotation  Unit kotlin.annotation  Uri kotlin.annotation  _recordings kotlin.annotation  all kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  apply kotlin.annotation  	emptyList kotlin.annotation  fillMaxWidth kotlin.annotation  find kotlin.annotation  height kotlin.annotation  indexOfFirst kotlin.annotation  
isNullOrEmpty kotlin.annotation  isRecording kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  loadRecordings kotlin.annotation  
mutableListOf kotlin.annotation  nextId kotlin.annotation  
outputFile kotlin.annotation  provideDelegate kotlin.annotation  
recordings kotlin.annotation  	removeAll kotlin.annotation  
repository kotlin.annotation  stopSelf kotlin.annotation  toList kotlin.annotation  toTypedArray kotlin.annotation  width kotlin.annotation  withContext kotlin.annotation  Activity kotlin.collections  ActivityResultContracts kotlin.collections  Arrangement kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  CallRecorderApp kotlin.collections  CallRecordingItem kotlin.collections  CallRecordingService kotlin.collections  CallRecordingsViewModel kotlin.collections  Class kotlin.collections  Context kotlin.collections  Date kotlin.collections  Dispatchers kotlin.collections  Divider kotlin.collections  	Exception kotlin.collections  File kotlin.collections  Intent kotlin.collections  
LazyColumn kotlin.collections  List kotlin.collections  Locale kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  MediaPlayer kotlin.collections  
MediaRecorder kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  PlaybackControls kotlin.collections  R kotlin.collections  Row kotlin.collections  START_STICKY kotlin.collections  SimpleDateFormat kotlin.collections  Spacer kotlin.collections  Text kotlin.collections  Unit kotlin.collections  Uri kotlin.collections  _recordings kotlin.collections  all kotlin.collections  android kotlin.collections  androidx kotlin.collections  apply kotlin.collections  	emptyList kotlin.collections  fillMaxWidth kotlin.collections  find kotlin.collections  height kotlin.collections  indexOfFirst kotlin.collections  
isNullOrEmpty kotlin.collections  isRecording kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  loadRecordings kotlin.collections  
mutableListOf kotlin.collections  nextId kotlin.collections  
outputFile kotlin.collections  provideDelegate kotlin.collections  
recordings kotlin.collections  	removeAll kotlin.collections  
repository kotlin.collections  stopSelf kotlin.collections  toList kotlin.collections  toTypedArray kotlin.collections  width kotlin.collections  withContext kotlin.collections  getTOTypedArray kotlin.collections.List  getToTypedArray kotlin.collections.List  Entry kotlin.collections.Map  getALL kotlin.collections.Map  getAll kotlin.collections.Map  getFIND kotlin.collections.MutableList  getFind kotlin.collections.MutableList  getINDEXOfFirst kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  	getTOList kotlin.collections.MutableList  	getToList kotlin.collections.MutableList  Activity kotlin.comparisons  ActivityResultContracts kotlin.comparisons  Arrangement kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  CallRecorderApp kotlin.comparisons  CallRecordingItem kotlin.comparisons  CallRecordingService kotlin.comparisons  CallRecordingsViewModel kotlin.comparisons  Class kotlin.comparisons  Context kotlin.comparisons  Date kotlin.comparisons  Dispatchers kotlin.comparisons  Divider kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  Intent kotlin.comparisons  
LazyColumn kotlin.comparisons  Locale kotlin.comparisons  Manifest kotlin.comparisons  
MaterialTheme kotlin.comparisons  MediaPlayer kotlin.comparisons  
MediaRecorder kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  PlaybackControls kotlin.comparisons  R kotlin.comparisons  Row kotlin.comparisons  START_STICKY kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Spacer kotlin.comparisons  Text kotlin.comparisons  Unit kotlin.comparisons  Uri kotlin.comparisons  _recordings kotlin.comparisons  all kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  apply kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxWidth kotlin.comparisons  find kotlin.comparisons  height kotlin.comparisons  indexOfFirst kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  isRecording kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  loadRecordings kotlin.comparisons  
mutableListOf kotlin.comparisons  nextId kotlin.comparisons  
outputFile kotlin.comparisons  provideDelegate kotlin.comparisons  
recordings kotlin.comparisons  	removeAll kotlin.comparisons  
repository kotlin.comparisons  stopSelf kotlin.comparisons  toList kotlin.comparisons  toTypedArray kotlin.comparisons  width kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  Activity 	kotlin.io  ActivityResultContracts 	kotlin.io  Arrangement 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  CallRecorderApp 	kotlin.io  CallRecordingItem 	kotlin.io  CallRecordingService 	kotlin.io  CallRecordingsViewModel 	kotlin.io  Class 	kotlin.io  Context 	kotlin.io  Date 	kotlin.io  Dispatchers 	kotlin.io  Divider 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  Intent 	kotlin.io  
LazyColumn 	kotlin.io  Locale 	kotlin.io  Manifest 	kotlin.io  
MaterialTheme 	kotlin.io  MediaPlayer 	kotlin.io  
MediaRecorder 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  PlaybackControls 	kotlin.io  R 	kotlin.io  Row 	kotlin.io  START_STICKY 	kotlin.io  SimpleDateFormat 	kotlin.io  Spacer 	kotlin.io  Text 	kotlin.io  Unit 	kotlin.io  Uri 	kotlin.io  _recordings 	kotlin.io  all 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  apply 	kotlin.io  	emptyList 	kotlin.io  fillMaxWidth 	kotlin.io  find 	kotlin.io  height 	kotlin.io  indexOfFirst 	kotlin.io  
isNullOrEmpty 	kotlin.io  isRecording 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  loadRecordings 	kotlin.io  
mutableListOf 	kotlin.io  nextId 	kotlin.io  
outputFile 	kotlin.io  provideDelegate 	kotlin.io  
recordings 	kotlin.io  	removeAll 	kotlin.io  
repository 	kotlin.io  stopSelf 	kotlin.io  toList 	kotlin.io  toTypedArray 	kotlin.io  width 	kotlin.io  withContext 	kotlin.io  Activity 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  Arrangement 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  CallRecorderApp 
kotlin.jvm  CallRecordingItem 
kotlin.jvm  CallRecordingService 
kotlin.jvm  CallRecordingsViewModel 
kotlin.jvm  Class 
kotlin.jvm  Context 
kotlin.jvm  Date 
kotlin.jvm  Dispatchers 
kotlin.jvm  Divider 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  Intent 
kotlin.jvm  
LazyColumn 
kotlin.jvm  Locale 
kotlin.jvm  Manifest 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  MediaPlayer 
kotlin.jvm  
MediaRecorder 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  PlaybackControls 
kotlin.jvm  R 
kotlin.jvm  Row 
kotlin.jvm  START_STICKY 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Spacer 
kotlin.jvm  Text 
kotlin.jvm  Unit 
kotlin.jvm  Uri 
kotlin.jvm  _recordings 
kotlin.jvm  all 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  apply 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  find 
kotlin.jvm  height 
kotlin.jvm  indexOfFirst 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  isRecording 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  loadRecordings 
kotlin.jvm  
mutableListOf 
kotlin.jvm  nextId 
kotlin.jvm  
outputFile 
kotlin.jvm  provideDelegate 
kotlin.jvm  
recordings 
kotlin.jvm  	removeAll 
kotlin.jvm  
repository 
kotlin.jvm  stopSelf 
kotlin.jvm  toList 
kotlin.jvm  toTypedArray 
kotlin.jvm  width 
kotlin.jvm  withContext 
kotlin.jvm  Activity 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  Arrangement 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  CallRecorderApp 
kotlin.ranges  CallRecordingItem 
kotlin.ranges  CallRecordingService 
kotlin.ranges  CallRecordingsViewModel 
kotlin.ranges  Class 
kotlin.ranges  Context 
kotlin.ranges  Date 
kotlin.ranges  Dispatchers 
kotlin.ranges  Divider 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  Intent 
kotlin.ranges  
LazyColumn 
kotlin.ranges  Locale 
kotlin.ranges  Manifest 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  MediaPlayer 
kotlin.ranges  
MediaRecorder 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  PlaybackControls 
kotlin.ranges  R 
kotlin.ranges  Row 
kotlin.ranges  START_STICKY 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Spacer 
kotlin.ranges  Text 
kotlin.ranges  Unit 
kotlin.ranges  Uri 
kotlin.ranges  _recordings 
kotlin.ranges  all 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  apply 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  find 
kotlin.ranges  height 
kotlin.ranges  indexOfFirst 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  isRecording 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  loadRecordings 
kotlin.ranges  
mutableListOf 
kotlin.ranges  nextId 
kotlin.ranges  
outputFile 
kotlin.ranges  provideDelegate 
kotlin.ranges  
recordings 
kotlin.ranges  	removeAll 
kotlin.ranges  
repository 
kotlin.ranges  stopSelf 
kotlin.ranges  toList 
kotlin.ranges  toTypedArray 
kotlin.ranges  width 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  Activity kotlin.sequences  ActivityResultContracts kotlin.sequences  Arrangement kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  CallRecorderApp kotlin.sequences  CallRecordingItem kotlin.sequences  CallRecordingService kotlin.sequences  CallRecordingsViewModel kotlin.sequences  Class kotlin.sequences  Context kotlin.sequences  Date kotlin.sequences  Dispatchers kotlin.sequences  Divider kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  Intent kotlin.sequences  
LazyColumn kotlin.sequences  Locale kotlin.sequences  Manifest kotlin.sequences  
MaterialTheme kotlin.sequences  MediaPlayer kotlin.sequences  
MediaRecorder kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  PlaybackControls kotlin.sequences  R kotlin.sequences  Row kotlin.sequences  START_STICKY kotlin.sequences  SimpleDateFormat kotlin.sequences  Spacer kotlin.sequences  Text kotlin.sequences  Unit kotlin.sequences  Uri kotlin.sequences  _recordings kotlin.sequences  all kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  apply kotlin.sequences  	emptyList kotlin.sequences  fillMaxWidth kotlin.sequences  find kotlin.sequences  height kotlin.sequences  indexOfFirst kotlin.sequences  
isNullOrEmpty kotlin.sequences  isRecording kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  loadRecordings kotlin.sequences  
mutableListOf kotlin.sequences  nextId kotlin.sequences  
outputFile kotlin.sequences  provideDelegate kotlin.sequences  
recordings kotlin.sequences  	removeAll kotlin.sequences  
repository kotlin.sequences  stopSelf kotlin.sequences  toList kotlin.sequences  toTypedArray kotlin.sequences  width kotlin.sequences  withContext kotlin.sequences  Activity kotlin.text  ActivityResultContracts kotlin.text  Arrangement kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  CallRecorderApp kotlin.text  CallRecordingItem kotlin.text  CallRecordingService kotlin.text  CallRecordingsViewModel kotlin.text  Class kotlin.text  Context kotlin.text  Date kotlin.text  Dispatchers kotlin.text  Divider kotlin.text  	Exception kotlin.text  File kotlin.text  Intent kotlin.text  
LazyColumn kotlin.text  Locale kotlin.text  Manifest kotlin.text  
MaterialTheme kotlin.text  MediaPlayer kotlin.text  
MediaRecorder kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  PlaybackControls kotlin.text  R kotlin.text  Row kotlin.text  START_STICKY kotlin.text  SimpleDateFormat kotlin.text  Spacer kotlin.text  Text kotlin.text  Unit kotlin.text  Uri kotlin.text  _recordings kotlin.text  all kotlin.text  android kotlin.text  androidx kotlin.text  apply kotlin.text  	emptyList kotlin.text  fillMaxWidth kotlin.text  find kotlin.text  height kotlin.text  indexOfFirst kotlin.text  
isNullOrEmpty kotlin.text  isRecording kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  loadRecordings kotlin.text  
mutableListOf kotlin.text  nextId kotlin.text  
outputFile kotlin.text  provideDelegate kotlin.text  
recordings kotlin.text  	removeAll kotlin.text  
repository kotlin.text  stopSelf kotlin.text  toList kotlin.text  toTypedArray kotlin.text  width kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Unit !kotlinx.coroutines.CoroutineScope  _recordings !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  getFIND !kotlinx.coroutines.CoroutineScope  getFind !kotlinx.coroutines.CoroutineScope  getINDEXOfFirst !kotlinx.coroutines.CoroutineScope  getIndexOfFirst !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLOADRecordings !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLoadRecordings !kotlinx.coroutines.CoroutineScope  	getNEXTId !kotlinx.coroutines.CoroutineScope  	getNextId !kotlinx.coroutines.CoroutineScope  
getRECORDINGS !kotlinx.coroutines.CoroutineScope  getREMOVEAll !kotlinx.coroutines.CoroutineScope  
getREPOSITORY !kotlinx.coroutines.CoroutineScope  
getRecordings !kotlinx.coroutines.CoroutineScope  getRemoveAll !kotlinx.coroutines.CoroutineScope  
getRepository !kotlinx.coroutines.CoroutineScope  	getTOList !kotlinx.coroutines.CoroutineScope  	getToList !kotlinx.coroutines.CoroutineScope  get_recordings !kotlinx.coroutines.CoroutineScope  indexOfFirst !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadRecordings !kotlinx.coroutines.CoroutineScope  nextId !kotlinx.coroutines.CoroutineScope  
recordings !kotlinx.coroutines.CoroutineScope  	removeAll !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  toList !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    