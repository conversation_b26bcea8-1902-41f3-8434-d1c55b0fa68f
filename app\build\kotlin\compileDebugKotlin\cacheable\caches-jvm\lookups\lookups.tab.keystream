  Manifest android  
permission android.Manifest  ACCESS_NETWORK_STATE android.Manifest.permission  FOREGROUND_SERVICE android.Manifest.permission  #FOREGROUND_SERVICE_MEDIA_PROJECTION android.Manifest.permission  FOREGROUND_SERVICE_MICROPHONE android.Manifest.permission  INTERNET android.Manifest.permission  POST_NOTIFICATIONS android.Manifest.permission  
READ_CALL_LOG android.Manifest.permission  
READ_CONTACTS android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  	WAKE_LOCK android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  AccessibilityService android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  ACTION_START_RECORDING android.app  ACTION_STOP_RECORDING android.app  Activity android.app  Application android.app  AudioFormat android.app  AudioRecord android.app  Build android.app  	ByteArray android.app  
CHANNEL_ID android.app  
CallRecording android.app  CallRecordingRepository android.app  CallRecordingService android.app  CallType android.app  Context android.app  CoroutineScope android.app  Date android.app  Dispatchers android.app  EXTRA_CALL_TYPE android.app  EXTRA_CONTACT_NAME android.app  EXTRA_MEDIA_PROJECTION_DATA android.app  EXTRA_PHONE_NUMBER android.app  	Exception android.app  File android.app  FileOutputStream android.app  IllegalStateException android.app  Intent android.app  Job android.app  Locale android.app  
MediaRecorder android.app  NOTIFICATION_ID android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  
PendingIntent android.app  PowerManager android.app  R android.app  START_STICKY android.app  Service android.app  SimpleDateFormat android.app  
SupervisorJob android.app  System android.app  Thread android.app  apply android.app  audioRecord android.app  cancel android.app  currentRecordingFile android.app  isRecording android.app  java android.app  launch android.app  let android.app  mediaProjection android.app  recordingStartTime android.app  
repository android.app  
stopRecording android.app  Bundle android.app.Activity  CallRecorderApp android.app.Activity  	RESULT_OK android.app.Activity  finish android.app.Activity  getSystemService android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  startActivityForResult android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableVibration android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  
getService android.app.PendingIntent  ACTION_START_RECORDING android.app.Service  ACTION_STOP_RECORDING android.app.Service  AccessibilityEvent android.app.Service  Activity android.app.Service  AudioFormat android.app.Service  AudioRecord android.app.Service  Build android.app.Service  	ByteArray android.app.Service  
CHANNEL_ID android.app.Service  
CallRecording android.app.Service  CallRecordingRepository android.app.Service  CallRecordingService android.app.Service  CallType android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Date android.app.Service  Dispatchers android.app.Service  EXTRA_CALL_TYPE android.app.Service  EXTRA_CONTACT_NAME android.app.Service  EXTRA_MEDIA_PROJECTION_DATA android.app.Service  EXTRA_PHONE_NUMBER android.app.Service  	Exception android.app.Service  File android.app.Service  FileOutputStream android.app.Service  IBinder android.app.Service  IllegalStateException android.app.Service  Int android.app.Service  Intent android.app.Service  Job android.app.Service  Locale android.app.Service  Long android.app.Service  MediaProjection android.app.Service  MediaProjectionManager android.app.Service  
MediaRecorder android.app.Service  NOTIFICATION_ID android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  PowerManager android.app.Service  R android.app.Service  START_STICKY android.app.Service  SimpleDateFormat android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  System android.app.Service  Thread android.app.Service  acquireWakeLock android.app.Service  apply android.app.Service  audioRecord android.app.Service  cancel android.app.Service  createNotificationChannel android.app.Service  createRecordingFile android.app.Service  createRecordingNotification android.app.Service  currentRecordingFile android.app.Service  getExternalFilesDir android.app.Service  getSystemService android.app.Service  isRecording android.app.Service  java android.app.Service  launch android.app.Service  let android.app.Service  mediaProjection android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  recordingStartTime android.app.Service  
repository android.app.Service  saveRecordingToDatabase android.app.Service  startAudioRecording android.app.Service  startForeground android.app.Service  startRecording android.app.Service  stopForeground android.app.Service  
stopRecording android.app.Service  stopSelf android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  ContentResolver android.content  Context android.content  Intent android.content  IntentFilter android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  getLET !android.content.BroadcastReceiver  getLet !android.content.BroadcastReceiver  handleOutgoingCall !android.content.BroadcastReceiver  let !android.content.BroadcastReceiver  query android.content.ContentResolver  registerContentObserver android.content.ContentResolver  unregisterContentObserver android.content.ContentResolver  ACTION_START_RECORDING android.content.Context  ACTION_STOP_RECORDING android.content.Context  AccessibilityEvent android.content.Context  Activity android.content.Context  AudioFormat android.content.Context  AudioRecord android.content.Context  Build android.content.Context  Bundle android.content.Context  	ByteArray android.content.Context  
CHANNEL_ID android.content.Context  CallRecorderApp android.content.Context  
CallRecording android.content.Context  CallRecordingRepository android.content.Context  CallRecordingService android.content.Context  CallType android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  Date android.content.Context  Dispatchers android.content.Context  EXTRA_CALL_TYPE android.content.Context  EXTRA_CONTACT_NAME android.content.Context  EXTRA_MEDIA_PROJECTION_DATA android.content.Context  EXTRA_PHONE_NUMBER android.content.Context  	Exception android.content.Context  File android.content.Context  FileOutputStream android.content.Context  IBinder android.content.Context  IllegalStateException android.content.Context  Int android.content.Context  Intent android.content.Context  Job android.content.Context  Locale android.content.Context  Long android.content.Context  MEDIA_PROJECTION_SERVICE android.content.Context  MediaProjection android.content.Context  MediaProjectionManager android.content.Context  
MediaRecorder android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  
POWER_SERVICE android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  R android.content.Context  START_STICKY android.content.Context  SimpleDateFormat android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  System android.content.Context  TELEPHONY_SERVICE android.content.Context  Thread android.content.Context  acquireWakeLock android.content.Context  applicationContext android.content.Context  apply android.content.Context  audioRecord android.content.Context  cacheDir android.content.Context  cancel android.content.Context  contentResolver android.content.Context  createNotificationChannel android.content.Context  createRecordingFile android.content.Context  createRecordingNotification android.content.Context  currentRecordingFile android.content.Context  finish android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getCACHEDir android.content.Context  getCONTENTResolver android.content.Context  getCacheDir android.content.Context  getContentResolver android.content.Context  getExternalFilesDir android.content.Context  getPACKAGEName android.content.Context  getPackageName android.content.Context  getSystemService android.content.Context  isRecording android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  mediaProjection android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  packageName android.content.Context  recordingStartTime android.content.Context  registerReceiver android.content.Context  
repository android.content.Context  saveRecordingToDatabase android.content.Context  setApplicationContext android.content.Context  setCacheDir android.content.Context  
setContent android.content.Context  setContentResolver android.content.Context  setPackageName android.content.Context  startActivityForResult android.content.Context  startAudioRecording android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startRecording android.content.Context  startService android.content.Context  stopForeground android.content.Context  
stopRecording android.content.Context  stopSelf android.content.Context  stopService android.content.Context  unregisterReceiver android.content.Context  ACTION_START_RECORDING android.content.ContextWrapper  ACTION_STOP_RECORDING android.content.ContextWrapper  AccessibilityEvent android.content.ContextWrapper  Activity android.content.ContextWrapper  AudioFormat android.content.ContextWrapper  AudioRecord android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  	ByteArray android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CallRecorderApp android.content.ContextWrapper  
CallRecording android.content.ContextWrapper  CallRecordingRepository android.content.ContextWrapper  CallRecordingService android.content.ContextWrapper  CallType android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Date android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  EXTRA_CALL_TYPE android.content.ContextWrapper  EXTRA_CONTACT_NAME android.content.ContextWrapper  EXTRA_MEDIA_PROJECTION_DATA android.content.ContextWrapper  EXTRA_PHONE_NUMBER android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FileOutputStream android.content.ContextWrapper  IBinder android.content.ContextWrapper  IllegalStateException android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Job android.content.ContextWrapper  Locale android.content.ContextWrapper  Long android.content.ContextWrapper  MediaProjection android.content.ContextWrapper  MediaProjectionManager android.content.ContextWrapper  
MediaRecorder android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  R android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  System android.content.ContextWrapper  Thread android.content.ContextWrapper  acquireWakeLock android.content.ContextWrapper  apply android.content.ContextWrapper  audioRecord android.content.ContextWrapper  cancel android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  createRecordingFile android.content.ContextWrapper  createRecordingNotification android.content.ContextWrapper  currentRecordingFile android.content.ContextWrapper  finish android.content.ContextWrapper  getExternalFilesDir android.content.ContextWrapper  getSystemService android.content.ContextWrapper  isRecording android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  mediaProjection android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  recordingStartTime android.content.ContextWrapper  
repository android.content.ContextWrapper  saveRecordingToDatabase android.content.ContextWrapper  
setContent android.content.ContextWrapper  startActivityForResult android.content.ContextWrapper  startAudioRecording android.content.ContextWrapper  startForeground android.content.ContextWrapper  startRecording android.content.ContextWrapper  stopForeground android.content.ContextWrapper  
stopRecording android.content.ContextWrapper  stopSelf android.content.ContextWrapper  ACTION_NEW_OUTGOING_CALL android.content.Intent  ACTION_SEND android.content.Intent  ACTION_STOP_RECORDING android.content.Intent  AUDIO_MIME_TYPE android.content.Intent  CallRecordingService android.content.Intent  EXTRA_PHONE_NUMBER android.content.Intent  EXTRA_STREAM android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  Uri android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  context android.content.Intent  data android.content.Intent  equals android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  
getCONTEXT android.content.Intent  
getContext android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getLET android.content.Intent  getLet android.content.Intent  getParcelableExtra android.content.Intent  getStringExtra android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  let android.content.Intent  putExtra android.content.Intent  	setAction android.content.Intent  setData android.content.Intent  setType android.content.Intent  type android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  ContentObserver android.database  Cursor android.database  Boolean  android.database.ContentObserver  Handler  android.database.ContentObserver  Looper  android.database.ContentObserver  Uri  android.database.ContentObserver  getLET  android.database.ContentObserver  getLet  android.database.ContentObserver  handleCallLogChange  android.database.ContentObserver  launch  android.database.ContentObserver  let  android.database.ContentObserver  onChange  android.database.ContentObserver  serviceScope  android.database.ContentObserver  getColumnIndex android.database.Cursor  	getString android.database.Cursor  getUSE android.database.Cursor  getUse android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  AudioFormat 
android.media  AudioRecord 
android.media  MediaPlayer 
android.media  
MediaRecorder 
android.media  CHANNEL_IN_MONO android.media.AudioFormat  ENCODING_PCM_16BIT android.media.AudioFormat  AudioRecord android.media.AudioRecord  STATE_INITIALIZED android.media.AudioRecord  apply android.media.AudioRecord  getAPPLY android.media.AudioRecord  getApply android.media.AudioRecord  getMinBufferSize android.media.AudioRecord  getSTATE android.media.AudioRecord  getState android.media.AudioRecord  read android.media.AudioRecord  release android.media.AudioRecord  setState android.media.AudioRecord  startRecording android.media.AudioRecord  state android.media.AudioRecord  stop android.media.AudioRecord  
PlaybackState android.media.MediaPlayer  _currentFile android.media.MediaPlayer  _currentPosition android.media.MediaPlayer  	_duration android.media.MediaPlayer  
_errorMessage android.media.MediaPlayer  _playbackState android.media.MediaPlayer  apply android.media.MediaPlayer  context android.media.MediaPlayer  currentPosition android.media.MediaPlayer  duration android.media.MediaPlayer  getAPPLY android.media.MediaPlayer  getApply android.media.MediaPlayer  
getCONTEXT android.media.MediaPlayer  getCURRENTPosition android.media.MediaPlayer  
getContext android.media.MediaPlayer  getCurrentPosition android.media.MediaPlayer  getDURATION android.media.MediaPlayer  getDuration android.media.MediaPlayer  getISPlaying android.media.MediaPlayer  getIsPlaying android.media.MediaPlayer  getLET android.media.MediaPlayer  getLet android.media.MediaPlayer  getMEDIAPlayer android.media.MediaPlayer  getMediaPlayer android.media.MediaPlayer  getSTARTPositionUpdates android.media.MediaPlayer  getSTOPPositionUpdates android.media.MediaPlayer  getStartPositionUpdates android.media.MediaPlayer  getStopPositionUpdates android.media.MediaPlayer  get_currentFile android.media.MediaPlayer  get_currentPosition android.media.MediaPlayer  get_duration android.media.MediaPlayer  get_errorMessage android.media.MediaPlayer  get_playbackState android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  let android.media.MediaPlayer  mediaPlayer android.media.MediaPlayer  pause android.media.MediaPlayer  prepare android.media.MediaPlayer  prepareAsync android.media.MediaPlayer  release android.media.MediaPlayer  seekTo android.media.MediaPlayer  setCurrentPosition android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setDuration android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  setOnErrorListener android.media.MediaPlayer  setOnPreparedListener android.media.MediaPlayer  
setPlaying android.media.MediaPlayer  start android.media.MediaPlayer  startPositionUpdates android.media.MediaPlayer  stop android.media.MediaPlayer  stopPositionUpdates android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  <SAM-CONSTRUCTOR> )android.media.MediaPlayer.OnErrorListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  AudioSource android.media.MediaRecorder  MIC 'android.media.MediaRecorder.AudioSource  MediaProjection android.media.projection  MediaProjectionManager android.media.projection  equals (android.media.projection.MediaProjection  stop (android.media.projection.MediaProjection  createScreenCaptureIntent /android.media.projection.MediaProjectionManager  getMediaProjection /android.media.projection.MediaProjectionManager  Uri android.net  encode android.net.Uri  fromFile android.net.Uri  parse android.net.Uri  toString android.net.Uri  withAppendedPath android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  MANUFACTURER android.os.Build  MODEL android.os.Build  VERSION android.os.Build  
VERSION_CODES android.os.Build  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  UPSIDE_DOWN_CAKE android.os.Build.VERSION_CODES  
MEDIA_MOUNTED android.os.Environment  MEDIA_MOUNTED_READ_ONLY android.os.Environment  getExternalStorageDirectory android.os.Environment  getExternalStorageState android.os.Environment  isExternalStorageManager android.os.Environment  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  PARTIAL_WAKE_LOCK android.os.PowerManager  WakeLock android.os.PowerManager  newWakeLock android.os.PowerManager  acquire  android.os.PowerManager.WakeLock  	getISHeld  android.os.PowerManager.WakeLock  	getIsHeld  android.os.PowerManager.WakeLock  getLET  android.os.PowerManager.WakeLock  getLet  android.os.PowerManager.WakeLock  isHeld  android.os.PowerManager.WakeLock  let  android.os.PowerManager.WakeLock  release  android.os.PowerManager.WakeLock  setHeld  android.os.PowerManager.WakeLock  CallLog android.provider  ContactsContract android.provider  Settings android.provider  Calls android.provider.CallLog  CONTENT_URI android.provider.CallLog.Calls  PhoneLookup !android.provider.ContactsContract  CONTENT_FILTER_URI -android.provider.ContactsContract.PhoneLookup  DISPLAY_NAME -android.provider.ContactsContract.PhoneLookup  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings  -ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  Secure android.provider.Settings  canDrawOverlays android.provider.Settings  ACCESSIBILITY_ENABLED  android.provider.Settings.Secure  ENABLED_ACCESSIBILITY_SERVICES  android.provider.Settings.Secure  getInt  android.provider.Settings.Secure  	getString  android.provider.Settings.Secure  PhoneStateListener android.telephony  TelephonyManager android.telephony  Int $android.telephony.PhoneStateListener  LISTEN_CALL_STATE $android.telephony.PhoneStateListener  LISTEN_NONE $android.telephony.PhoneStateListener  String $android.telephony.PhoneStateListener  getLET $android.telephony.PhoneStateListener  getLet $android.telephony.PhoneStateListener  handleCallStateChange $android.telephony.PhoneStateListener  let $android.telephony.PhoneStateListener  onCallStateChanged $android.telephony.PhoneStateListener  CALL_STATE_IDLE "android.telephony.TelephonyManager  CALL_STATE_OFFHOOK "android.telephony.TelephonyManager  CALL_STATE_RINGING "android.telephony.TelephonyManager  listen "android.telephony.TelephonyManager  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  Bundle  android.view.ContextThemeWrapper  CallRecorderApp  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  startActivityForResult  android.view.ContextThemeWrapper  AccessibilityEvent android.view.accessibility  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  CallRecorderApp #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  CallRecorderApp -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  invoke Zandroidx.activity.result.contract.ActivityResultContracts.StartActivityForResult.Companion  ScrollState androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Call "androidx.compose.foundation.layout  CallRecordingService "androidx.compose.foundation.layout  CallRecordingsViewModel "androidx.compose.foundation.layout  CallType "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Class "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Delete "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  DropdownMenu "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  EmptyRecordingsState "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MoreVert "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  PermissionItem "androidx.compose.foundation.layout  Phone "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  RecordingControlScreen "androidx.compose.foundation.layout  
RecordingItem "androidx.compose.foundation.layout  RecordingsListScreen "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Share "androidx.compose.foundation.layout  
SideEffect "androidx.compose.foundation.layout  SimpleDateFormat "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  SpecialPermissionItem "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  all "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  buildString "androidx.compose.foundation.layout  checkAllPermissions "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mapOf "androidx.compose.foundation.layout  mutableMapOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  set "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  startRecordingService "androidx.compose.foundation.layout  stopRecordingService "androidx.compose.foundation.layout  substringAfterLast "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  toTypedArray "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Delete +androidx.compose.foundation.layout.BoxScope  Divider +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Info +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  Share +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Build .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Call .androidx.compose.foundation.layout.ColumnScope  CallType .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Context .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  DropdownMenu .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  EmptyRecordingsState .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Locale .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MoreVert .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  PermissionItem .androidx.compose.foundation.layout.ColumnScope  Phone .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RecordingControlScreen .androidx.compose.foundation.layout.ColumnScope  
RecordingItem .androidx.compose.foundation.layout.ColumnScope  RecordingsListScreen .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Share .androidx.compose.foundation.layout.ColumnScope  SimpleDateFormat .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  SpecialPermissionItem .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  all .androidx.compose.foundation.layout.ColumnScope  buildString .androidx.compose.foundation.layout.ColumnScope  checkAllPermissions .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getALL .androidx.compose.foundation.layout.ColumnScope  getAll .androidx.compose.foundation.layout.ColumnScope  getBUILDString .androidx.compose.foundation.layout.ColumnScope  getBuildString .androidx.compose.foundation.layout.ColumnScope  getCHECKAllPermissions .androidx.compose.foundation.layout.ColumnScope  getCheckAllPermissions .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getREMEMBERScrollState .androidx.compose.foundation.layout.ColumnScope  getRememberScrollState .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSTARTRecordingService .androidx.compose.foundation.layout.ColumnScope  getSTOPRecordingService .androidx.compose.foundation.layout.ColumnScope  getSUBSTRINGAfterLast .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getStartRecordingService .androidx.compose.foundation.layout.ColumnScope  getStopRecordingService .androidx.compose.foundation.layout.ColumnScope  getSubstringAfterLast .androidx.compose.foundation.layout.ColumnScope  	getTOList .androidx.compose.foundation.layout.ColumnScope  	getToList .androidx.compose.foundation.layout.ColumnScope  getVERTICALScroll .androidx.compose.foundation.layout.ColumnScope  getVerticalScroll .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  startRecordingService .androidx.compose.foundation.layout.ColumnScope  stopRecordingService .androidx.compose.foundation.layout.ColumnScope  substringAfterLast .androidx.compose.foundation.layout.ColumnScope  toList .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Build +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Call +androidx.compose.foundation.layout.RowScope  CallType +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Context +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Divider +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  Locale +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  MediaPlayer +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  SimpleDateFormat +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  TextOverflow +androidx.compose.foundation.layout.RowScope  all +androidx.compose.foundation.layout.RowScope  apply +androidx.compose.foundation.layout.RowScope  checkAllPermissions +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  getALL +androidx.compose.foundation.layout.RowScope  getAPPLY +androidx.compose.foundation.layout.RowScope  getAll +androidx.compose.foundation.layout.RowScope  getApply +androidx.compose.foundation.layout.RowScope  getCHECKAllPermissions +androidx.compose.foundation.layout.RowScope  getCheckAllPermissions +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSTARTRecordingService +androidx.compose.foundation.layout.RowScope  getSTOPRecordingService +androidx.compose.foundation.layout.RowScope  getSUBSTRINGAfterLast +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getStartRecordingService +androidx.compose.foundation.layout.RowScope  getStopRecordingService +androidx.compose.foundation.layout.RowScope  getSubstringAfterLast +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  startRecordingService +androidx.compose.foundation.layout.RowScope  stopRecordingService +androidx.compose.foundation.layout.RowScope  substringAfterLast +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  PermissionItem .androidx.compose.foundation.lazy.LazyItemScope  
RecordingItem .androidx.compose.foundation.lazy.LazyItemScope  SpecialPermissionItem .androidx.compose.foundation.lazy.LazyItemScope  PermissionItem .androidx.compose.foundation.lazy.LazyListScope  
RecordingItem .androidx.compose.foundation.lazy.LazyListScope  SpecialPermissionItem .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  	getTOList .androidx.compose.foundation.lazy.LazyListScope  	getToList .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  toList .androidx.compose.foundation.lazy.LazyListScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Call ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  Phone ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  Activity &androidx.compose.material.icons.filled  ActivityResultContracts &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Build &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Call &androidx.compose.material.icons.filled  CallRecordingService &androidx.compose.material.icons.filled  CallType &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Context &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Divider &androidx.compose.material.icons.filled  DropdownMenu &androidx.compose.material.icons.filled  DropdownMenuItem &androidx.compose.material.icons.filled  EmptyRecordingsState &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  Locale &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  Phone &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  
RecordingItem &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  SimpleDateFormat &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  TextOverflow &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  apply &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  java &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  startRecordingService &androidx.compose.material.icons.filled  stopRecordingService &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Build androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Call androidx.compose.material3  CallRecordingService androidx.compose.material3  CallRecordingsViewModel androidx.compose.material3  CallType androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Checkbox androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Class androidx.compose.material3  Close androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Context androidx.compose.material3  Delete androidx.compose.material3  Divider androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  EmptyRecordingsState androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Info androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  Locale androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MoreVert androidx.compose.material3  OutlinedButton androidx.compose.material3  PermissionItem androidx.compose.material3  Phone androidx.compose.material3  	PlayArrow androidx.compose.material3  RecordingControlScreen androidx.compose.material3  
RecordingItem androidx.compose.material3  RecordingsListScreen androidx.compose.material3  Row androidx.compose.material3  Share androidx.compose.material3  
SideEffect androidx.compose.material3  SimpleDateFormat androidx.compose.material3  Spacer androidx.compose.material3  SpecialPermissionItem androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  TextOverflow androidx.compose.material3  all androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  apply androidx.compose.material3  buildString androidx.compose.material3  checkAllPermissions androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  invoke androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  listOf androidx.compose.material3  mapOf androidx.compose.material3  mutableMapOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberScrollState androidx.compose.material3  set androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  startRecordingService androidx.compose.material3  stopRecordingService androidx.compose.material3  substringAfterLast androidx.compose.material3  toList androidx.compose.material3  toTypedArray androidx.compose.material3  verticalScroll androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Build androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Call androidx.compose.runtime  CallRecordingService androidx.compose.runtime  CallRecordingsViewModel androidx.compose.runtime  CallType androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Checkbox androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Class androidx.compose.runtime  Close androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  Delete androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  Divider androidx.compose.runtime  DropdownMenu androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  EmptyRecordingsState androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Info androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Locale androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  MediaPlayer androidx.compose.runtime  Modifier androidx.compose.runtime  MoreVert androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  PermissionItem androidx.compose.runtime  Phone androidx.compose.runtime  	PlayArrow androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RecordingControlScreen androidx.compose.runtime  
RecordingItem androidx.compose.runtime  RecordingsListScreen androidx.compose.runtime  Row androidx.compose.runtime  Share androidx.compose.runtime  
SideEffect androidx.compose.runtime  SimpleDateFormat androidx.compose.runtime  Spacer androidx.compose.runtime  SpecialPermissionItem androidx.compose.runtime  State androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  TextOverflow androidx.compose.runtime  all androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  buildString androidx.compose.runtime  checkAllPermissions androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  invoke androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  listOf androidx.compose.runtime  mapOf androidx.compose.runtime  mutableMapOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberScrollState androidx.compose.runtime  set androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  startRecordingService androidx.compose.runtime  stopRecordingService androidx.compose.runtime  substringAfterLast androidx.compose.runtime  toList androidx.compose.runtime  toTypedArray androidx.compose.runtime  verticalScroll androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getVERTICALScroll androidx.compose.ui.Modifier  getVerticalScroll androidx.compose.ui.Modifier  	getWEIGHT androidx.compose.ui.Modifier  	getWeight androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  Bundle #androidx.core.app.ComponentActivity  CallRecorderApp #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  FileProvider androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  
getUriForFile "androidx.core.content.FileProvider  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  viewModelScope androidx.lifecycle  Activity #androidx.lifecycle.AndroidViewModel  ActivityResultLauncher #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Build #androidx.lifecycle.AndroidViewModel  Context #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  Intent #androidx.lifecycle.AndroidViewModel  MediaProjection #androidx.lifecycle.AndroidViewModel  MediaProjectionManager #androidx.lifecycle.AndroidViewModel  let #androidx.lifecycle.AndroidViewModel  	onCleared #androidx.lifecycle.AndroidViewModel  Activity androidx.lifecycle.ViewModel  ActivityResultLauncher androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Build androidx.lifecycle.ViewModel  CallRecordingDisplay androidx.lifecycle.ViewModel  CallRecordingRepository androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  File androidx.lifecycle.ViewModel  FileManager androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Intent androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MediaPlayer androidx.lifecycle.ViewModel  MediaProjection androidx.lifecycle.ViewModel  MediaProjectionManager androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
_errorMessage androidx.lifecycle.ViewModel  
_isLoading androidx.lifecycle.ViewModel  _recordings androidx.lifecycle.ViewModel  apply androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  deleteRecording androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  fileManager androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  let androidx.lifecycle.ViewModel  loadRecordings androidx.lifecycle.ViewModel  mediaPlayer androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  
playRecording androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  shareRecording androidx.lifecycle.ViewModel  stopCurrentPlayback androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  viewTranscript androidx.lifecycle.ViewModel  Factory $androidx.lifecycle.ViewModelProvider  collectAsStateWithLifecycle androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  CallRecordingDao androidx.room.RoomDatabase  CallRecordingDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  CallRecordingDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  getSYNCHRONIZED $androidx.room.RoomDatabase.Companion  getSynchronized $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  CallRecorderApp com.example.callrecorder  MainActivity com.example.callrecorder  R com.example.callrecorder  
setContent com.example.callrecorder  Bundle %com.example.callrecorder.MainActivity  CallRecorderApp %com.example.callrecorder.MainActivity  
getSETContent %com.example.callrecorder.MainActivity  
getSetContent %com.example.callrecorder.MainActivity  
setContent %com.example.callrecorder.MainActivity  mipmap com.example.callrecorder.R  ic_launcher !com.example.callrecorder.R.mipmap  BASE_URL com.example.callrecorder.ai  Boolean com.example.callrecorder.ai  CostEstimate com.example.callrecorder.ai  Dispatchers com.example.callrecorder.ai  Double com.example.callrecorder.ai  	Exception com.example.callrecorder.ai  File com.example.callrecorder.ai  GsonConverterFactory com.example.callrecorder.ai  Int com.example.callrecorder.ai  List com.example.callrecorder.ai  Log com.example.callrecorder.ai  Long com.example.callrecorder.ai  
MAX_FILE_SIZE com.example.callrecorder.ai  	Multipart com.example.callrecorder.ai  
MultipartBody com.example.callrecorder.ai  OkHttpClient com.example.callrecorder.ai  OpenAIApiService com.example.callrecorder.ai  
OpenAIService com.example.callrecorder.ai  POST com.example.callrecorder.ai  Part com.example.callrecorder.ai  RequestBody com.example.callrecorder.ai  Retrofit com.example.callrecorder.ai  String com.example.callrecorder.ai  TAG com.example.callrecorder.ai  TIMEOUT_SECONDS com.example.callrecorder.ai  TimeUnit com.example.callrecorder.ai  TranscriptionManager com.example.callrecorder.ai  TranscriptionQueueStatus com.example.callrecorder.ai  TranscriptionResponse com.example.callrecorder.ai  TranscriptionResult com.example.callrecorder.ai  apiKey com.example.callrecorder.ai  
apiService com.example.callrecorder.ai  
asRequestBody com.example.callrecorder.ai  com com.example.callrecorder.ai  contains com.example.callrecorder.ai  context com.example.callrecorder.ai  	extension com.example.callrecorder.ai  format com.example.callrecorder.ai  
isNotEmpty com.example.callrecorder.ai  
isNullOrBlank com.example.callrecorder.ai  java com.example.callrecorder.ai  let com.example.callrecorder.ai  listOf com.example.callrecorder.ai  	lowercase com.example.callrecorder.ai  
mutableListOf com.example.callrecorder.ai  toMediaType com.example.callrecorder.ai  transcribeAudio com.example.callrecorder.ai  withContext com.example.callrecorder.ai  	writeText com.example.callrecorder.ai  Double (com.example.callrecorder.ai.CostEstimate  String (com.example.callrecorder.ai.CostEstimate  estimatedCostUSD (com.example.callrecorder.ai.CostEstimate  estimatedDurationMinutes (com.example.callrecorder.ai.CostEstimate  format (com.example.callrecorder.ai.CostEstimate  	getFORMAT (com.example.callrecorder.ai.CostEstimate  	getFormat (com.example.callrecorder.ai.CostEstimate  	Multipart ,com.example.callrecorder.ai.OpenAIApiService  
MultipartBody ,com.example.callrecorder.ai.OpenAIApiService  POST ,com.example.callrecorder.ai.OpenAIApiService  Part ,com.example.callrecorder.ai.OpenAIApiService  RequestBody ,com.example.callrecorder.ai.OpenAIApiService  Response ,com.example.callrecorder.ai.OpenAIApiService  TranscriptionResponse ,com.example.callrecorder.ai.OpenAIApiService  transcribeAudio ,com.example.callrecorder.ai.OpenAIApiService  BASE_URL )com.example.callrecorder.ai.OpenAIService  Boolean )com.example.callrecorder.ai.OpenAIService  Context )com.example.callrecorder.ai.OpenAIService  CostEstimate )com.example.callrecorder.ai.OpenAIService  Dispatchers )com.example.callrecorder.ai.OpenAIService  	Exception )com.example.callrecorder.ai.OpenAIService  File )com.example.callrecorder.ai.OpenAIService  GsonConverterFactory )com.example.callrecorder.ai.OpenAIService  List )com.example.callrecorder.ai.OpenAIService  Log )com.example.callrecorder.ai.OpenAIService  
MAX_FILE_SIZE )com.example.callrecorder.ai.OpenAIService  
MultipartBody )com.example.callrecorder.ai.OpenAIService  OkHttpClient )com.example.callrecorder.ai.OpenAIService  OpenAIApiService )com.example.callrecorder.ai.OpenAIService  RequestBody )com.example.callrecorder.ai.OpenAIService  Retrofit )com.example.callrecorder.ai.OpenAIService  String )com.example.callrecorder.ai.OpenAIService  TAG )com.example.callrecorder.ai.OpenAIService  TIMEOUT_SECONDS )com.example.callrecorder.ai.OpenAIService  TimeUnit )com.example.callrecorder.ai.OpenAIService  TranscriptionResult )com.example.callrecorder.ai.OpenAIService  apiKey )com.example.callrecorder.ai.OpenAIService  
apiService )com.example.callrecorder.ai.OpenAIService  
asRequestBody )com.example.callrecorder.ai.OpenAIService  contains )com.example.callrecorder.ai.OpenAIService  context )com.example.callrecorder.ai.OpenAIService  	extension )com.example.callrecorder.ai.OpenAIService  getASRequestBody )com.example.callrecorder.ai.OpenAIService  getAsRequestBody )com.example.callrecorder.ai.OpenAIService  getCONTAINS )com.example.callrecorder.ai.OpenAIService  getContains )com.example.callrecorder.ai.OpenAIService  getISNullOrBlank )com.example.callrecorder.ai.OpenAIService  getIsNullOrBlank )com.example.callrecorder.ai.OpenAIService  getLET )com.example.callrecorder.ai.OpenAIService  	getLISTOf )com.example.callrecorder.ai.OpenAIService  getLOWERCASE )com.example.callrecorder.ai.OpenAIService  getLet )com.example.callrecorder.ai.OpenAIService  	getListOf )com.example.callrecorder.ai.OpenAIService  getLowercase )com.example.callrecorder.ai.OpenAIService  getSupportedFormats )com.example.callrecorder.ai.OpenAIService  getTOMediaType )com.example.callrecorder.ai.OpenAIService  getToMediaType )com.example.callrecorder.ai.OpenAIService  getWITHContext )com.example.callrecorder.ai.OpenAIService  getWRITEText )com.example.callrecorder.ai.OpenAIService  getWithContext )com.example.callrecorder.ai.OpenAIService  getWriteText )com.example.callrecorder.ai.OpenAIService  
isNullOrBlank )com.example.callrecorder.ai.OpenAIService  java )com.example.callrecorder.ai.OpenAIService  let )com.example.callrecorder.ai.OpenAIService  listOf )com.example.callrecorder.ai.OpenAIService  	lowercase )com.example.callrecorder.ai.OpenAIService  toMediaType )com.example.callrecorder.ai.OpenAIService  transcribeAudio )com.example.callrecorder.ai.OpenAIService  withContext )com.example.callrecorder.ai.OpenAIService  	writeText )com.example.callrecorder.ai.OpenAIService  BASE_URL 3com.example.callrecorder.ai.OpenAIService.Companion  Boolean 3com.example.callrecorder.ai.OpenAIService.Companion  Context 3com.example.callrecorder.ai.OpenAIService.Companion  CostEstimate 3com.example.callrecorder.ai.OpenAIService.Companion  Dispatchers 3com.example.callrecorder.ai.OpenAIService.Companion  	Exception 3com.example.callrecorder.ai.OpenAIService.Companion  File 3com.example.callrecorder.ai.OpenAIService.Companion  GsonConverterFactory 3com.example.callrecorder.ai.OpenAIService.Companion  List 3com.example.callrecorder.ai.OpenAIService.Companion  Log 3com.example.callrecorder.ai.OpenAIService.Companion  
MAX_FILE_SIZE 3com.example.callrecorder.ai.OpenAIService.Companion  
MultipartBody 3com.example.callrecorder.ai.OpenAIService.Companion  OkHttpClient 3com.example.callrecorder.ai.OpenAIService.Companion  OpenAIApiService 3com.example.callrecorder.ai.OpenAIService.Companion  RequestBody 3com.example.callrecorder.ai.OpenAIService.Companion  Retrofit 3com.example.callrecorder.ai.OpenAIService.Companion  String 3com.example.callrecorder.ai.OpenAIService.Companion  TAG 3com.example.callrecorder.ai.OpenAIService.Companion  TIMEOUT_SECONDS 3com.example.callrecorder.ai.OpenAIService.Companion  TimeUnit 3com.example.callrecorder.ai.OpenAIService.Companion  TranscriptionResult 3com.example.callrecorder.ai.OpenAIService.Companion  apiKey 3com.example.callrecorder.ai.OpenAIService.Companion  
apiService 3com.example.callrecorder.ai.OpenAIService.Companion  
asRequestBody 3com.example.callrecorder.ai.OpenAIService.Companion  contains 3com.example.callrecorder.ai.OpenAIService.Companion  context 3com.example.callrecorder.ai.OpenAIService.Companion  	extension 3com.example.callrecorder.ai.OpenAIService.Companion  getASRequestBody 3com.example.callrecorder.ai.OpenAIService.Companion  getAsRequestBody 3com.example.callrecorder.ai.OpenAIService.Companion  getCONTAINS 3com.example.callrecorder.ai.OpenAIService.Companion  getContains 3com.example.callrecorder.ai.OpenAIService.Companion  getISNullOrBlank 3com.example.callrecorder.ai.OpenAIService.Companion  getIsNullOrBlank 3com.example.callrecorder.ai.OpenAIService.Companion  getLET 3com.example.callrecorder.ai.OpenAIService.Companion  	getLISTOf 3com.example.callrecorder.ai.OpenAIService.Companion  getLOWERCASE 3com.example.callrecorder.ai.OpenAIService.Companion  getLet 3com.example.callrecorder.ai.OpenAIService.Companion  	getListOf 3com.example.callrecorder.ai.OpenAIService.Companion  getLowercase 3com.example.callrecorder.ai.OpenAIService.Companion  getTOMediaType 3com.example.callrecorder.ai.OpenAIService.Companion  getToMediaType 3com.example.callrecorder.ai.OpenAIService.Companion  getWITHContext 3com.example.callrecorder.ai.OpenAIService.Companion  getWRITEText 3com.example.callrecorder.ai.OpenAIService.Companion  getWithContext 3com.example.callrecorder.ai.OpenAIService.Companion  getWriteText 3com.example.callrecorder.ai.OpenAIService.Companion  
isNullOrBlank 3com.example.callrecorder.ai.OpenAIService.Companion  java 3com.example.callrecorder.ai.OpenAIService.Companion  let 3com.example.callrecorder.ai.OpenAIService.Companion  listOf 3com.example.callrecorder.ai.OpenAIService.Companion  	lowercase 3com.example.callrecorder.ai.OpenAIService.Companion  toMediaType 3com.example.callrecorder.ai.OpenAIService.Companion  transcribeAudio 3com.example.callrecorder.ai.OpenAIService.Companion  withContext 3com.example.callrecorder.ai.OpenAIService.Companion  	writeText 3com.example.callrecorder.ai.OpenAIService.Companion  CallRecordingRepository 0com.example.callrecorder.ai.TranscriptionManager  Context 0com.example.callrecorder.ai.TranscriptionManager  	Exception 0com.example.callrecorder.ai.TranscriptionManager  File 0com.example.callrecorder.ai.TranscriptionManager  Log 0com.example.callrecorder.ai.TranscriptionManager  Long 0com.example.callrecorder.ai.TranscriptionManager  
OpenAIService 0com.example.callrecorder.ai.TranscriptionManager  TranscriptionQueueStatus 0com.example.callrecorder.ai.TranscriptionManager  TranscriptionResult 0com.example.callrecorder.ai.TranscriptionManager  com 0com.example.callrecorder.ai.TranscriptionManager  fileManager 0com.example.callrecorder.ai.TranscriptionManager  
getISNotEmpty 0com.example.callrecorder.ai.TranscriptionManager  
getIsNotEmpty 0com.example.callrecorder.ai.TranscriptionManager  getMUTABLEListOf 0com.example.callrecorder.ai.TranscriptionManager  getMutableListOf 0com.example.callrecorder.ai.TranscriptionManager  
isNotEmpty 0com.example.callrecorder.ai.TranscriptionManager  isProcessing 0com.example.callrecorder.ai.TranscriptionManager  
mutableListOf 0com.example.callrecorder.ai.TranscriptionManager  
openAIService 0com.example.callrecorder.ai.TranscriptionManager  processQueue 0com.example.callrecorder.ai.TranscriptionManager  
repository 0com.example.callrecorder.ai.TranscriptionManager  transcribeRecording 0com.example.callrecorder.ai.TranscriptionManager  transcriptionQueue 0com.example.callrecorder.ai.TranscriptionManager  Boolean 4com.example.callrecorder.ai.TranscriptionQueueStatus  Int 4com.example.callrecorder.ai.TranscriptionQueueStatus  Double 1com.example.callrecorder.ai.TranscriptionResponse  SerializedName 1com.example.callrecorder.ai.TranscriptionResponse  String 1com.example.callrecorder.ai.TranscriptionResponse  duration 1com.example.callrecorder.ai.TranscriptionResponse  equals 1com.example.callrecorder.ai.TranscriptionResponse  language 1com.example.callrecorder.ai.TranscriptionResponse  text 1com.example.callrecorder.ai.TranscriptionResponse  Double /com.example.callrecorder.ai.TranscriptionResult  Error /com.example.callrecorder.ai.TranscriptionResult  String /com.example.callrecorder.ai.TranscriptionResult  Success /com.example.callrecorder.ai.TranscriptionResult  TranscriptionResult /com.example.callrecorder.ai.TranscriptionResult  message /com.example.callrecorder.ai.TranscriptionResult  text /com.example.callrecorder.ai.TranscriptionResult  String 5com.example.callrecorder.ai.TranscriptionResult.Error  message 5com.example.callrecorder.ai.TranscriptionResult.Error  Double 7com.example.callrecorder.ai.TranscriptionResult.Success  String 7com.example.callrecorder.ai.TranscriptionResult.Success  text 7com.example.callrecorder.ai.TranscriptionResult.Success  AudioPlayerManager com.example.callrecorder.audio  Boolean com.example.callrecorder.audio  	Exception com.example.callrecorder.audio  Int com.example.callrecorder.audio  MediaPlayer com.example.callrecorder.audio  MutableStateFlow com.example.callrecorder.audio  
PlaybackState com.example.callrecorder.audio  Runnable com.example.callrecorder.audio  String com.example.callrecorder.audio  _currentFile com.example.callrecorder.audio  _currentPosition com.example.callrecorder.audio  	_duration com.example.callrecorder.audio  _playbackState com.example.callrecorder.audio  android com.example.callrecorder.audio  apply com.example.callrecorder.audio  asStateFlow com.example.callrecorder.audio  context com.example.callrecorder.audio  format com.example.callrecorder.audio  
formatTime com.example.callrecorder.audio  let com.example.callrecorder.audio  mediaPlayer com.example.callrecorder.audio  startPositionUpdates com.example.callrecorder.audio  stopPositionUpdates com.example.callrecorder.audio  Boolean 1com.example.callrecorder.audio.AudioPlayerManager  Context 1com.example.callrecorder.audio.AudioPlayerManager  	Exception 1com.example.callrecorder.audio.AudioPlayerManager  File 1com.example.callrecorder.audio.AudioPlayerManager  Int 1com.example.callrecorder.audio.AudioPlayerManager  MediaPlayer 1com.example.callrecorder.audio.AudioPlayerManager  MutableStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  
PlaybackState 1com.example.callrecorder.audio.AudioPlayerManager  Runnable 1com.example.callrecorder.audio.AudioPlayerManager  	StateFlow 1com.example.callrecorder.audio.AudioPlayerManager  String 1com.example.callrecorder.audio.AudioPlayerManager  Uri 1com.example.callrecorder.audio.AudioPlayerManager  _currentFile 1com.example.callrecorder.audio.AudioPlayerManager  _currentPosition 1com.example.callrecorder.audio.AudioPlayerManager  	_duration 1com.example.callrecorder.audio.AudioPlayerManager  _playbackState 1com.example.callrecorder.audio.AudioPlayerManager  android 1com.example.callrecorder.audio.AudioPlayerManager  apply 1com.example.callrecorder.audio.AudioPlayerManager  asStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  context 1com.example.callrecorder.audio.AudioPlayerManager  
getANDROID 1com.example.callrecorder.audio.AudioPlayerManager  getAPPLY 1com.example.callrecorder.audio.AudioPlayerManager  getASStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  
getAndroid 1com.example.callrecorder.audio.AudioPlayerManager  getApply 1com.example.callrecorder.audio.AudioPlayerManager  getAsStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  getLET 1com.example.callrecorder.audio.AudioPlayerManager  getLet 1com.example.callrecorder.audio.AudioPlayerManager  let 1com.example.callrecorder.audio.AudioPlayerManager  mediaPlayer 1com.example.callrecorder.audio.AudioPlayerManager  positionUpdateRunnable 1com.example.callrecorder.audio.AudioPlayerManager  startPositionUpdates 1com.example.callrecorder.audio.AudioPlayerManager  stop 1com.example.callrecorder.audio.AudioPlayerManager  stopPositionUpdates 1com.example.callrecorder.audio.AudioPlayerManager  
getANDROID Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  
getAndroid Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  getLET Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  getLet Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  getMEDIAPlayer Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  getMediaPlayer Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  get_currentPosition Ycom.example.callrecorder.audio.AudioPlayerManager.startPositionUpdates.<no name provided>  	COMPLETED ,com.example.callrecorder.audio.PlaybackState  ERROR ,com.example.callrecorder.audio.PlaybackState  IDLE ,com.example.callrecorder.audio.PlaybackState  PAUSED ,com.example.callrecorder.audio.PlaybackState  PLAYING ,com.example.callrecorder.audio.PlaybackState  	PREPARING ,com.example.callrecorder.audio.PlaybackState  equals ,com.example.callrecorder.audio.PlaybackState  Boolean com.example.callrecorder.data  
CallRecording com.example.callrecorder.data  CallRecordingDao com.example.callrecorder.data  CallRecordingDatabase com.example.callrecorder.data  CallRecordingDisplay com.example.callrecorder.data  CallRecordingRepository com.example.callrecorder.data  CallType com.example.callrecorder.data  
Converters com.example.callrecorder.data  Dao com.example.callrecorder.data  Date com.example.callrecorder.data  Delete com.example.callrecorder.data  Dispatchers com.example.callrecorder.data  InMemoryCallRecordingRepository com.example.callrecorder.data  Insert com.example.callrecorder.data  Int com.example.callrecorder.data  List com.example.callrecorder.data  Long com.example.callrecorder.data  OnConflictStrategy com.example.callrecorder.data  Query com.example.callrecorder.data  Room com.example.callrecorder.data  String com.example.callrecorder.data  Unit com.example.callrecorder.data  Update com.example.callrecorder.data  Volatile com.example.callrecorder.data  	emptyList com.example.callrecorder.data  find com.example.callrecorder.data  flowOf com.example.callrecorder.data  format com.example.callrecorder.data  formatDuration com.example.callrecorder.data  formatFileSize com.example.callrecorder.data  indexOfFirst com.example.callrecorder.data  java com.example.callrecorder.data  let com.example.callrecorder.data  map com.example.callrecorder.data  
mutableListOf com.example.callrecorder.data  nextId com.example.callrecorder.data  
recordings com.example.callrecorder.data  	removeAll com.example.callrecorder.data  synchronized com.example.callrecorder.data  withContext com.example.callrecorder.data  Boolean +com.example.callrecorder.data.CallRecording  CallRecordingDisplay +com.example.callrecorder.data.CallRecording  CallType +com.example.callrecorder.data.CallRecording  Date +com.example.callrecorder.data.CallRecording  Long +com.example.callrecorder.data.CallRecording  
PrimaryKey +com.example.callrecorder.data.CallRecording  String +com.example.callrecorder.data.CallRecording  callDate +com.example.callrecorder.data.CallRecording  callType +com.example.callrecorder.data.CallRecording  contactName +com.example.callrecorder.data.CallRecording  copy +com.example.callrecorder.data.CallRecording  	createdAt +com.example.callrecorder.data.CallRecording  duration +com.example.callrecorder.data.CallRecording  filePath +com.example.callrecorder.data.CallRecording  fileSize +com.example.callrecorder.data.CallRecording  formatDuration +com.example.callrecorder.data.CallRecording  formatFileSize +com.example.callrecorder.data.CallRecording  getFORMATDuration +com.example.callrecorder.data.CallRecording  getFORMATFileSize +com.example.callrecorder.data.CallRecording  getFormatDuration +com.example.callrecorder.data.CallRecording  getFormatFileSize +com.example.callrecorder.data.CallRecording  getTODisplayObject +com.example.callrecorder.data.CallRecording  getToDisplayObject +com.example.callrecorder.data.CallRecording  id +com.example.callrecorder.data.CallRecording  
isTranscribed +com.example.callrecorder.data.CallRecording  phoneNumber +com.example.callrecorder.data.CallRecording  recordingDuration +com.example.callrecorder.data.CallRecording  toDisplayObject +com.example.callrecorder.data.CallRecording  transcriptPath +com.example.callrecorder.data.CallRecording  Boolean .com.example.callrecorder.data.CallRecordingDao  
CallRecording .com.example.callrecorder.data.CallRecordingDao  CallType .com.example.callrecorder.data.CallRecordingDao  Date .com.example.callrecorder.data.CallRecordingDao  Delete .com.example.callrecorder.data.CallRecordingDao  Flow .com.example.callrecorder.data.CallRecordingDao  Insert .com.example.callrecorder.data.CallRecordingDao  Int .com.example.callrecorder.data.CallRecordingDao  List .com.example.callrecorder.data.CallRecordingDao  Long .com.example.callrecorder.data.CallRecordingDao  OnConflictStrategy .com.example.callrecorder.data.CallRecordingDao  Query .com.example.callrecorder.data.CallRecordingDao  String .com.example.callrecorder.data.CallRecordingDao  Update .com.example.callrecorder.data.CallRecordingDao  CallRecordingDao 3com.example.callrecorder.data.CallRecordingDatabase  CallRecordingDatabase 3com.example.callrecorder.data.CallRecordingDatabase  	Companion 3com.example.callrecorder.data.CallRecordingDatabase  Context 3com.example.callrecorder.data.CallRecordingDatabase  Room 3com.example.callrecorder.data.CallRecordingDatabase  Volatile 3com.example.callrecorder.data.CallRecordingDatabase  java 3com.example.callrecorder.data.CallRecordingDatabase  synchronized 3com.example.callrecorder.data.CallRecordingDatabase  CallRecordingDao =com.example.callrecorder.data.CallRecordingDatabase.Companion  CallRecordingDatabase =com.example.callrecorder.data.CallRecordingDatabase.Companion  Context =com.example.callrecorder.data.CallRecordingDatabase.Companion  INSTANCE =com.example.callrecorder.data.CallRecordingDatabase.Companion  Room =com.example.callrecorder.data.CallRecordingDatabase.Companion  Volatile =com.example.callrecorder.data.CallRecordingDatabase.Companion  getSYNCHRONIZED =com.example.callrecorder.data.CallRecordingDatabase.Companion  getSynchronized =com.example.callrecorder.data.CallRecordingDatabase.Companion  java =com.example.callrecorder.data.CallRecordingDatabase.Companion  synchronized =com.example.callrecorder.data.CallRecordingDatabase.Companion  Boolean 2com.example.callrecorder.data.CallRecordingDisplay  CallType 2com.example.callrecorder.data.CallRecordingDisplay  Date 2com.example.callrecorder.data.CallRecordingDisplay  Long 2com.example.callrecorder.data.CallRecordingDisplay  String 2com.example.callrecorder.data.CallRecordingDisplay  callDate 2com.example.callrecorder.data.CallRecordingDisplay  callType 2com.example.callrecorder.data.CallRecordingDisplay  contactName 2com.example.callrecorder.data.CallRecordingDisplay  duration 2com.example.callrecorder.data.CallRecordingDisplay  filePath 2com.example.callrecorder.data.CallRecordingDisplay  fileSize 2com.example.callrecorder.data.CallRecordingDisplay  id 2com.example.callrecorder.data.CallRecordingDisplay  
isTranscribed 2com.example.callrecorder.data.CallRecordingDisplay  phoneNumber 2com.example.callrecorder.data.CallRecordingDisplay  Boolean 5com.example.callrecorder.data.CallRecordingRepository  
CallRecording 5com.example.callrecorder.data.CallRecordingRepository  CallRecordingDisplay 5com.example.callrecorder.data.CallRecordingRepository  Context 5com.example.callrecorder.data.CallRecordingRepository  Flow 5com.example.callrecorder.data.CallRecordingRepository  List 5com.example.callrecorder.data.CallRecordingRepository  Long 5com.example.callrecorder.data.CallRecordingRepository  String 5com.example.callrecorder.data.CallRecordingRepository  deleteRecordingById 5com.example.callrecorder.data.CallRecordingRepository  	emptyList 5com.example.callrecorder.data.CallRecordingRepository  flowOf 5com.example.callrecorder.data.CallRecordingRepository  getAllRecordings 5com.example.callrecorder.data.CallRecordingRepository  getEMPTYList 5com.example.callrecorder.data.CallRecordingRepository  getEmptyList 5com.example.callrecorder.data.CallRecordingRepository  	getFLOWOf 5com.example.callrecorder.data.CallRecordingRepository  	getFlowOf 5com.example.callrecorder.data.CallRecordingRepository  getRecordingById 5com.example.callrecorder.data.CallRecordingRepository  insertRecording 5com.example.callrecorder.data.CallRecordingRepository  updateTranscriptionStatus 5com.example.callrecorder.data.CallRecordingRepository  INCOMING &com.example.callrecorder.data.CallType  MISSED &com.example.callrecorder.data.CallType  OUTGOING &com.example.callrecorder.data.CallType  name &com.example.callrecorder.data.CallType  valueOf &com.example.callrecorder.data.CallType  CallType (com.example.callrecorder.data.Converters  Date (com.example.callrecorder.data.Converters  Long (com.example.callrecorder.data.Converters  String (com.example.callrecorder.data.Converters  
TypeConverter (com.example.callrecorder.data.Converters  getLET (com.example.callrecorder.data.Converters  getLet (com.example.callrecorder.data.Converters  let (com.example.callrecorder.data.Converters  Boolean =com.example.callrecorder.data.InMemoryCallRecordingRepository  
CallRecording =com.example.callrecorder.data.InMemoryCallRecordingRepository  CallRecordingDisplay =com.example.callrecorder.data.InMemoryCallRecordingRepository  Date =com.example.callrecorder.data.InMemoryCallRecordingRepository  Dispatchers =com.example.callrecorder.data.InMemoryCallRecordingRepository  Flow =com.example.callrecorder.data.InMemoryCallRecordingRepository  List =com.example.callrecorder.data.InMemoryCallRecordingRepository  Long =com.example.callrecorder.data.InMemoryCallRecordingRepository  String =com.example.callrecorder.data.InMemoryCallRecordingRepository  Unit =com.example.callrecorder.data.InMemoryCallRecordingRepository  find =com.example.callrecorder.data.InMemoryCallRecordingRepository  flowOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  format =com.example.callrecorder.data.InMemoryCallRecordingRepository  formatDuration =com.example.callrecorder.data.InMemoryCallRecordingRepository  formatFileSize =com.example.callrecorder.data.InMemoryCallRecordingRepository  getFIND =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getFLOWOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getFORMAT =com.example.callrecorder.data.InMemoryCallRecordingRepository  getFind =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getFlowOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  	getFormat =com.example.callrecorder.data.InMemoryCallRecordingRepository  getINDEXOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getIndexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMAP =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMUTABLEListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMap =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getREMOVEAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  getRemoveAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWITHContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWithContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  indexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  map =com.example.callrecorder.data.InMemoryCallRecordingRepository  
mutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  nextId =com.example.callrecorder.data.InMemoryCallRecordingRepository  
recordings =com.example.callrecorder.data.InMemoryCallRecordingRepository  	removeAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  toDisplayObject =com.example.callrecorder.data.InMemoryCallRecordingRepository  withContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  
CallRecording com.example.callrecorder.domain  Long com.example.callrecorder.domain  String com.example.callrecorder.domain  Long -com.example.callrecorder.domain.CallRecording  String -com.example.callrecorder.domain.CallRecording  filePath -com.example.callrecorder.domain.CallRecording  id -com.example.callrecorder.domain.CallRecording  "ACCESSIBILITY_SERVICE_REQUEST_CODE #com.example.callrecorder.permission  ActivityCompat #com.example.callrecorder.permission  Array #com.example.callrecorder.permission  Boolean #com.example.callrecorder.permission  Build #com.example.callrecorder.permission  CORE_PERMISSIONS #com.example.callrecorder.permission  
ContextCompat #com.example.callrecorder.permission  Int #com.example.callrecorder.permission  IntArray #com.example.callrecorder.permission  Intent #com.example.callrecorder.permission  List #com.example.callrecorder.permission  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE #com.example.callrecorder.permission  Manifest #com.example.callrecorder.permission  NETWORK_PERMISSIONS #com.example.callrecorder.permission  PERMISSION_REQUEST_CODE #com.example.callrecorder.permission  PackageManager #com.example.callrecorder.permission  PermissionManager #com.example.callrecorder.permission  SERVICE_PERMISSIONS #com.example.callrecorder.permission  STORAGE_PERMISSIONS_LEGACY #com.example.callrecorder.permission   SYSTEM_ALERT_WINDOW_REQUEST_CODE #com.example.callrecorder.permission  Settings #com.example.callrecorder.permission  String #com.example.callrecorder.permission  Unit #com.example.callrecorder.permission  Uri #com.example.callrecorder.permission  addAll #com.example.callrecorder.permission  all #com.example.callrecorder.permission  android #com.example.callrecorder.permission  apply #com.example.callrecorder.permission  arrayOf #com.example.callrecorder.permission  contains #com.example.callrecorder.permission  context #com.example.callrecorder.permission  filter #com.example.callrecorder.permission  forEachIndexed #com.example.callrecorder.permission  
isNotEmpty #com.example.callrecorder.permission  
mutableListOf #com.example.callrecorder.permission  toTypedArray #com.example.callrecorder.permission  "ACCESSIBILITY_SERVICE_REQUEST_CODE 5com.example.callrecorder.permission.PermissionManager  Activity 5com.example.callrecorder.permission.PermissionManager  ActivityCompat 5com.example.callrecorder.permission.PermissionManager  Array 5com.example.callrecorder.permission.PermissionManager  Boolean 5com.example.callrecorder.permission.PermissionManager  Build 5com.example.callrecorder.permission.PermissionManager  CORE_PERMISSIONS 5com.example.callrecorder.permission.PermissionManager  Context 5com.example.callrecorder.permission.PermissionManager  
ContextCompat 5com.example.callrecorder.permission.PermissionManager  Int 5com.example.callrecorder.permission.PermissionManager  IntArray 5com.example.callrecorder.permission.PermissionManager  Intent 5com.example.callrecorder.permission.PermissionManager  List 5com.example.callrecorder.permission.PermissionManager  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE 5com.example.callrecorder.permission.PermissionManager  Manifest 5com.example.callrecorder.permission.PermissionManager  NETWORK_PERMISSIONS 5com.example.callrecorder.permission.PermissionManager  PERMISSION_REQUEST_CODE 5com.example.callrecorder.permission.PermissionManager  PackageManager 5com.example.callrecorder.permission.PermissionManager  SERVICE_PERMISSIONS 5com.example.callrecorder.permission.PermissionManager  STORAGE_PERMISSIONS_LEGACY 5com.example.callrecorder.permission.PermissionManager   SYSTEM_ALERT_WINDOW_REQUEST_CODE 5com.example.callrecorder.permission.PermissionManager  Settings 5com.example.callrecorder.permission.PermissionManager  String 5com.example.callrecorder.permission.PermissionManager  Unit 5com.example.callrecorder.permission.PermissionManager  Uri 5com.example.callrecorder.permission.PermissionManager  addAll 5com.example.callrecorder.permission.PermissionManager  all 5com.example.callrecorder.permission.PermissionManager  android 5com.example.callrecorder.permission.PermissionManager  apply 5com.example.callrecorder.permission.PermissionManager  areAllPermissionsGranted 5com.example.callrecorder.permission.PermissionManager  arrayOf 5com.example.callrecorder.permission.PermissionManager  contains 5com.example.callrecorder.permission.PermissionManager  context 5com.example.callrecorder.permission.PermissionManager  filter 5com.example.callrecorder.permission.PermissionManager  forEachIndexed 5com.example.callrecorder.permission.PermissionManager  	getADDAll 5com.example.callrecorder.permission.PermissionManager  getALL 5com.example.callrecorder.permission.PermissionManager  
getANDROID 5com.example.callrecorder.permission.PermissionManager  getAPPLY 5com.example.callrecorder.permission.PermissionManager  	getAddAll 5com.example.callrecorder.permission.PermissionManager  getAll 5com.example.callrecorder.permission.PermissionManager  
getAndroid 5com.example.callrecorder.permission.PermissionManager  getApply 5com.example.callrecorder.permission.PermissionManager  getCONTAINS 5com.example.callrecorder.permission.PermissionManager  getContains 5com.example.callrecorder.permission.PermissionManager  	getFILTER 5com.example.callrecorder.permission.PermissionManager  getFOREachIndexed 5com.example.callrecorder.permission.PermissionManager  	getFilter 5com.example.callrecorder.permission.PermissionManager  getForEachIndexed 5com.example.callrecorder.permission.PermissionManager  
getISNotEmpty 5com.example.callrecorder.permission.PermissionManager  
getIsNotEmpty 5com.example.callrecorder.permission.PermissionManager  getMUTABLEListOf 5com.example.callrecorder.permission.PermissionManager  getMissingPermissions 5com.example.callrecorder.permission.PermissionManager  getMutableListOf 5com.example.callrecorder.permission.PermissionManager  getPermissionExplanation 5com.example.callrecorder.permission.PermissionManager  getRequiredPermissions 5com.example.callrecorder.permission.PermissionManager  getTOTypedArray 5com.example.callrecorder.permission.PermissionManager  getToTypedArray 5com.example.callrecorder.permission.PermissionManager  isAccessibilityServiceEnabled 5com.example.callrecorder.permission.PermissionManager  isManageExternalStorageGranted 5com.example.callrecorder.permission.PermissionManager  
isNotEmpty 5com.example.callrecorder.permission.PermissionManager  isSystemAlertWindowGranted 5com.example.callrecorder.permission.PermissionManager  
mutableListOf 5com.example.callrecorder.permission.PermissionManager  openAccessibilitySettings 5com.example.callrecorder.permission.PermissionManager  requestManageExternalStorage 5com.example.callrecorder.permission.PermissionManager  requestMissingPermissions 5com.example.callrecorder.permission.PermissionManager  requestSystemAlertWindow 5com.example.callrecorder.permission.PermissionManager  toTypedArray 5com.example.callrecorder.permission.PermissionManager  "ACCESSIBILITY_SERVICE_REQUEST_CODE ?com.example.callrecorder.permission.PermissionManager.Companion  Activity ?com.example.callrecorder.permission.PermissionManager.Companion  ActivityCompat ?com.example.callrecorder.permission.PermissionManager.Companion  Array ?com.example.callrecorder.permission.PermissionManager.Companion  Boolean ?com.example.callrecorder.permission.PermissionManager.Companion  Build ?com.example.callrecorder.permission.PermissionManager.Companion  CORE_PERMISSIONS ?com.example.callrecorder.permission.PermissionManager.Companion  Context ?com.example.callrecorder.permission.PermissionManager.Companion  
ContextCompat ?com.example.callrecorder.permission.PermissionManager.Companion  Int ?com.example.callrecorder.permission.PermissionManager.Companion  IntArray ?com.example.callrecorder.permission.PermissionManager.Companion  Intent ?com.example.callrecorder.permission.PermissionManager.Companion  List ?com.example.callrecorder.permission.PermissionManager.Companion  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE ?com.example.callrecorder.permission.PermissionManager.Companion  Manifest ?com.example.callrecorder.permission.PermissionManager.Companion  NETWORK_PERMISSIONS ?com.example.callrecorder.permission.PermissionManager.Companion  PERMISSION_REQUEST_CODE ?com.example.callrecorder.permission.PermissionManager.Companion  PackageManager ?com.example.callrecorder.permission.PermissionManager.Companion  SERVICE_PERMISSIONS ?com.example.callrecorder.permission.PermissionManager.Companion  STORAGE_PERMISSIONS_LEGACY ?com.example.callrecorder.permission.PermissionManager.Companion   SYSTEM_ALERT_WINDOW_REQUEST_CODE ?com.example.callrecorder.permission.PermissionManager.Companion  Settings ?com.example.callrecorder.permission.PermissionManager.Companion  String ?com.example.callrecorder.permission.PermissionManager.Companion  Unit ?com.example.callrecorder.permission.PermissionManager.Companion  Uri ?com.example.callrecorder.permission.PermissionManager.Companion  addAll ?com.example.callrecorder.permission.PermissionManager.Companion  all ?com.example.callrecorder.permission.PermissionManager.Companion  android ?com.example.callrecorder.permission.PermissionManager.Companion  apply ?com.example.callrecorder.permission.PermissionManager.Companion  arrayOf ?com.example.callrecorder.permission.PermissionManager.Companion  contains ?com.example.callrecorder.permission.PermissionManager.Companion  context ?com.example.callrecorder.permission.PermissionManager.Companion  filter ?com.example.callrecorder.permission.PermissionManager.Companion  forEachIndexed ?com.example.callrecorder.permission.PermissionManager.Companion  	getADDAll ?com.example.callrecorder.permission.PermissionManager.Companion  getALL ?com.example.callrecorder.permission.PermissionManager.Companion  
getANDROID ?com.example.callrecorder.permission.PermissionManager.Companion  getAPPLY ?com.example.callrecorder.permission.PermissionManager.Companion  
getARRAYOf ?com.example.callrecorder.permission.PermissionManager.Companion  	getAddAll ?com.example.callrecorder.permission.PermissionManager.Companion  getAll ?com.example.callrecorder.permission.PermissionManager.Companion  
getAndroid ?com.example.callrecorder.permission.PermissionManager.Companion  getApply ?com.example.callrecorder.permission.PermissionManager.Companion  
getArrayOf ?com.example.callrecorder.permission.PermissionManager.Companion  getCONTAINS ?com.example.callrecorder.permission.PermissionManager.Companion  getContains ?com.example.callrecorder.permission.PermissionManager.Companion  	getFILTER ?com.example.callrecorder.permission.PermissionManager.Companion  getFOREachIndexed ?com.example.callrecorder.permission.PermissionManager.Companion  	getFilter ?com.example.callrecorder.permission.PermissionManager.Companion  getForEachIndexed ?com.example.callrecorder.permission.PermissionManager.Companion  
getISNotEmpty ?com.example.callrecorder.permission.PermissionManager.Companion  
getIsNotEmpty ?com.example.callrecorder.permission.PermissionManager.Companion  getMUTABLEListOf ?com.example.callrecorder.permission.PermissionManager.Companion  getMutableListOf ?com.example.callrecorder.permission.PermissionManager.Companion  getTOTypedArray ?com.example.callrecorder.permission.PermissionManager.Companion  getToTypedArray ?com.example.callrecorder.permission.PermissionManager.Companion  invoke ?com.example.callrecorder.permission.PermissionManager.Companion  
isNotEmpty ?com.example.callrecorder.permission.PermissionManager.Companion  
mutableListOf ?com.example.callrecorder.permission.PermissionManager.Companion  toTypedArray ?com.example.callrecorder.permission.PermissionManager.Companion  ACTION_START_RECORDING  com.example.callrecorder.service  ACTION_STOP_RECORDING  com.example.callrecorder.service  Activity  com.example.callrecorder.service  AudioFormat  com.example.callrecorder.service  AudioRecord  com.example.callrecorder.service  Boolean  com.example.callrecorder.service  Build  com.example.callrecorder.service  	ByteArray  com.example.callrecorder.service  
CHANNEL_ID  com.example.callrecorder.service  CallAccessibilityService  com.example.callrecorder.service  CallDetectionService  com.example.callrecorder.service  CallLog  com.example.callrecorder.service  
CallRecording  com.example.callrecorder.service  CallRecordingRepository  com.example.callrecorder.service  CallRecordingService  com.example.callrecorder.service  CallType  com.example.callrecorder.service  Context  com.example.callrecorder.service  CoroutineScope  com.example.callrecorder.service  Date  com.example.callrecorder.service  Dispatchers  com.example.callrecorder.service  EXTRA_CALL_TYPE  com.example.callrecorder.service  EXTRA_CONTACT_NAME  com.example.callrecorder.service  EXTRA_MEDIA_PROJECTION_DATA  com.example.callrecorder.service  EXTRA_PHONE_NUMBER  com.example.callrecorder.service  	Exception  com.example.callrecorder.service  File  com.example.callrecorder.service  FileOutputStream  com.example.callrecorder.service  Handler  com.example.callrecorder.service  IllegalStateException  com.example.callrecorder.service  Int  com.example.callrecorder.service  Intent  com.example.callrecorder.service  IntentFilter  com.example.callrecorder.service  Job  com.example.callrecorder.service  Locale  com.example.callrecorder.service  Log  com.example.callrecorder.service  Long  com.example.callrecorder.service  Looper  com.example.callrecorder.service  MediaProjectionHelper  com.example.callrecorder.service  
MediaRecorder  com.example.callrecorder.service  NOTIFICATION_ID  com.example.callrecorder.service  Notification  com.example.callrecorder.service  NotificationChannel  com.example.callrecorder.service  NotificationCompat  com.example.callrecorder.service  NotificationManager  com.example.callrecorder.service  
PendingIntent  com.example.callrecorder.service  PermissionManager  com.example.callrecorder.service  PhoneStateListener  com.example.callrecorder.service  PowerManager  com.example.callrecorder.service  R  com.example.callrecorder.service  START_STICKY  com.example.callrecorder.service  Service  com.example.callrecorder.service  SimpleDateFormat  com.example.callrecorder.service  String  com.example.callrecorder.service  
SupervisorJob  com.example.callrecorder.service  System  com.example.callrecorder.service  TAG  com.example.callrecorder.service  TelephonyManager  com.example.callrecorder.service  Thread  com.example.callrecorder.service  Unit  com.example.callrecorder.service  Uri  com.example.callrecorder.service  android  com.example.callrecorder.service  apply  com.example.callrecorder.service  arrayOf  com.example.callrecorder.service  audioRecord  com.example.callrecorder.service  cancel  com.example.callrecorder.service  currentRecordingFile  com.example.callrecorder.service  handleCallLogChange  com.example.callrecorder.service  handleCallStateChange  com.example.callrecorder.service  handleOutgoingCall  com.example.callrecorder.service  isRecording  com.example.callrecorder.service  java  com.example.callrecorder.service  launch  com.example.callrecorder.service  let  com.example.callrecorder.service  mediaProjection  com.example.callrecorder.service  recordingStartTime  com.example.callrecorder.service  
repository  com.example.callrecorder.service  serviceScope  com.example.callrecorder.service  
stopRecording  com.example.callrecorder.service  use  com.example.callrecorder.service  AccessibilityEvent 9com.example.callrecorder.service.CallAccessibilityService  Boolean 5com.example.callrecorder.service.CallDetectionService  BroadcastReceiver 5com.example.callrecorder.service.CallDetectionService  CallLog 5com.example.callrecorder.service.CallDetectionService  CallType 5com.example.callrecorder.service.CallDetectionService  ContentObserver 5com.example.callrecorder.service.CallDetectionService  Context 5com.example.callrecorder.service.CallDetectionService  CoroutineScope 5com.example.callrecorder.service.CallDetectionService  Dispatchers 5com.example.callrecorder.service.CallDetectionService  	Exception 5com.example.callrecorder.service.CallDetectionService  Handler 5com.example.callrecorder.service.CallDetectionService  Int 5com.example.callrecorder.service.CallDetectionService  Intent 5com.example.callrecorder.service.CallDetectionService  IntentFilter 5com.example.callrecorder.service.CallDetectionService  Log 5com.example.callrecorder.service.CallDetectionService  Long 5com.example.callrecorder.service.CallDetectionService  Looper 5com.example.callrecorder.service.CallDetectionService  PermissionManager 5com.example.callrecorder.service.CallDetectionService  PhoneStateListener 5com.example.callrecorder.service.CallDetectionService  String 5com.example.callrecorder.service.CallDetectionService  
SupervisorJob 5com.example.callrecorder.service.CallDetectionService  System 5com.example.callrecorder.service.CallDetectionService  TAG 5com.example.callrecorder.service.CallDetectionService  TelephonyManager 5com.example.callrecorder.service.CallDetectionService  Unit 5com.example.callrecorder.service.CallDetectionService  Uri 5com.example.callrecorder.service.CallDetectionService  android 5com.example.callrecorder.service.CallDetectionService  arrayOf 5com.example.callrecorder.service.CallDetectionService  callLogObserver 5com.example.callrecorder.service.CallDetectionService  
callStartTime 5com.example.callrecorder.service.CallDetectionService  cancel 5com.example.callrecorder.service.CallDetectionService  context 5com.example.callrecorder.service.CallDetectionService  currentPhoneNumber 5com.example.callrecorder.service.CallDetectionService  
getANDROID 5com.example.callrecorder.service.CallDetectionService  
getARRAYOf 5com.example.callrecorder.service.CallDetectionService  
getAndroid 5com.example.callrecorder.service.CallDetectionService  
getArrayOf 5com.example.callrecorder.service.CallDetectionService  	getCANCEL 5com.example.callrecorder.service.CallDetectionService  	getCancel 5com.example.callrecorder.service.CallDetectionService  getContactName 5com.example.callrecorder.service.CallDetectionService  	getLAUNCH 5com.example.callrecorder.service.CallDetectionService  getLET 5com.example.callrecorder.service.CallDetectionService  	getLaunch 5com.example.callrecorder.service.CallDetectionService  getLet 5com.example.callrecorder.service.CallDetectionService  getUSE 5com.example.callrecorder.service.CallDetectionService  getUse 5com.example.callrecorder.service.CallDetectionService  handleCallLogChange 5com.example.callrecorder.service.CallDetectionService  handleCallStateChange 5com.example.callrecorder.service.CallDetectionService  handleOutgoingCall 5com.example.callrecorder.service.CallDetectionService  invoke 5com.example.callrecorder.service.CallDetectionService  isListening 5com.example.callrecorder.service.CallDetectionService  
lastCallState 5com.example.callrecorder.service.CallDetectionService  launch 5com.example.callrecorder.service.CallDetectionService  let 5com.example.callrecorder.service.CallDetectionService  onCallDetected 5com.example.callrecorder.service.CallDetectionService  onCallEnded 5com.example.callrecorder.service.CallDetectionService  outgoingCallReceiver 5com.example.callrecorder.service.CallDetectionService  phoneStateListener 5com.example.callrecorder.service.CallDetectionService  serviceScope 5com.example.callrecorder.service.CallDetectionService  setupCallLogObserver 5com.example.callrecorder.service.CallDetectionService  setupOutgoingCallReceiver 5com.example.callrecorder.service.CallDetectionService  setupPhoneStateListener 5com.example.callrecorder.service.CallDetectionService  
stopListening 5com.example.callrecorder.service.CallDetectionService  telephonyManager 5com.example.callrecorder.service.CallDetectionService  use 5com.example.callrecorder.service.CallDetectionService  Boolean ?com.example.callrecorder.service.CallDetectionService.Companion  BroadcastReceiver ?com.example.callrecorder.service.CallDetectionService.Companion  CallLog ?com.example.callrecorder.service.CallDetectionService.Companion  CallType ?com.example.callrecorder.service.CallDetectionService.Companion  ContentObserver ?com.example.callrecorder.service.CallDetectionService.Companion  Context ?com.example.callrecorder.service.CallDetectionService.Companion  CoroutineScope ?com.example.callrecorder.service.CallDetectionService.Companion  Dispatchers ?com.example.callrecorder.service.CallDetectionService.Companion  	Exception ?com.example.callrecorder.service.CallDetectionService.Companion  Handler ?com.example.callrecorder.service.CallDetectionService.Companion  Int ?com.example.callrecorder.service.CallDetectionService.Companion  Intent ?com.example.callrecorder.service.CallDetectionService.Companion  IntentFilter ?com.example.callrecorder.service.CallDetectionService.Companion  Log ?com.example.callrecorder.service.CallDetectionService.Companion  Long ?com.example.callrecorder.service.CallDetectionService.Companion  Looper ?com.example.callrecorder.service.CallDetectionService.Companion  PermissionManager ?com.example.callrecorder.service.CallDetectionService.Companion  PhoneStateListener ?com.example.callrecorder.service.CallDetectionService.Companion  String ?com.example.callrecorder.service.CallDetectionService.Companion  
SupervisorJob ?com.example.callrecorder.service.CallDetectionService.Companion  System ?com.example.callrecorder.service.CallDetectionService.Companion  TAG ?com.example.callrecorder.service.CallDetectionService.Companion  TelephonyManager ?com.example.callrecorder.service.CallDetectionService.Companion  Unit ?com.example.callrecorder.service.CallDetectionService.Companion  Uri ?com.example.callrecorder.service.CallDetectionService.Companion  android ?com.example.callrecorder.service.CallDetectionService.Companion  arrayOf ?com.example.callrecorder.service.CallDetectionService.Companion  cancel ?com.example.callrecorder.service.CallDetectionService.Companion  
getANDROID ?com.example.callrecorder.service.CallDetectionService.Companion  
getARRAYOf ?com.example.callrecorder.service.CallDetectionService.Companion  
getAndroid ?com.example.callrecorder.service.CallDetectionService.Companion  
getArrayOf ?com.example.callrecorder.service.CallDetectionService.Companion  	getCANCEL ?com.example.callrecorder.service.CallDetectionService.Companion  	getCancel ?com.example.callrecorder.service.CallDetectionService.Companion  	getLAUNCH ?com.example.callrecorder.service.CallDetectionService.Companion  getLET ?com.example.callrecorder.service.CallDetectionService.Companion  	getLaunch ?com.example.callrecorder.service.CallDetectionService.Companion  getLet ?com.example.callrecorder.service.CallDetectionService.Companion  getUSE ?com.example.callrecorder.service.CallDetectionService.Companion  getUse ?com.example.callrecorder.service.CallDetectionService.Companion  handleCallLogChange ?com.example.callrecorder.service.CallDetectionService.Companion  handleCallStateChange ?com.example.callrecorder.service.CallDetectionService.Companion  handleOutgoingCall ?com.example.callrecorder.service.CallDetectionService.Companion  invoke ?com.example.callrecorder.service.CallDetectionService.Companion  launch ?com.example.callrecorder.service.CallDetectionService.Companion  let ?com.example.callrecorder.service.CallDetectionService.Companion  serviceScope ?com.example.callrecorder.service.CallDetectionService.Companion  use ?com.example.callrecorder.service.CallDetectionService.Companion  getHANDLECallLogChange ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  getHandleCallLogChange ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  	getLAUNCH ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  	getLaunch ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  getSERVICEScope ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  getServiceScope ]com.example.callrecorder.service.CallDetectionService.setupCallLogObserver.<no name provided>  getHANDLEOutgoingCall bcom.example.callrecorder.service.CallDetectionService.setupOutgoingCallReceiver.<no name provided>  getHandleOutgoingCall bcom.example.callrecorder.service.CallDetectionService.setupOutgoingCallReceiver.<no name provided>  getLET bcom.example.callrecorder.service.CallDetectionService.setupOutgoingCallReceiver.<no name provided>  getLet bcom.example.callrecorder.service.CallDetectionService.setupOutgoingCallReceiver.<no name provided>  getHANDLECallStateChange `com.example.callrecorder.service.CallDetectionService.setupPhoneStateListener.<no name provided>  getHandleCallStateChange `com.example.callrecorder.service.CallDetectionService.setupPhoneStateListener.<no name provided>  ACTION_START_RECORDING 5com.example.callrecorder.service.CallRecordingService  ACTION_STOP_RECORDING 5com.example.callrecorder.service.CallRecordingService  Activity 5com.example.callrecorder.service.CallRecordingService  AudioFormat 5com.example.callrecorder.service.CallRecordingService  AudioRecord 5com.example.callrecorder.service.CallRecordingService  Build 5com.example.callrecorder.service.CallRecordingService  	ByteArray 5com.example.callrecorder.service.CallRecordingService  
CHANNEL_ID 5com.example.callrecorder.service.CallRecordingService  
CallRecording 5com.example.callrecorder.service.CallRecordingService  CallRecordingRepository 5com.example.callrecorder.service.CallRecordingService  CallRecordingService 5com.example.callrecorder.service.CallRecordingService  CallType 5com.example.callrecorder.service.CallRecordingService  	Companion 5com.example.callrecorder.service.CallRecordingService  Context 5com.example.callrecorder.service.CallRecordingService  CoroutineScope 5com.example.callrecorder.service.CallRecordingService  Date 5com.example.callrecorder.service.CallRecordingService  Dispatchers 5com.example.callrecorder.service.CallRecordingService  EXTRA_CALL_TYPE 5com.example.callrecorder.service.CallRecordingService  EXTRA_CONTACT_NAME 5com.example.callrecorder.service.CallRecordingService  EXTRA_MEDIA_PROJECTION_DATA 5com.example.callrecorder.service.CallRecordingService  EXTRA_PHONE_NUMBER 5com.example.callrecorder.service.CallRecordingService  	Exception 5com.example.callrecorder.service.CallRecordingService  File 5com.example.callrecorder.service.CallRecordingService  FileOutputStream 5com.example.callrecorder.service.CallRecordingService  IBinder 5com.example.callrecorder.service.CallRecordingService  IllegalStateException 5com.example.callrecorder.service.CallRecordingService  Int 5com.example.callrecorder.service.CallRecordingService  Intent 5com.example.callrecorder.service.CallRecordingService  Job 5com.example.callrecorder.service.CallRecordingService  Locale 5com.example.callrecorder.service.CallRecordingService  Long 5com.example.callrecorder.service.CallRecordingService  MediaProjection 5com.example.callrecorder.service.CallRecordingService  MediaProjectionManager 5com.example.callrecorder.service.CallRecordingService  
MediaRecorder 5com.example.callrecorder.service.CallRecordingService  NOTIFICATION_ID 5com.example.callrecorder.service.CallRecordingService  Notification 5com.example.callrecorder.service.CallRecordingService  NotificationChannel 5com.example.callrecorder.service.CallRecordingService  NotificationCompat 5com.example.callrecorder.service.CallRecordingService  NotificationManager 5com.example.callrecorder.service.CallRecordingService  
PendingIntent 5com.example.callrecorder.service.CallRecordingService  PowerManager 5com.example.callrecorder.service.CallRecordingService  R 5com.example.callrecorder.service.CallRecordingService  START_STICKY 5com.example.callrecorder.service.CallRecordingService  SimpleDateFormat 5com.example.callrecorder.service.CallRecordingService  String 5com.example.callrecorder.service.CallRecordingService  
SupervisorJob 5com.example.callrecorder.service.CallRecordingService  System 5com.example.callrecorder.service.CallRecordingService  Thread 5com.example.callrecorder.service.CallRecordingService  acquireWakeLock 5com.example.callrecorder.service.CallRecordingService  apply 5com.example.callrecorder.service.CallRecordingService  audioRecord 5com.example.callrecorder.service.CallRecordingService  cancel 5com.example.callrecorder.service.CallRecordingService  createNotificationChannel 5com.example.callrecorder.service.CallRecordingService  createRecordingFile 5com.example.callrecorder.service.CallRecordingService  createRecordingNotification 5com.example.callrecorder.service.CallRecordingService  currentRecordingFile 5com.example.callrecorder.service.CallRecordingService  getAPPLY 5com.example.callrecorder.service.CallRecordingService  getApply 5com.example.callrecorder.service.CallRecordingService  	getCANCEL 5com.example.callrecorder.service.CallRecordingService  	getCancel 5com.example.callrecorder.service.CallRecordingService  getExternalFilesDir 5com.example.callrecorder.service.CallRecordingService  	getLAUNCH 5com.example.callrecorder.service.CallRecordingService  getLET 5com.example.callrecorder.service.CallRecordingService  	getLaunch 5com.example.callrecorder.service.CallRecordingService  getLet 5com.example.callrecorder.service.CallRecordingService  getSystemService 5com.example.callrecorder.service.CallRecordingService  isRecording 5com.example.callrecorder.service.CallRecordingService  java 5com.example.callrecorder.service.CallRecordingService  launch 5com.example.callrecorder.service.CallRecordingService  let 5com.example.callrecorder.service.CallRecordingService  mediaProjection 5com.example.callrecorder.service.CallRecordingService  recordingJob 5com.example.callrecorder.service.CallRecordingService  recordingStartTime 5com.example.callrecorder.service.CallRecordingService  
repository 5com.example.callrecorder.service.CallRecordingService  saveRecordingToDatabase 5com.example.callrecorder.service.CallRecordingService  serviceScope 5com.example.callrecorder.service.CallRecordingService  startAudioRecording 5com.example.callrecorder.service.CallRecordingService  startForeground 5com.example.callrecorder.service.CallRecordingService  startRecording 5com.example.callrecorder.service.CallRecordingService  stopForeground 5com.example.callrecorder.service.CallRecordingService  
stopRecording 5com.example.callrecorder.service.CallRecordingService  stopSelf 5com.example.callrecorder.service.CallRecordingService  wakeLock 5com.example.callrecorder.service.CallRecordingService  ACTION_START_RECORDING ?com.example.callrecorder.service.CallRecordingService.Companion  ACTION_STOP_RECORDING ?com.example.callrecorder.service.CallRecordingService.Companion  Activity ?com.example.callrecorder.service.CallRecordingService.Companion  AudioFormat ?com.example.callrecorder.service.CallRecordingService.Companion  AudioRecord ?com.example.callrecorder.service.CallRecordingService.Companion  Build ?com.example.callrecorder.service.CallRecordingService.Companion  	ByteArray ?com.example.callrecorder.service.CallRecordingService.Companion  
CHANNEL_ID ?com.example.callrecorder.service.CallRecordingService.Companion  
CallRecording ?com.example.callrecorder.service.CallRecordingService.Companion  CallRecordingRepository ?com.example.callrecorder.service.CallRecordingService.Companion  CallRecordingService ?com.example.callrecorder.service.CallRecordingService.Companion  CallType ?com.example.callrecorder.service.CallRecordingService.Companion  Context ?com.example.callrecorder.service.CallRecordingService.Companion  CoroutineScope ?com.example.callrecorder.service.CallRecordingService.Companion  Date ?com.example.callrecorder.service.CallRecordingService.Companion  Dispatchers ?com.example.callrecorder.service.CallRecordingService.Companion  EXTRA_CALL_TYPE ?com.example.callrecorder.service.CallRecordingService.Companion  EXTRA_CONTACT_NAME ?com.example.callrecorder.service.CallRecordingService.Companion  EXTRA_MEDIA_PROJECTION_DATA ?com.example.callrecorder.service.CallRecordingService.Companion  EXTRA_PHONE_NUMBER ?com.example.callrecorder.service.CallRecordingService.Companion  	Exception ?com.example.callrecorder.service.CallRecordingService.Companion  File ?com.example.callrecorder.service.CallRecordingService.Companion  FileOutputStream ?com.example.callrecorder.service.CallRecordingService.Companion  IBinder ?com.example.callrecorder.service.CallRecordingService.Companion  IllegalStateException ?com.example.callrecorder.service.CallRecordingService.Companion  Int ?com.example.callrecorder.service.CallRecordingService.Companion  Intent ?com.example.callrecorder.service.CallRecordingService.Companion  Job ?com.example.callrecorder.service.CallRecordingService.Companion  Locale ?com.example.callrecorder.service.CallRecordingService.Companion  Long ?com.example.callrecorder.service.CallRecordingService.Companion  MediaProjection ?com.example.callrecorder.service.CallRecordingService.Companion  MediaProjectionManager ?com.example.callrecorder.service.CallRecordingService.Companion  
MediaRecorder ?com.example.callrecorder.service.CallRecordingService.Companion  NOTIFICATION_ID ?com.example.callrecorder.service.CallRecordingService.Companion  Notification ?com.example.callrecorder.service.CallRecordingService.Companion  NotificationChannel ?com.example.callrecorder.service.CallRecordingService.Companion  NotificationCompat ?com.example.callrecorder.service.CallRecordingService.Companion  NotificationManager ?com.example.callrecorder.service.CallRecordingService.Companion  
PendingIntent ?com.example.callrecorder.service.CallRecordingService.Companion  PowerManager ?com.example.callrecorder.service.CallRecordingService.Companion  R ?com.example.callrecorder.service.CallRecordingService.Companion  START_STICKY ?com.example.callrecorder.service.CallRecordingService.Companion  SimpleDateFormat ?com.example.callrecorder.service.CallRecordingService.Companion  String ?com.example.callrecorder.service.CallRecordingService.Companion  
SupervisorJob ?com.example.callrecorder.service.CallRecordingService.Companion  System ?com.example.callrecorder.service.CallRecordingService.Companion  Thread ?com.example.callrecorder.service.CallRecordingService.Companion  apply ?com.example.callrecorder.service.CallRecordingService.Companion  audioRecord ?com.example.callrecorder.service.CallRecordingService.Companion  cancel ?com.example.callrecorder.service.CallRecordingService.Companion  currentRecordingFile ?com.example.callrecorder.service.CallRecordingService.Companion  getAPPLY ?com.example.callrecorder.service.CallRecordingService.Companion  getApply ?com.example.callrecorder.service.CallRecordingService.Companion  	getCANCEL ?com.example.callrecorder.service.CallRecordingService.Companion  	getCancel ?com.example.callrecorder.service.CallRecordingService.Companion  	getLAUNCH ?com.example.callrecorder.service.CallRecordingService.Companion  getLET ?com.example.callrecorder.service.CallRecordingService.Companion  	getLaunch ?com.example.callrecorder.service.CallRecordingService.Companion  getLet ?com.example.callrecorder.service.CallRecordingService.Companion  isRecording ?com.example.callrecorder.service.CallRecordingService.Companion  java ?com.example.callrecorder.service.CallRecordingService.Companion  launch ?com.example.callrecorder.service.CallRecordingService.Companion  let ?com.example.callrecorder.service.CallRecordingService.Companion  mediaProjection ?com.example.callrecorder.service.CallRecordingService.Companion  recordingStartTime ?com.example.callrecorder.service.CallRecordingService.Companion  
repository ?com.example.callrecorder.service.CallRecordingService.Companion  
stopRecording ?com.example.callrecorder.service.CallRecordingService.Companion  Activity 6com.example.callrecorder.service.MediaProjectionHelper  ActivityResultLauncher 6com.example.callrecorder.service.MediaProjectionHelper  Application 6com.example.callrecorder.service.MediaProjectionHelper  Build 6com.example.callrecorder.service.MediaProjectionHelper  Context 6com.example.callrecorder.service.MediaProjectionHelper  Int 6com.example.callrecorder.service.MediaProjectionHelper  Intent 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjection 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjectionManager 6com.example.callrecorder.service.MediaProjectionHelper  getLET 6com.example.callrecorder.service.MediaProjectionHelper  getLet 6com.example.callrecorder.service.MediaProjectionHelper  let 6com.example.callrecorder.service.MediaProjectionHelper  mediaProjection 6com.example.callrecorder.service.MediaProjectionHelper  mediaProjectionManager 6com.example.callrecorder.service.MediaProjectionHelper  AUDIO_EXTENSION  com.example.callrecorder.storage  AUDIO_MIME_TYPE  com.example.callrecorder.storage  Boolean  com.example.callrecorder.storage  Build  com.example.callrecorder.storage  CallType  com.example.callrecorder.storage  Date  com.example.callrecorder.storage  Environment  com.example.callrecorder.storage  	Exception  com.example.callrecorder.storage  File  com.example.callrecorder.storage  FileManager  com.example.callrecorder.storage  FileProvider  com.example.callrecorder.storage  Int  com.example.callrecorder.storage  Intent  com.example.callrecorder.storage  Locale  com.example.callrecorder.storage  Long  com.example.callrecorder.storage  RECORDINGS_FOLDER  com.example.callrecorder.storage  Regex  com.example.callrecorder.storage  SimpleDateFormat  com.example.callrecorder.storage  String  com.example.callrecorder.storage  System  com.example.callrecorder.storage  TEMP_FOLDER  com.example.callrecorder.storage  TRANSCRIPTS_FOLDER  com.example.callrecorder.storage  TRANSCRIPT_EXTENSION  com.example.callrecorder.storage  Uri  com.example.callrecorder.storage  apply  com.example.callrecorder.storage  forEach  com.example.callrecorder.storage  format  com.example.callrecorder.storage  invoke  com.example.callrecorder.storage  nameWithoutExtension  com.example.callrecorder.storage  
plusAssign  com.example.callrecorder.storage  readText  com.example.callrecorder.storage  replace  com.example.callrecorder.storage  	writeText  com.example.callrecorder.storage  AUDIO_EXTENSION ,com.example.callrecorder.storage.FileManager  AUDIO_MIME_TYPE ,com.example.callrecorder.storage.FileManager  Boolean ,com.example.callrecorder.storage.FileManager  Build ,com.example.callrecorder.storage.FileManager  CallType ,com.example.callrecorder.storage.FileManager  Context ,com.example.callrecorder.storage.FileManager  Date ,com.example.callrecorder.storage.FileManager  Environment ,com.example.callrecorder.storage.FileManager  	Exception ,com.example.callrecorder.storage.FileManager  File ,com.example.callrecorder.storage.FileManager  FileProvider ,com.example.callrecorder.storage.FileManager  IOException ,com.example.callrecorder.storage.FileManager  Int ,com.example.callrecorder.storage.FileManager  Intent ,com.example.callrecorder.storage.FileManager  Locale ,com.example.callrecorder.storage.FileManager  Long ,com.example.callrecorder.storage.FileManager  RECORDINGS_FOLDER ,com.example.callrecorder.storage.FileManager  Regex ,com.example.callrecorder.storage.FileManager  SimpleDateFormat ,com.example.callrecorder.storage.FileManager  String ,com.example.callrecorder.storage.FileManager  System ,com.example.callrecorder.storage.FileManager  TEMP_FOLDER ,com.example.callrecorder.storage.FileManager  TRANSCRIPTS_FOLDER ,com.example.callrecorder.storage.FileManager  TRANSCRIPT_EXTENSION ,com.example.callrecorder.storage.FileManager  Uri ,com.example.callrecorder.storage.FileManager  apply ,com.example.callrecorder.storage.FileManager  context ,com.example.callrecorder.storage.FileManager  createTranscriptFile ,com.example.callrecorder.storage.FileManager  deleteRecording ,com.example.callrecorder.storage.FileManager  forEach ,com.example.callrecorder.storage.FileManager  format ,com.example.callrecorder.storage.FileManager  getAPPLY ,com.example.callrecorder.storage.FileManager  getApply ,com.example.callrecorder.storage.FileManager  
getFOREach ,com.example.callrecorder.storage.FileManager  	getFORMAT ,com.example.callrecorder.storage.FileManager  
getForEach ,com.example.callrecorder.storage.FileManager  	getFormat ,com.example.callrecorder.storage.FileManager  
getPLUSAssign ,com.example.callrecorder.storage.FileManager  
getPlusAssign ,com.example.callrecorder.storage.FileManager  getREADText ,com.example.callrecorder.storage.FileManager  
getREPLACE ,com.example.callrecorder.storage.FileManager  getReadText ,com.example.callrecorder.storage.FileManager  getRecordingsDirectory ,com.example.callrecorder.storage.FileManager  
getReplace ,com.example.callrecorder.storage.FileManager  getTranscriptsDirectory ,com.example.callrecorder.storage.FileManager  getWRITEText ,com.example.callrecorder.storage.FileManager  getWriteText ,com.example.callrecorder.storage.FileManager  invoke ,com.example.callrecorder.storage.FileManager  nameWithoutExtension ,com.example.callrecorder.storage.FileManager  
plusAssign ,com.example.callrecorder.storage.FileManager  readText ,com.example.callrecorder.storage.FileManager  replace ,com.example.callrecorder.storage.FileManager  	writeText ,com.example.callrecorder.storage.FileManager  writeTranscript ,com.example.callrecorder.storage.FileManager  AUDIO_EXTENSION 6com.example.callrecorder.storage.FileManager.Companion  AUDIO_MIME_TYPE 6com.example.callrecorder.storage.FileManager.Companion  Boolean 6com.example.callrecorder.storage.FileManager.Companion  Build 6com.example.callrecorder.storage.FileManager.Companion  CallType 6com.example.callrecorder.storage.FileManager.Companion  Context 6com.example.callrecorder.storage.FileManager.Companion  Date 6com.example.callrecorder.storage.FileManager.Companion  Environment 6com.example.callrecorder.storage.FileManager.Companion  	Exception 6com.example.callrecorder.storage.FileManager.Companion  File 6com.example.callrecorder.storage.FileManager.Companion  FileProvider 6com.example.callrecorder.storage.FileManager.Companion  IOException 6com.example.callrecorder.storage.FileManager.Companion  Int 6com.example.callrecorder.storage.FileManager.Companion  Intent 6com.example.callrecorder.storage.FileManager.Companion  Locale 6com.example.callrecorder.storage.FileManager.Companion  Long 6com.example.callrecorder.storage.FileManager.Companion  RECORDINGS_FOLDER 6com.example.callrecorder.storage.FileManager.Companion  Regex 6com.example.callrecorder.storage.FileManager.Companion  SimpleDateFormat 6com.example.callrecorder.storage.FileManager.Companion  String 6com.example.callrecorder.storage.FileManager.Companion  System 6com.example.callrecorder.storage.FileManager.Companion  TEMP_FOLDER 6com.example.callrecorder.storage.FileManager.Companion  TRANSCRIPTS_FOLDER 6com.example.callrecorder.storage.FileManager.Companion  TRANSCRIPT_EXTENSION 6com.example.callrecorder.storage.FileManager.Companion  Uri 6com.example.callrecorder.storage.FileManager.Companion  apply 6com.example.callrecorder.storage.FileManager.Companion  forEach 6com.example.callrecorder.storage.FileManager.Companion  format 6com.example.callrecorder.storage.FileManager.Companion  getAPPLY 6com.example.callrecorder.storage.FileManager.Companion  getApply 6com.example.callrecorder.storage.FileManager.Companion  
getFOREach 6com.example.callrecorder.storage.FileManager.Companion  	getFORMAT 6com.example.callrecorder.storage.FileManager.Companion  
getForEach 6com.example.callrecorder.storage.FileManager.Companion  	getFormat 6com.example.callrecorder.storage.FileManager.Companion  
getPLUSAssign 6com.example.callrecorder.storage.FileManager.Companion  
getPlusAssign 6com.example.callrecorder.storage.FileManager.Companion  getREADText 6com.example.callrecorder.storage.FileManager.Companion  
getREPLACE 6com.example.callrecorder.storage.FileManager.Companion  getReadText 6com.example.callrecorder.storage.FileManager.Companion  
getReplace 6com.example.callrecorder.storage.FileManager.Companion  getWRITEText 6com.example.callrecorder.storage.FileManager.Companion  getWriteText 6com.example.callrecorder.storage.FileManager.Companion  invoke 6com.example.callrecorder.storage.FileManager.Companion  nameWithoutExtension 6com.example.callrecorder.storage.FileManager.Companion  
plusAssign 6com.example.callrecorder.storage.FileManager.Companion  readText 6com.example.callrecorder.storage.FileManager.Companion  replace 6com.example.callrecorder.storage.FileManager.Companion  	writeText 6com.example.callrecorder.storage.FileManager.Companion  Activity com.example.callrecorder.ui  ActivityResultContracts com.example.callrecorder.ui  AlertDialog com.example.callrecorder.ui  	Alignment com.example.callrecorder.ui  Arrangement com.example.callrecorder.ui  Build com.example.callrecorder.ui  Button com.example.callrecorder.ui  CallRecorderApp com.example.callrecorder.ui  CallRecordingsViewModel com.example.callrecorder.ui  Card com.example.callrecorder.ui  CardDefaults com.example.callrecorder.ui  Class com.example.callrecorder.ui  Column com.example.callrecorder.ui  
Composable com.example.callrecorder.ui  
ConsentScreen com.example.callrecorder.ui  Context com.example.callrecorder.ui  DisposableEffect com.example.callrecorder.ui  Divider com.example.callrecorder.ui  
MainScreen com.example.callrecorder.ui  Manifest com.example.callrecorder.ui  
MaterialTheme com.example.callrecorder.ui  MediaPlayer com.example.callrecorder.ui  Modifier com.example.callrecorder.ui  OutlinedButton com.example.callrecorder.ui  PermissionAndConsentScreen com.example.callrecorder.ui  PlaybackControls com.example.callrecorder.ui  PreviewCallRecorderApp com.example.callrecorder.ui  PrivacyPolicyScreen com.example.callrecorder.ui  RecordingControlScreen com.example.callrecorder.ui  RecordingsListScreen com.example.callrecorder.ui  Row com.example.callrecorder.ui  
SideEffect com.example.callrecorder.ui  Spacer com.example.callrecorder.ui  StartRecordingButton com.example.callrecorder.ui  Suppress com.example.callrecorder.ui  Surface com.example.callrecorder.ui  Text com.example.callrecorder.ui  Unit com.example.callrecorder.ui  all com.example.callrecorder.ui  android com.example.callrecorder.ui  androidx com.example.callrecorder.ui  apply com.example.callrecorder.ui  fillMaxSize com.example.callrecorder.ui  fillMaxWidth com.example.callrecorder.ui  getValue com.example.callrecorder.ui  invoke com.example.callrecorder.ui  listOf com.example.callrecorder.ui  mutableStateOf com.example.callrecorder.ui  padding com.example.callrecorder.ui  provideDelegate com.example.callrecorder.ui  remember com.example.callrecorder.ui  setValue com.example.callrecorder.ui  toTypedArray com.example.callrecorder.ui  width com.example.callrecorder.ui  AlertDialog #com.example.callrecorder.ui.dialogs  	Alignment #com.example.callrecorder.ui.dialogs  Arrangement #com.example.callrecorder.ui.dialogs  Boolean #com.example.callrecorder.ui.dialogs  Button #com.example.callrecorder.ui.dialogs  Card #com.example.callrecorder.ui.dialogs  CardDefaults #com.example.callrecorder.ui.dialogs  Checkbox #com.example.callrecorder.ui.dialogs  Column #com.example.callrecorder.ui.dialogs  
Composable #com.example.callrecorder.ui.dialogs  
ConsentDialog #com.example.callrecorder.ui.dialogs  ConsentDialogContent #com.example.callrecorder.ui.dialogs  
FontWeight #com.example.callrecorder.ui.dialogs  Icon #com.example.callrecorder.ui.dialogs  Icons #com.example.callrecorder.ui.dialogs  
MaterialTheme #com.example.callrecorder.ui.dialogs  Modifier #com.example.callrecorder.ui.dialogs  OutlinedButton #com.example.callrecorder.ui.dialogs  QuickConsentDialog #com.example.callrecorder.ui.dialogs  Row #com.example.callrecorder.ui.dialogs  Spacer #com.example.callrecorder.ui.dialogs  String #com.example.callrecorder.ui.dialogs  Text #com.example.callrecorder.ui.dialogs  	TextAlign #com.example.callrecorder.ui.dialogs  Unit #com.example.callrecorder.ui.dialogs  buildString #com.example.callrecorder.ui.dialogs  fillMaxWidth #com.example.callrecorder.ui.dialogs  getValue #com.example.callrecorder.ui.dialogs  height #com.example.callrecorder.ui.dialogs  mutableStateOf #com.example.callrecorder.ui.dialogs  padding #com.example.callrecorder.ui.dialogs  provideDelegate #com.example.callrecorder.ui.dialogs  remember #com.example.callrecorder.ui.dialogs  rememberScrollState #com.example.callrecorder.ui.dialogs  setValue #com.example.callrecorder.ui.dialogs  size #com.example.callrecorder.ui.dialogs  verticalScroll #com.example.callrecorder.ui.dialogs  width #com.example.callrecorder.ui.dialogs  Activity #com.example.callrecorder.ui.screens  ActivityResultContracts #com.example.callrecorder.ui.screens  	Alignment #com.example.callrecorder.ui.screens  Arrangement #com.example.callrecorder.ui.screens  Boolean #com.example.callrecorder.ui.screens  Box #com.example.callrecorder.ui.screens  Build #com.example.callrecorder.ui.screens  Button #com.example.callrecorder.ui.screens  ButtonDefaults #com.example.callrecorder.ui.screens  Call #com.example.callrecorder.ui.screens  CallRecordingService #com.example.callrecorder.ui.screens  CallType #com.example.callrecorder.ui.screens  Card #com.example.callrecorder.ui.screens  CardDefaults #com.example.callrecorder.ui.screens  CircularProgressIndicator #com.example.callrecorder.ui.screens  Close #com.example.callrecorder.ui.screens  Column #com.example.callrecorder.ui.screens  
Composable #com.example.callrecorder.ui.screens  Context #com.example.callrecorder.ui.screens  Delete #com.example.callrecorder.ui.screens  Divider #com.example.callrecorder.ui.screens  DropdownMenu #com.example.callrecorder.ui.screens  DropdownMenuItem #com.example.callrecorder.ui.screens  EmptyRecordingsState #com.example.callrecorder.ui.screens  
FontWeight #com.example.callrecorder.ui.screens  Icon #com.example.callrecorder.ui.screens  
IconButton #com.example.callrecorder.ui.screens  Icons #com.example.callrecorder.ui.screens  Info #com.example.callrecorder.ui.screens  LaunchedEffect #com.example.callrecorder.ui.screens  
LazyColumn #com.example.callrecorder.ui.screens  Locale #com.example.callrecorder.ui.screens  Map #com.example.callrecorder.ui.screens  
MaterialTheme #com.example.callrecorder.ui.screens  Modifier #com.example.callrecorder.ui.screens  MoreVert #com.example.callrecorder.ui.screens  OutlinedButton #com.example.callrecorder.ui.screens  PermissionItem #com.example.callrecorder.ui.screens  PermissionScreen #com.example.callrecorder.ui.screens  Phone #com.example.callrecorder.ui.screens  	PlayArrow #com.example.callrecorder.ui.screens  RecordingControlScreen #com.example.callrecorder.ui.screens  
RecordingItem #com.example.callrecorder.ui.screens  RecordingsListScreen #com.example.callrecorder.ui.screens  Row #com.example.callrecorder.ui.screens  Share #com.example.callrecorder.ui.screens  SimpleDateFormat #com.example.callrecorder.ui.screens  Spacer #com.example.callrecorder.ui.screens  SpecialPermissionItem #com.example.callrecorder.ui.screens  String #com.example.callrecorder.ui.screens  Text #com.example.callrecorder.ui.screens  TextOverflow #com.example.callrecorder.ui.screens  Unit #com.example.callrecorder.ui.screens  all #com.example.callrecorder.ui.screens  android #com.example.callrecorder.ui.screens  apply #com.example.callrecorder.ui.screens  checkAllPermissions #com.example.callrecorder.ui.screens  fillMaxSize #com.example.callrecorder.ui.screens  fillMaxWidth #com.example.callrecorder.ui.screens  forEach #com.example.callrecorder.ui.screens  getValue #com.example.callrecorder.ui.screens  height #com.example.callrecorder.ui.screens  items #com.example.callrecorder.ui.screens  java #com.example.callrecorder.ui.screens  mapOf #com.example.callrecorder.ui.screens  mutableMapOf #com.example.callrecorder.ui.screens  mutableStateOf #com.example.callrecorder.ui.screens  padding #com.example.callrecorder.ui.screens  provideDelegate #com.example.callrecorder.ui.screens  remember #com.example.callrecorder.ui.screens  set #com.example.callrecorder.ui.screens  setValue #com.example.callrecorder.ui.screens  size #com.example.callrecorder.ui.screens  startRecordingService #com.example.callrecorder.ui.screens  stopRecordingService #com.example.callrecorder.ui.screens  substringAfterLast #com.example.callrecorder.ui.screens  toList #com.example.callrecorder.ui.screens  width #com.example.callrecorder.ui.screens  Boolean %com.example.callrecorder.ui.viewmodel  CallRecordingsViewModel %com.example.callrecorder.ui.viewmodel  	Exception %com.example.callrecorder.ui.viewmodel  File %com.example.callrecorder.ui.viewmodel  FileManager %com.example.callrecorder.ui.viewmodel  List %com.example.callrecorder.ui.viewmodel  MediaPlayer %com.example.callrecorder.ui.viewmodel  MutableStateFlow %com.example.callrecorder.ui.viewmodel  	StateFlow %com.example.callrecorder.ui.viewmodel  String %com.example.callrecorder.ui.viewmodel  
_errorMessage %com.example.callrecorder.ui.viewmodel  
_isLoading %com.example.callrecorder.ui.viewmodel  _recordings %com.example.callrecorder.ui.viewmodel  apply %com.example.callrecorder.ui.viewmodel  asStateFlow %com.example.callrecorder.ui.viewmodel  	emptyList %com.example.callrecorder.ui.viewmodel  fileManager %com.example.callrecorder.ui.viewmodel  launch %com.example.callrecorder.ui.viewmodel  let %com.example.callrecorder.ui.viewmodel  loadRecordings %com.example.callrecorder.ui.viewmodel  mediaPlayer %com.example.callrecorder.ui.viewmodel  
repository %com.example.callrecorder.ui.viewmodel  stopCurrentPlayback %com.example.callrecorder.ui.viewmodel  viewModelScope %com.example.callrecorder.ui.viewmodel  Boolean =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  CallRecordingDisplay =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  CallRecordingRepository =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Context =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	Exception =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  File =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  FileManager =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  List =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  MediaPlayer =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  MutableStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	StateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  String =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
_errorMessage =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
_isLoading =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  _recordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  apply =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  asStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  deleteRecording =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	emptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  fileManager =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getAPPLY =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getASStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getApply =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getAsStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEMPTYList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEmptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	getLAUNCH =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getLET =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	getLaunch =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getLet =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getVIEWModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getViewModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  invoke =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	isLoading =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  launch =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  let =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  loadRecordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  mediaPlayer =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
playRecording =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
recordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
repository =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  shareRecording =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  stopCurrentPlayback =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  viewModelScope =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  viewTranscript =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Boolean com.example.callrecorder.util  Build com.example.callrecorder.util  CallRecordingService com.example.callrecorder.util  ErrorHandler com.example.callrecorder.util  ErrorResult com.example.callrecorder.util  	ErrorType com.example.callrecorder.util  	Exception com.example.callrecorder.util  Intent com.example.callrecorder.util  List com.example.callrecorder.util  Log com.example.callrecorder.util  PermissionManager com.example.callrecorder.util  RecordingServiceStarter com.example.callrecorder.util  String com.example.callrecorder.util  TAG com.example.callrecorder.util  android com.example.callrecorder.util  any com.example.callrecorder.util  buildString com.example.callrecorder.util  contains com.example.callrecorder.util  java com.example.callrecorder.util  listOf com.example.callrecorder.util  Build *com.example.callrecorder.util.ErrorHandler  Context *com.example.callrecorder.util.ErrorHandler  ErrorResult *com.example.callrecorder.util.ErrorHandler  	ErrorType *com.example.callrecorder.util.ErrorHandler  	Exception *com.example.callrecorder.util.ErrorHandler  List *com.example.callrecorder.util.ErrorHandler  Log *com.example.callrecorder.util.ErrorHandler  PermissionManager *com.example.callrecorder.util.ErrorHandler  String *com.example.callrecorder.util.ErrorHandler  TAG *com.example.callrecorder.util.ErrorHandler  android *com.example.callrecorder.util.ErrorHandler  any *com.example.callrecorder.util.ErrorHandler  buildString *com.example.callrecorder.util.ErrorHandler  contains *com.example.callrecorder.util.ErrorHandler  context *com.example.callrecorder.util.ErrorHandler  
getANDROID *com.example.callrecorder.util.ErrorHandler  getANY *com.example.callrecorder.util.ErrorHandler  
getAndroid *com.example.callrecorder.util.ErrorHandler  getAny *com.example.callrecorder.util.ErrorHandler  getBUILDString *com.example.callrecorder.util.ErrorHandler  getBuildString *com.example.callrecorder.util.ErrorHandler  getCONTAINS *com.example.callrecorder.util.ErrorHandler  getContains *com.example.callrecorder.util.ErrorHandler  	getLISTOf *com.example.callrecorder.util.ErrorHandler  	getListOf *com.example.callrecorder.util.ErrorHandler  invoke *com.example.callrecorder.util.ErrorHandler  listOf *com.example.callrecorder.util.ErrorHandler  Build 4com.example.callrecorder.util.ErrorHandler.Companion  Context 4com.example.callrecorder.util.ErrorHandler.Companion  ErrorResult 4com.example.callrecorder.util.ErrorHandler.Companion  	ErrorType 4com.example.callrecorder.util.ErrorHandler.Companion  	Exception 4com.example.callrecorder.util.ErrorHandler.Companion  List 4com.example.callrecorder.util.ErrorHandler.Companion  Log 4com.example.callrecorder.util.ErrorHandler.Companion  PermissionManager 4com.example.callrecorder.util.ErrorHandler.Companion  String 4com.example.callrecorder.util.ErrorHandler.Companion  TAG 4com.example.callrecorder.util.ErrorHandler.Companion  android 4com.example.callrecorder.util.ErrorHandler.Companion  any 4com.example.callrecorder.util.ErrorHandler.Companion  buildString 4com.example.callrecorder.util.ErrorHandler.Companion  contains 4com.example.callrecorder.util.ErrorHandler.Companion  
getANDROID 4com.example.callrecorder.util.ErrorHandler.Companion  getANY 4com.example.callrecorder.util.ErrorHandler.Companion  
getAndroid 4com.example.callrecorder.util.ErrorHandler.Companion  getAny 4com.example.callrecorder.util.ErrorHandler.Companion  getBUILDString 4com.example.callrecorder.util.ErrorHandler.Companion  getBuildString 4com.example.callrecorder.util.ErrorHandler.Companion  getCONTAINS 4com.example.callrecorder.util.ErrorHandler.Companion  getContains 4com.example.callrecorder.util.ErrorHandler.Companion  	getLISTOf 4com.example.callrecorder.util.ErrorHandler.Companion  	getListOf 4com.example.callrecorder.util.ErrorHandler.Companion  invoke 4com.example.callrecorder.util.ErrorHandler.Companion  listOf 4com.example.callrecorder.util.ErrorHandler.Companion  Boolean )com.example.callrecorder.util.ErrorResult  	ErrorType )com.example.callrecorder.util.ErrorResult  String )com.example.callrecorder.util.ErrorResult  ANDROID_10_LIMITATION 'com.example.callrecorder.util.ErrorType  API_AUTHENTICATION_FAILED 'com.example.callrecorder.util.ErrorType  API_RATE_LIMIT 'com.example.callrecorder.util.ErrorType  AUDIO_RECORD_FAILED 'com.example.callrecorder.util.ErrorType  CRITICAL_PERMISSION_MISSING 'com.example.callrecorder.util.ErrorType  DATABASE_ERROR 'com.example.callrecorder.util.ErrorType  FILE_NOT_FOUND 'com.example.callrecorder.util.ErrorType  INSUFFICIENT_STORAGE 'com.example.callrecorder.util.ErrorType  MEDIA_PROJECTION_FAILED 'com.example.callrecorder.util.ErrorType  NETWORK_TIMEOUT 'com.example.callrecorder.util.ErrorType  
NO_NETWORK 'com.example.callrecorder.util.ErrorType  OPTIONAL_PERMISSION_MISSING 'com.example.callrecorder.util.ErrorType  
STORAGE_ERROR 'com.example.callrecorder.util.ErrorType  STORAGE_PERMISSION_DENIED 'com.example.callrecorder.util.ErrorType  UNKNOWN_COMPATIBILITY 'com.example.callrecorder.util.ErrorType  UNKNOWN_FILE_ERROR 'com.example.callrecorder.util.ErrorType  UNKNOWN_NETWORK_ERROR 'com.example.callrecorder.util.ErrorType  UNKNOWN_RECORDING_ERROR 'com.example.callrecorder.util.ErrorType  UNSUPPORTED_ANDROID_VERSION 'com.example.callrecorder.util.ErrorType  Build 5com.example.callrecorder.util.RecordingServiceStarter  CallRecordingService 5com.example.callrecorder.util.RecordingServiceStarter  Context 5com.example.callrecorder.util.RecordingServiceStarter  Intent 5com.example.callrecorder.util.RecordingServiceStarter  java 5com.example.callrecorder.util.RecordingServiceStarter  start 5com.example.callrecorder.util.RecordingServiceStarter  SerializedName com.google.gson.annotations  File java.io  FileOutputStream java.io  IOException java.io  absolutePath java.io.File  
asRequestBody java.io.File  delete java.io.File  exists java.io.File  	extension java.io.File  	freeSpace java.io.File  getABSOLUTEPath java.io.File  getASRequestBody java.io.File  getAbsolutePath java.io.File  getAsRequestBody java.io.File  getEXTENSION java.io.File  getExtension java.io.File  getFREESpace java.io.File  getFreeSpace java.io.File  	getISFile java.io.File  	getIsFile java.io.File  getLET java.io.File  getLet java.io.File  getNAME java.io.File  getNAMEWithoutExtension java.io.File  getName java.io.File  getNameWithoutExtension java.io.File  getREADText java.io.File  getReadText java.io.File  getWRITEText java.io.File  getWriteText java.io.File  isFile java.io.File  lastModified java.io.File  length java.io.File  let java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  nameWithoutExtension java.io.File  readText java.io.File  setAbsolutePath java.io.File  setFile java.io.File  setFreeSpace java.io.File  setName java.io.File  	writeText java.io.File  close java.io.FileOutputStream  write java.io.FileOutputStream  printStackTrace java.io.IOException  close java.io.OutputStream  write java.io.OutputStream  "ACCESSIBILITY_SERVICE_REQUEST_CODE 	java.lang  ACTION_START_RECORDING 	java.lang  ACTION_STOP_RECORDING 	java.lang  AUDIO_EXTENSION 	java.lang  AUDIO_MIME_TYPE 	java.lang  Activity 	java.lang  ActivityCompat 	java.lang  ActivityResultContracts 	java.lang  	Alignment 	java.lang  Arrangement 	java.lang  AudioFormat 	java.lang  AudioRecord 	java.lang  BASE_URL 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  	ByteArray 	java.lang  
CHANNEL_ID 	java.lang  CORE_PERMISSIONS 	java.lang  CallLog 	java.lang  CallRecorderApp 	java.lang  
CallRecording 	java.lang  CallRecordingDatabase 	java.lang  CallRecordingDisplay 	java.lang  CallRecordingRepository 	java.lang  CallRecordingService 	java.lang  CallRecordingsViewModel 	java.lang  CallType 	java.lang  Card 	java.lang  CardDefaults 	java.lang  Checkbox 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Column 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  
Converters 	java.lang  CoroutineScope 	java.lang  CostEstimate 	java.lang  Date 	java.lang  Dispatchers 	java.lang  Divider 	java.lang  DropdownMenu 	java.lang  DropdownMenuItem 	java.lang  EXTRA_CALL_TYPE 	java.lang  EXTRA_CONTACT_NAME 	java.lang  EXTRA_MEDIA_PROJECTION_DATA 	java.lang  EXTRA_PHONE_NUMBER 	java.lang  EmptyRecordingsState 	java.lang  Environment 	java.lang  ErrorResult 	java.lang  	ErrorType 	java.lang  	Exception 	java.lang  File 	java.lang  FileManager 	java.lang  FileOutputStream 	java.lang  FileProvider 	java.lang  
FontWeight 	java.lang  GsonConverterFactory 	java.lang  Handler 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  IllegalStateException 	java.lang  Intent 	java.lang  IntentFilter 	java.lang  
LazyColumn 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE 	java.lang  
MAX_FILE_SIZE 	java.lang  Manifest 	java.lang  
MaterialTheme 	java.lang  MediaPlayer 	java.lang  
MediaRecorder 	java.lang  Modifier 	java.lang  
MultipartBody 	java.lang  MutableStateFlow 	java.lang  NETWORK_PERMISSIONS 	java.lang  NOTIFICATION_ID 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  OkHttpClient 	java.lang  OnConflictStrategy 	java.lang  OpenAIApiService 	java.lang  OutlinedButton 	java.lang  PERMISSION_REQUEST_CODE 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang  PermissionItem 	java.lang  PermissionManager 	java.lang  PhoneStateListener 	java.lang  
PlaybackState 	java.lang  PowerManager 	java.lang  R 	java.lang  RECORDINGS_FOLDER 	java.lang  RecordingControlScreen 	java.lang  
RecordingItem 	java.lang  RecordingsListScreen 	java.lang  Regex 	java.lang  RequestBody 	java.lang  Retrofit 	java.lang  Room 	java.lang  Row 	java.lang  Runnable 	java.lang  SERVICE_PERMISSIONS 	java.lang  START_STICKY 	java.lang  STORAGE_PERMISSIONS_LEGACY 	java.lang   SYSTEM_ALERT_WINDOW_REQUEST_CODE 	java.lang  Settings 	java.lang  SimpleDateFormat 	java.lang  Spacer 	java.lang  SpecialPermissionItem 	java.lang  String 	java.lang  
StringBuilder 	java.lang  
SupervisorJob 	java.lang  System 	java.lang  TAG 	java.lang  TEMP_FOLDER 	java.lang  TIMEOUT_SECONDS 	java.lang  TRANSCRIPTS_FOLDER 	java.lang  TRANSCRIPT_EXTENSION 	java.lang  TelephonyManager 	java.lang  Text 	java.lang  	TextAlign 	java.lang  TextOverflow 	java.lang  Thread 	java.lang  TimeUnit 	java.lang  TranscriptionQueueStatus 	java.lang  TranscriptionResult 	java.lang  Unit 	java.lang  Uri 	java.lang  _currentFile 	java.lang  _currentPosition 	java.lang  	_duration 	java.lang  
_errorMessage 	java.lang  
_isLoading 	java.lang  _playbackState 	java.lang  _recordings 	java.lang  addAll 	java.lang  all 	java.lang  android 	java.lang  androidx 	java.lang  any 	java.lang  apiKey 	java.lang  
apiService 	java.lang  apply 	java.lang  arrayOf 	java.lang  
asRequestBody 	java.lang  asStateFlow 	java.lang  audioRecord 	java.lang  buildString 	java.lang  cancel 	java.lang  checkAllPermissions 	java.lang  com 	java.lang  contains 	java.lang  context 	java.lang  currentRecordingFile 	java.lang  	emptyList 	java.lang  	extension 	java.lang  fileManager 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  filter 	java.lang  find 	java.lang  flowOf 	java.lang  forEach 	java.lang  forEachIndexed 	java.lang  format 	java.lang  formatDuration 	java.lang  formatFileSize 	java.lang  handleCallLogChange 	java.lang  handleCallStateChange 	java.lang  handleOutgoingCall 	java.lang  height 	java.lang  indexOfFirst 	java.lang  invoke 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  isRecording 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  loadRecordings 	java.lang  	lowercase 	java.lang  map 	java.lang  mapOf 	java.lang  mediaPlayer 	java.lang  mediaProjection 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  nameWithoutExtension 	java.lang  nextId 	java.lang  padding 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  readText 	java.lang  recordingStartTime 	java.lang  
recordings 	java.lang  rememberScrollState 	java.lang  	removeAll 	java.lang  replace 	java.lang  
repository 	java.lang  serviceScope 	java.lang  set 	java.lang  size 	java.lang  startPositionUpdates 	java.lang  startRecordingService 	java.lang  stopCurrentPlayback 	java.lang  stopPositionUpdates 	java.lang  
stopRecording 	java.lang  stopRecordingService 	java.lang  substringAfterLast 	java.lang  synchronized 	java.lang  toList 	java.lang  toMediaType 	java.lang  toTypedArray 	java.lang  transcribeAudio 	java.lang  use 	java.lang  verticalScroll 	java.lang  width 	java.lang  withContext 	java.lang  	writeText 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  getLET java.lang.Runnable  getLet java.lang.Runnable  let java.lang.Runnable  Build java.lang.StringBuilder  append java.lang.StringBuilder  currentTimeMillis java.lang.System  
currentThread java.lang.Thread  getISInterrupted java.lang.Thread  getIsInterrupted java.lang.Thread  
isInterrupted java.lang.Thread  setInterrupted java.lang.Thread  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ACTION_START_RECORDING 	java.util  ACTION_STOP_RECORDING 	java.util  AUDIO_EXTENSION 	java.util  AUDIO_MIME_TYPE 	java.util  Activity 	java.util  	Alignment 	java.util  Arrangement 	java.util  AudioFormat 	java.util  AudioRecord 	java.util  Box 	java.util  Build 	java.util  	ByteArray 	java.util  
CHANNEL_ID 	java.util  Call 	java.util  
CallRecording 	java.util  CallRecordingRepository 	java.util  CallRecordingService 	java.util  CallType 	java.util  Card 	java.util  CardDefaults 	java.util  CircularProgressIndicator 	java.util  Close 	java.util  Column 	java.util  
Composable 	java.util  Context 	java.util  CoroutineScope 	java.util  Date 	java.util  Delete 	java.util  Dispatchers 	java.util  Divider 	java.util  DropdownMenu 	java.util  DropdownMenuItem 	java.util  EXTRA_CALL_TYPE 	java.util  EXTRA_CONTACT_NAME 	java.util  EXTRA_MEDIA_PROJECTION_DATA 	java.util  EXTRA_PHONE_NUMBER 	java.util  EmptyRecordingsState 	java.util  Environment 	java.util  	Exception 	java.util  File 	java.util  FileOutputStream 	java.util  FileProvider 	java.util  
FontWeight 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  IllegalStateException 	java.util  Info 	java.util  Intent 	java.util  Job 	java.util  
LazyColumn 	java.util  Locale 	java.util  
MaterialTheme 	java.util  
MediaRecorder 	java.util  Modifier 	java.util  MoreVert 	java.util  NOTIFICATION_ID 	java.util  Notification 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  
PendingIntent 	java.util  Phone 	java.util  	PlayArrow 	java.util  PowerManager 	java.util  R 	java.util  RECORDINGS_FOLDER 	java.util  
RecordingItem 	java.util  Regex 	java.util  Row 	java.util  START_STICKY 	java.util  Service 	java.util  Share 	java.util  SimpleDateFormat 	java.util  Spacer 	java.util  String 	java.util  
SupervisorJob 	java.util  System 	java.util  TEMP_FOLDER 	java.util  TRANSCRIPTS_FOLDER 	java.util  TRANSCRIPT_EXTENSION 	java.util  Text 	java.util  TextOverflow 	java.util  Thread 	java.util  Uri 	java.util  apply 	java.util  audioRecord 	java.util  cancel 	java.util  currentRecordingFile 	java.util  	emptyList 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  flowOf 	java.util  forEach 	java.util  format 	java.util  getValue 	java.util  height 	java.util  invoke 	java.util  isRecording 	java.util  items 	java.util  java 	java.util  launch 	java.util  let 	java.util  mediaProjection 	java.util  mutableStateOf 	java.util  nameWithoutExtension 	java.util  padding 	java.util  
plusAssign 	java.util  provideDelegate 	java.util  readText 	java.util  recordingStartTime 	java.util  remember 	java.util  replace 	java.util  
repository 	java.util  setValue 	java.util  size 	java.util  
stopRecording 	java.util  width 	java.util  	writeText 	java.util  getTIME java.util.Date  getTime java.util.Date  setTime java.util.Date  time java.util.Date  
getDefault java.util.Locale  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin  ACTION_START_RECORDING kotlin  ACTION_STOP_RECORDING kotlin  AUDIO_EXTENSION kotlin  AUDIO_MIME_TYPE kotlin  Activity kotlin  ActivityCompat kotlin  ActivityResultContracts kotlin  	Alignment kotlin  Any kotlin  Arrangement kotlin  Array kotlin  AudioFormat kotlin  AudioRecord kotlin  BASE_URL kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  	ByteArray kotlin  
CHANNEL_ID kotlin  CORE_PERMISSIONS kotlin  CallLog kotlin  CallRecorderApp kotlin  
CallRecording kotlin  CallRecordingDatabase kotlin  CallRecordingDisplay kotlin  CallRecordingRepository kotlin  CallRecordingService kotlin  CallRecordingsViewModel kotlin  CallType kotlin  Card kotlin  CardDefaults kotlin  Checkbox kotlin  CircularProgressIndicator kotlin  Class kotlin  Column kotlin  Context kotlin  
ContextCompat kotlin  
Converters kotlin  CoroutineScope kotlin  CostEstimate kotlin  Date kotlin  Dispatchers kotlin  Divider kotlin  Double kotlin  DropdownMenu kotlin  DropdownMenuItem kotlin  EXTRA_CALL_TYPE kotlin  EXTRA_CONTACT_NAME kotlin  EXTRA_MEDIA_PROJECTION_DATA kotlin  EXTRA_PHONE_NUMBER kotlin  EmptyRecordingsState kotlin  Environment kotlin  ErrorResult kotlin  	ErrorType kotlin  	Exception kotlin  File kotlin  FileManager kotlin  FileOutputStream kotlin  FileProvider kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  GsonConverterFactory kotlin  Handler kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  IllegalStateException kotlin  Int kotlin  IntArray kotlin  Intent kotlin  IntentFilter kotlin  
LazyColumn kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin  
MAX_FILE_SIZE kotlin  Manifest kotlin  
MaterialTheme kotlin  MediaPlayer kotlin  
MediaRecorder kotlin  Modifier kotlin  
MultipartBody kotlin  MutableStateFlow kotlin  NETWORK_PERMISSIONS kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  OkHttpClient kotlin  OnConflictStrategy kotlin  OpenAIApiService kotlin  OutlinedButton kotlin  PERMISSION_REQUEST_CODE kotlin  PackageManager kotlin  
PendingIntent kotlin  PermissionItem kotlin  PermissionManager kotlin  PhoneStateListener kotlin  
PlaybackState kotlin  PowerManager kotlin  R kotlin  RECORDINGS_FOLDER kotlin  RecordingControlScreen kotlin  
RecordingItem kotlin  RecordingsListScreen kotlin  Regex kotlin  RequestBody kotlin  Retrofit kotlin  Room kotlin  Row kotlin  Runnable kotlin  SERVICE_PERMISSIONS kotlin  START_STICKY kotlin  STORAGE_PERMISSIONS_LEGACY kotlin   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin  Settings kotlin  SimpleDateFormat kotlin  Spacer kotlin  SpecialPermissionItem kotlin  String kotlin  
SupervisorJob kotlin  Suppress kotlin  System kotlin  TAG kotlin  TEMP_FOLDER kotlin  TIMEOUT_SECONDS kotlin  TRANSCRIPTS_FOLDER kotlin  TRANSCRIPT_EXTENSION kotlin  TelephonyManager kotlin  Text kotlin  	TextAlign kotlin  TextOverflow kotlin  Thread kotlin  TimeUnit kotlin  TranscriptionQueueStatus kotlin  TranscriptionResult kotlin  Unit kotlin  Uri kotlin  Volatile kotlin  _currentFile kotlin  _currentPosition kotlin  	_duration kotlin  
_errorMessage kotlin  
_isLoading kotlin  _playbackState kotlin  _recordings kotlin  addAll kotlin  all kotlin  android kotlin  androidx kotlin  any kotlin  apiKey kotlin  
apiService kotlin  apply kotlin  arrayOf kotlin  
asRequestBody kotlin  asStateFlow kotlin  audioRecord kotlin  buildString kotlin  cancel kotlin  checkAllPermissions kotlin  com kotlin  contains kotlin  context kotlin  currentRecordingFile kotlin  	emptyList kotlin  	extension kotlin  fileManager kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  filter kotlin  find kotlin  flowOf kotlin  forEach kotlin  forEachIndexed kotlin  format kotlin  formatDuration kotlin  formatFileSize kotlin  handleCallLogChange kotlin  handleCallStateChange kotlin  handleOutgoingCall kotlin  height kotlin  indexOfFirst kotlin  invoke kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  isRecording kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  loadRecordings kotlin  	lowercase kotlin  map kotlin  mapOf kotlin  mediaPlayer kotlin  mediaProjection kotlin  
mutableListOf kotlin  mutableMapOf kotlin  nameWithoutExtension kotlin  nextId kotlin  padding kotlin  
plusAssign kotlin  provideDelegate kotlin  readText kotlin  recordingStartTime kotlin  
recordings kotlin  rememberScrollState kotlin  	removeAll kotlin  replace kotlin  
repository kotlin  serviceScope kotlin  set kotlin  size kotlin  startPositionUpdates kotlin  startRecordingService kotlin  stopCurrentPlayback kotlin  stopPositionUpdates kotlin  
stopRecording kotlin  stopRecordingService kotlin  substringAfterLast kotlin  synchronized kotlin  toList kotlin  toMediaType kotlin  toTypedArray kotlin  transcribeAudio kotlin  use kotlin  verticalScroll kotlin  width kotlin  withContext kotlin  	writeText kotlin  getALL kotlin.Array  getAll kotlin.Array  	getFILTER kotlin.Array  
getFOREach kotlin.Array  getFOREachIndexed kotlin.Array  	getFilter kotlin.Array  
getForEach kotlin.Array  getForEachIndexed kotlin.Array  	getTOList kotlin.Array  	getToList kotlin.Array  getDP 
kotlin.Int  getDp 
kotlin.Int  	getFORMAT 
kotlin.Int  	getFormat 
kotlin.Int  getLET kotlin.Long  getLet kotlin.Long  
getPLUSAssign kotlin.Long  
getPlusAssign kotlin.Long  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getISNullOrBlank 
kotlin.String  getIsNullOrBlank 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  
getREPLACE 
kotlin.String  
getReplace 
kotlin.String  getSUBSTRINGAfterLast 
kotlin.String  getSubstringAfterLast 
kotlin.String  getTOMediaType 
kotlin.String  getToMediaType 
kotlin.String  
isNullOrBlank 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin.annotation  ACTION_START_RECORDING kotlin.annotation  ACTION_STOP_RECORDING kotlin.annotation  AUDIO_EXTENSION kotlin.annotation  AUDIO_MIME_TYPE kotlin.annotation  Activity kotlin.annotation  ActivityCompat kotlin.annotation  ActivityResultContracts kotlin.annotation  	Alignment kotlin.annotation  Arrangement kotlin.annotation  AudioFormat kotlin.annotation  AudioRecord kotlin.annotation  BASE_URL kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  	ByteArray kotlin.annotation  
CHANNEL_ID kotlin.annotation  CORE_PERMISSIONS kotlin.annotation  CallLog kotlin.annotation  CallRecorderApp kotlin.annotation  
CallRecording kotlin.annotation  CallRecordingDatabase kotlin.annotation  CallRecordingDisplay kotlin.annotation  CallRecordingRepository kotlin.annotation  CallRecordingService kotlin.annotation  CallRecordingsViewModel kotlin.annotation  CallType kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  Checkbox kotlin.annotation  CircularProgressIndicator kotlin.annotation  Class kotlin.annotation  Column kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  
Converters kotlin.annotation  CoroutineScope kotlin.annotation  CostEstimate kotlin.annotation  Date kotlin.annotation  Dispatchers kotlin.annotation  Divider kotlin.annotation  DropdownMenu kotlin.annotation  DropdownMenuItem kotlin.annotation  EXTRA_CALL_TYPE kotlin.annotation  EXTRA_CONTACT_NAME kotlin.annotation  EXTRA_MEDIA_PROJECTION_DATA kotlin.annotation  EXTRA_PHONE_NUMBER kotlin.annotation  EmptyRecordingsState kotlin.annotation  Environment kotlin.annotation  ErrorResult kotlin.annotation  	ErrorType kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileManager kotlin.annotation  FileOutputStream kotlin.annotation  FileProvider kotlin.annotation  
FontWeight kotlin.annotation  GsonConverterFactory kotlin.annotation  Handler kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  IllegalStateException kotlin.annotation  Intent kotlin.annotation  IntentFilter kotlin.annotation  
LazyColumn kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin.annotation  
MAX_FILE_SIZE kotlin.annotation  Manifest kotlin.annotation  
MaterialTheme kotlin.annotation  MediaPlayer kotlin.annotation  
MediaRecorder kotlin.annotation  Modifier kotlin.annotation  
MultipartBody kotlin.annotation  MutableStateFlow kotlin.annotation  NETWORK_PERMISSIONS kotlin.annotation  NOTIFICATION_ID kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  OkHttpClient kotlin.annotation  OnConflictStrategy kotlin.annotation  OpenAIApiService kotlin.annotation  OutlinedButton kotlin.annotation  PERMISSION_REQUEST_CODE kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation  PermissionItem kotlin.annotation  PermissionManager kotlin.annotation  PhoneStateListener kotlin.annotation  
PlaybackState kotlin.annotation  PowerManager kotlin.annotation  R kotlin.annotation  RECORDINGS_FOLDER kotlin.annotation  RecordingControlScreen kotlin.annotation  
RecordingItem kotlin.annotation  RecordingsListScreen kotlin.annotation  Regex kotlin.annotation  RequestBody kotlin.annotation  Retrofit kotlin.annotation  Room kotlin.annotation  Row kotlin.annotation  Runnable kotlin.annotation  SERVICE_PERMISSIONS kotlin.annotation  START_STICKY kotlin.annotation  STORAGE_PERMISSIONS_LEGACY kotlin.annotation   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin.annotation  Settings kotlin.annotation  SimpleDateFormat kotlin.annotation  Spacer kotlin.annotation  SpecialPermissionItem kotlin.annotation  String kotlin.annotation  
SupervisorJob kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  TEMP_FOLDER kotlin.annotation  TIMEOUT_SECONDS kotlin.annotation  TRANSCRIPTS_FOLDER kotlin.annotation  TRANSCRIPT_EXTENSION kotlin.annotation  TelephonyManager kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  TextOverflow kotlin.annotation  Thread kotlin.annotation  TimeUnit kotlin.annotation  TranscriptionQueueStatus kotlin.annotation  TranscriptionResult kotlin.annotation  Unit kotlin.annotation  Uri kotlin.annotation  Volatile kotlin.annotation  _currentFile kotlin.annotation  _currentPosition kotlin.annotation  	_duration kotlin.annotation  
_errorMessage kotlin.annotation  
_isLoading kotlin.annotation  _playbackState kotlin.annotation  _recordings kotlin.annotation  addAll kotlin.annotation  all kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  any kotlin.annotation  apiKey kotlin.annotation  
apiService kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  
asRequestBody kotlin.annotation  asStateFlow kotlin.annotation  audioRecord kotlin.annotation  buildString kotlin.annotation  cancel kotlin.annotation  checkAllPermissions kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  context kotlin.annotation  currentRecordingFile kotlin.annotation  	emptyList kotlin.annotation  	extension kotlin.annotation  fileManager kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  filter kotlin.annotation  find kotlin.annotation  flowOf kotlin.annotation  forEach kotlin.annotation  forEachIndexed kotlin.annotation  format kotlin.annotation  formatDuration kotlin.annotation  formatFileSize kotlin.annotation  handleCallLogChange kotlin.annotation  handleCallStateChange kotlin.annotation  handleOutgoingCall kotlin.annotation  height kotlin.annotation  indexOfFirst kotlin.annotation  invoke kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  isRecording kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  loadRecordings kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  mediaPlayer kotlin.annotation  mediaProjection kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  nameWithoutExtension kotlin.annotation  nextId kotlin.annotation  padding kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  readText kotlin.annotation  recordingStartTime kotlin.annotation  
recordings kotlin.annotation  rememberScrollState kotlin.annotation  	removeAll kotlin.annotation  replace kotlin.annotation  
repository kotlin.annotation  serviceScope kotlin.annotation  set kotlin.annotation  size kotlin.annotation  startPositionUpdates kotlin.annotation  startRecordingService kotlin.annotation  stopCurrentPlayback kotlin.annotation  stopPositionUpdates kotlin.annotation  
stopRecording kotlin.annotation  stopRecordingService kotlin.annotation  substringAfterLast kotlin.annotation  synchronized kotlin.annotation  toList kotlin.annotation  toMediaType kotlin.annotation  toTypedArray kotlin.annotation  transcribeAudio kotlin.annotation  use kotlin.annotation  verticalScroll kotlin.annotation  width kotlin.annotation  withContext kotlin.annotation  	writeText kotlin.annotation  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin.collections  ACTION_START_RECORDING kotlin.collections  ACTION_STOP_RECORDING kotlin.collections  AUDIO_EXTENSION kotlin.collections  AUDIO_MIME_TYPE kotlin.collections  Activity kotlin.collections  ActivityCompat kotlin.collections  ActivityResultContracts kotlin.collections  	Alignment kotlin.collections  Arrangement kotlin.collections  AudioFormat kotlin.collections  AudioRecord kotlin.collections  BASE_URL kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  	ByteArray kotlin.collections  
CHANNEL_ID kotlin.collections  CORE_PERMISSIONS kotlin.collections  CallLog kotlin.collections  CallRecorderApp kotlin.collections  
CallRecording kotlin.collections  CallRecordingDatabase kotlin.collections  CallRecordingDisplay kotlin.collections  CallRecordingRepository kotlin.collections  CallRecordingService kotlin.collections  CallRecordingsViewModel kotlin.collections  CallType kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  Checkbox kotlin.collections  CircularProgressIndicator kotlin.collections  Class kotlin.collections  Column kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  
Converters kotlin.collections  CoroutineScope kotlin.collections  CostEstimate kotlin.collections  Date kotlin.collections  Dispatchers kotlin.collections  Divider kotlin.collections  DropdownMenu kotlin.collections  DropdownMenuItem kotlin.collections  EXTRA_CALL_TYPE kotlin.collections  EXTRA_CONTACT_NAME kotlin.collections  EXTRA_MEDIA_PROJECTION_DATA kotlin.collections  EXTRA_PHONE_NUMBER kotlin.collections  EmptyRecordingsState kotlin.collections  Environment kotlin.collections  ErrorResult kotlin.collections  	ErrorType kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileManager kotlin.collections  FileOutputStream kotlin.collections  FileProvider kotlin.collections  
FontWeight kotlin.collections  GsonConverterFactory kotlin.collections  Handler kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  IllegalStateException kotlin.collections  Intent kotlin.collections  IntentFilter kotlin.collections  
LazyColumn kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin.collections  
MAX_FILE_SIZE kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  MediaPlayer kotlin.collections  
MediaRecorder kotlin.collections  Modifier kotlin.collections  
MultipartBody kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  NETWORK_PERMISSIONS kotlin.collections  NOTIFICATION_ID kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  OkHttpClient kotlin.collections  OnConflictStrategy kotlin.collections  OpenAIApiService kotlin.collections  OutlinedButton kotlin.collections  PERMISSION_REQUEST_CODE kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections  PermissionItem kotlin.collections  PermissionManager kotlin.collections  PhoneStateListener kotlin.collections  
PlaybackState kotlin.collections  PowerManager kotlin.collections  R kotlin.collections  RECORDINGS_FOLDER kotlin.collections  RecordingControlScreen kotlin.collections  
RecordingItem kotlin.collections  RecordingsListScreen kotlin.collections  Regex kotlin.collections  RequestBody kotlin.collections  Retrofit kotlin.collections  Room kotlin.collections  Row kotlin.collections  Runnable kotlin.collections  SERVICE_PERMISSIONS kotlin.collections  START_STICKY kotlin.collections  STORAGE_PERMISSIONS_LEGACY kotlin.collections   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin.collections  Settings kotlin.collections  SimpleDateFormat kotlin.collections  Spacer kotlin.collections  SpecialPermissionItem kotlin.collections  String kotlin.collections  
SupervisorJob kotlin.collections  System kotlin.collections  TAG kotlin.collections  TEMP_FOLDER kotlin.collections  TIMEOUT_SECONDS kotlin.collections  TRANSCRIPTS_FOLDER kotlin.collections  TRANSCRIPT_EXTENSION kotlin.collections  TelephonyManager kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  TextOverflow kotlin.collections  Thread kotlin.collections  TimeUnit kotlin.collections  TranscriptionQueueStatus kotlin.collections  TranscriptionResult kotlin.collections  Unit kotlin.collections  Uri kotlin.collections  Volatile kotlin.collections  _currentFile kotlin.collections  _currentPosition kotlin.collections  	_duration kotlin.collections  
_errorMessage kotlin.collections  
_isLoading kotlin.collections  _playbackState kotlin.collections  _recordings kotlin.collections  addAll kotlin.collections  all kotlin.collections  android kotlin.collections  androidx kotlin.collections  any kotlin.collections  apiKey kotlin.collections  
apiService kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  
asRequestBody kotlin.collections  asStateFlow kotlin.collections  audioRecord kotlin.collections  buildString kotlin.collections  cancel kotlin.collections  checkAllPermissions kotlin.collections  com kotlin.collections  contains kotlin.collections  context kotlin.collections  currentRecordingFile kotlin.collections  	emptyList kotlin.collections  	extension kotlin.collections  fileManager kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  filter kotlin.collections  find kotlin.collections  flowOf kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  format kotlin.collections  formatDuration kotlin.collections  formatFileSize kotlin.collections  handleCallLogChange kotlin.collections  handleCallStateChange kotlin.collections  handleOutgoingCall kotlin.collections  height kotlin.collections  indexOfFirst kotlin.collections  invoke kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  isRecording kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  loadRecordings kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  mapOf kotlin.collections  mediaPlayer kotlin.collections  mediaProjection kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  nameWithoutExtension kotlin.collections  nextId kotlin.collections  padding kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  readText kotlin.collections  recordingStartTime kotlin.collections  
recordings kotlin.collections  rememberScrollState kotlin.collections  	removeAll kotlin.collections  replace kotlin.collections  
repository kotlin.collections  serviceScope kotlin.collections  set kotlin.collections  size kotlin.collections  startPositionUpdates kotlin.collections  startRecordingService kotlin.collections  stopCurrentPlayback kotlin.collections  stopPositionUpdates kotlin.collections  
stopRecording kotlin.collections  stopRecordingService kotlin.collections  substringAfterLast kotlin.collections  synchronized kotlin.collections  toList kotlin.collections  toMediaType kotlin.collections  toTypedArray kotlin.collections  transcribeAudio kotlin.collections  use kotlin.collections  verticalScroll kotlin.collections  width kotlin.collections  withContext kotlin.collections  	writeText kotlin.collections  getALL kotlin.collections.Collection  getAll kotlin.collections.Collection  getANY kotlin.collections.List  getAny kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getTOTypedArray kotlin.collections.List  getToTypedArray kotlin.collections.List  
isNotEmpty kotlin.collections.List  Entry kotlin.collections.Map  getALL kotlin.collections.Map  getAll kotlin.collections.Map  	getADDAll kotlin.collections.MutableList  	getAddAll kotlin.collections.MutableList  getFIND kotlin.collections.MutableList  getFind kotlin.collections.MutableList  getINDEXOfFirst kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getMAP kotlin.collections.MutableList  getMap kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin.comparisons  ACTION_START_RECORDING kotlin.comparisons  ACTION_STOP_RECORDING kotlin.comparisons  AUDIO_EXTENSION kotlin.comparisons  AUDIO_MIME_TYPE kotlin.comparisons  Activity kotlin.comparisons  ActivityCompat kotlin.comparisons  ActivityResultContracts kotlin.comparisons  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  AudioFormat kotlin.comparisons  AudioRecord kotlin.comparisons  BASE_URL kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  	ByteArray kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  CORE_PERMISSIONS kotlin.comparisons  CallLog kotlin.comparisons  CallRecorderApp kotlin.comparisons  
CallRecording kotlin.comparisons  CallRecordingDatabase kotlin.comparisons  CallRecordingDisplay kotlin.comparisons  CallRecordingRepository kotlin.comparisons  CallRecordingService kotlin.comparisons  CallRecordingsViewModel kotlin.comparisons  CallType kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  Checkbox kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Class kotlin.comparisons  Column kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  
Converters kotlin.comparisons  CoroutineScope kotlin.comparisons  CostEstimate kotlin.comparisons  Date kotlin.comparisons  Dispatchers kotlin.comparisons  Divider kotlin.comparisons  DropdownMenu kotlin.comparisons  DropdownMenuItem kotlin.comparisons  EXTRA_CALL_TYPE kotlin.comparisons  EXTRA_CONTACT_NAME kotlin.comparisons  EXTRA_MEDIA_PROJECTION_DATA kotlin.comparisons  EXTRA_PHONE_NUMBER kotlin.comparisons  EmptyRecordingsState kotlin.comparisons  Environment kotlin.comparisons  ErrorResult kotlin.comparisons  	ErrorType kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileManager kotlin.comparisons  FileOutputStream kotlin.comparisons  FileProvider kotlin.comparisons  
FontWeight kotlin.comparisons  GsonConverterFactory kotlin.comparisons  Handler kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  IllegalStateException kotlin.comparisons  Intent kotlin.comparisons  IntentFilter kotlin.comparisons  
LazyColumn kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin.comparisons  
MAX_FILE_SIZE kotlin.comparisons  Manifest kotlin.comparisons  
MaterialTheme kotlin.comparisons  MediaPlayer kotlin.comparisons  
MediaRecorder kotlin.comparisons  Modifier kotlin.comparisons  
MultipartBody kotlin.comparisons  MutableStateFlow kotlin.comparisons  NETWORK_PERMISSIONS kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  OkHttpClient kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OpenAIApiService kotlin.comparisons  OutlinedButton kotlin.comparisons  PERMISSION_REQUEST_CODE kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons  PermissionItem kotlin.comparisons  PermissionManager kotlin.comparisons  PhoneStateListener kotlin.comparisons  
PlaybackState kotlin.comparisons  PowerManager kotlin.comparisons  R kotlin.comparisons  RECORDINGS_FOLDER kotlin.comparisons  RecordingControlScreen kotlin.comparisons  
RecordingItem kotlin.comparisons  RecordingsListScreen kotlin.comparisons  Regex kotlin.comparisons  RequestBody kotlin.comparisons  Retrofit kotlin.comparisons  Room kotlin.comparisons  Row kotlin.comparisons  Runnable kotlin.comparisons  SERVICE_PERMISSIONS kotlin.comparisons  START_STICKY kotlin.comparisons  STORAGE_PERMISSIONS_LEGACY kotlin.comparisons   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin.comparisons  Settings kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Spacer kotlin.comparisons  SpecialPermissionItem kotlin.comparisons  String kotlin.comparisons  
SupervisorJob kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  TEMP_FOLDER kotlin.comparisons  TIMEOUT_SECONDS kotlin.comparisons  TRANSCRIPTS_FOLDER kotlin.comparisons  TRANSCRIPT_EXTENSION kotlin.comparisons  TelephonyManager kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  TextOverflow kotlin.comparisons  Thread kotlin.comparisons  TimeUnit kotlin.comparisons  TranscriptionQueueStatus kotlin.comparisons  TranscriptionResult kotlin.comparisons  Unit kotlin.comparisons  Uri kotlin.comparisons  Volatile kotlin.comparisons  _currentFile kotlin.comparisons  _currentPosition kotlin.comparisons  	_duration kotlin.comparisons  
_errorMessage kotlin.comparisons  
_isLoading kotlin.comparisons  _playbackState kotlin.comparisons  _recordings kotlin.comparisons  addAll kotlin.comparisons  all kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  any kotlin.comparisons  apiKey kotlin.comparisons  
apiService kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  
asRequestBody kotlin.comparisons  asStateFlow kotlin.comparisons  audioRecord kotlin.comparisons  buildString kotlin.comparisons  cancel kotlin.comparisons  checkAllPermissions kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  context kotlin.comparisons  currentRecordingFile kotlin.comparisons  	emptyList kotlin.comparisons  	extension kotlin.comparisons  fileManager kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  filter kotlin.comparisons  find kotlin.comparisons  flowOf kotlin.comparisons  forEach kotlin.comparisons  forEachIndexed kotlin.comparisons  format kotlin.comparisons  formatDuration kotlin.comparisons  formatFileSize kotlin.comparisons  handleCallLogChange kotlin.comparisons  handleCallStateChange kotlin.comparisons  handleOutgoingCall kotlin.comparisons  height kotlin.comparisons  indexOfFirst kotlin.comparisons  invoke kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  isRecording kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  loadRecordings kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  mediaPlayer kotlin.comparisons  mediaProjection kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  nameWithoutExtension kotlin.comparisons  nextId kotlin.comparisons  padding kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  readText kotlin.comparisons  recordingStartTime kotlin.comparisons  
recordings kotlin.comparisons  rememberScrollState kotlin.comparisons  	removeAll kotlin.comparisons  replace kotlin.comparisons  
repository kotlin.comparisons  serviceScope kotlin.comparisons  set kotlin.comparisons  size kotlin.comparisons  startPositionUpdates kotlin.comparisons  startRecordingService kotlin.comparisons  stopCurrentPlayback kotlin.comparisons  stopPositionUpdates kotlin.comparisons  
stopRecording kotlin.comparisons  stopRecordingService kotlin.comparisons  substringAfterLast kotlin.comparisons  synchronized kotlin.comparisons  toList kotlin.comparisons  toMediaType kotlin.comparisons  toTypedArray kotlin.comparisons  transcribeAudio kotlin.comparisons  use kotlin.comparisons  verticalScroll kotlin.comparisons  width kotlin.comparisons  withContext kotlin.comparisons  	writeText kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  "ACCESSIBILITY_SERVICE_REQUEST_CODE 	kotlin.io  ACTION_START_RECORDING 	kotlin.io  ACTION_STOP_RECORDING 	kotlin.io  AUDIO_EXTENSION 	kotlin.io  AUDIO_MIME_TYPE 	kotlin.io  Activity 	kotlin.io  ActivityCompat 	kotlin.io  ActivityResultContracts 	kotlin.io  	Alignment 	kotlin.io  Arrangement 	kotlin.io  AudioFormat 	kotlin.io  AudioRecord 	kotlin.io  BASE_URL 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  	ByteArray 	kotlin.io  
CHANNEL_ID 	kotlin.io  CORE_PERMISSIONS 	kotlin.io  CallLog 	kotlin.io  CallRecorderApp 	kotlin.io  
CallRecording 	kotlin.io  CallRecordingDatabase 	kotlin.io  CallRecordingDisplay 	kotlin.io  CallRecordingRepository 	kotlin.io  CallRecordingService 	kotlin.io  CallRecordingsViewModel 	kotlin.io  CallType 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  Checkbox 	kotlin.io  CircularProgressIndicator 	kotlin.io  Class 	kotlin.io  Column 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  
Converters 	kotlin.io  CoroutineScope 	kotlin.io  CostEstimate 	kotlin.io  Date 	kotlin.io  Dispatchers 	kotlin.io  Divider 	kotlin.io  DropdownMenu 	kotlin.io  DropdownMenuItem 	kotlin.io  EXTRA_CALL_TYPE 	kotlin.io  EXTRA_CONTACT_NAME 	kotlin.io  EXTRA_MEDIA_PROJECTION_DATA 	kotlin.io  EXTRA_PHONE_NUMBER 	kotlin.io  EmptyRecordingsState 	kotlin.io  Environment 	kotlin.io  ErrorResult 	kotlin.io  	ErrorType 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileManager 	kotlin.io  FileOutputStream 	kotlin.io  FileProvider 	kotlin.io  
FontWeight 	kotlin.io  GsonConverterFactory 	kotlin.io  Handler 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  IllegalStateException 	kotlin.io  Intent 	kotlin.io  IntentFilter 	kotlin.io  
LazyColumn 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE 	kotlin.io  
MAX_FILE_SIZE 	kotlin.io  Manifest 	kotlin.io  
MaterialTheme 	kotlin.io  MediaPlayer 	kotlin.io  
MediaRecorder 	kotlin.io  Modifier 	kotlin.io  
MultipartBody 	kotlin.io  MutableStateFlow 	kotlin.io  NETWORK_PERMISSIONS 	kotlin.io  NOTIFICATION_ID 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  OkHttpClient 	kotlin.io  OnConflictStrategy 	kotlin.io  OpenAIApiService 	kotlin.io  OutlinedButton 	kotlin.io  PERMISSION_REQUEST_CODE 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io  PermissionItem 	kotlin.io  PermissionManager 	kotlin.io  PhoneStateListener 	kotlin.io  
PlaybackState 	kotlin.io  PowerManager 	kotlin.io  R 	kotlin.io  RECORDINGS_FOLDER 	kotlin.io  RecordingControlScreen 	kotlin.io  
RecordingItem 	kotlin.io  RecordingsListScreen 	kotlin.io  Regex 	kotlin.io  RequestBody 	kotlin.io  Retrofit 	kotlin.io  Room 	kotlin.io  Row 	kotlin.io  Runnable 	kotlin.io  SERVICE_PERMISSIONS 	kotlin.io  START_STICKY 	kotlin.io  STORAGE_PERMISSIONS_LEGACY 	kotlin.io   SYSTEM_ALERT_WINDOW_REQUEST_CODE 	kotlin.io  Settings 	kotlin.io  SimpleDateFormat 	kotlin.io  Spacer 	kotlin.io  SpecialPermissionItem 	kotlin.io  String 	kotlin.io  
SupervisorJob 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  TEMP_FOLDER 	kotlin.io  TIMEOUT_SECONDS 	kotlin.io  TRANSCRIPTS_FOLDER 	kotlin.io  TRANSCRIPT_EXTENSION 	kotlin.io  TelephonyManager 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  TextOverflow 	kotlin.io  Thread 	kotlin.io  TimeUnit 	kotlin.io  TranscriptionQueueStatus 	kotlin.io  TranscriptionResult 	kotlin.io  Unit 	kotlin.io  Uri 	kotlin.io  Volatile 	kotlin.io  _currentFile 	kotlin.io  _currentPosition 	kotlin.io  	_duration 	kotlin.io  
_errorMessage 	kotlin.io  
_isLoading 	kotlin.io  _playbackState 	kotlin.io  _recordings 	kotlin.io  addAll 	kotlin.io  all 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  any 	kotlin.io  apiKey 	kotlin.io  
apiService 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  
asRequestBody 	kotlin.io  asStateFlow 	kotlin.io  audioRecord 	kotlin.io  buildString 	kotlin.io  cancel 	kotlin.io  checkAllPermissions 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  context 	kotlin.io  currentRecordingFile 	kotlin.io  	emptyList 	kotlin.io  	extension 	kotlin.io  fileManager 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  filter 	kotlin.io  find 	kotlin.io  flowOf 	kotlin.io  forEach 	kotlin.io  forEachIndexed 	kotlin.io  format 	kotlin.io  formatDuration 	kotlin.io  formatFileSize 	kotlin.io  handleCallLogChange 	kotlin.io  handleCallStateChange 	kotlin.io  handleOutgoingCall 	kotlin.io  height 	kotlin.io  indexOfFirst 	kotlin.io  invoke 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  isRecording 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  loadRecordings 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  mediaPlayer 	kotlin.io  mediaProjection 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  nameWithoutExtension 	kotlin.io  nextId 	kotlin.io  padding 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  readText 	kotlin.io  recordingStartTime 	kotlin.io  
recordings 	kotlin.io  rememberScrollState 	kotlin.io  	removeAll 	kotlin.io  replace 	kotlin.io  
repository 	kotlin.io  serviceScope 	kotlin.io  set 	kotlin.io  size 	kotlin.io  startPositionUpdates 	kotlin.io  startRecordingService 	kotlin.io  stopCurrentPlayback 	kotlin.io  stopPositionUpdates 	kotlin.io  
stopRecording 	kotlin.io  stopRecordingService 	kotlin.io  substringAfterLast 	kotlin.io  synchronized 	kotlin.io  toList 	kotlin.io  toMediaType 	kotlin.io  toTypedArray 	kotlin.io  transcribeAudio 	kotlin.io  use 	kotlin.io  verticalScroll 	kotlin.io  width 	kotlin.io  withContext 	kotlin.io  	writeText 	kotlin.io  "ACCESSIBILITY_SERVICE_REQUEST_CODE 
kotlin.jvm  ACTION_START_RECORDING 
kotlin.jvm  ACTION_STOP_RECORDING 
kotlin.jvm  AUDIO_EXTENSION 
kotlin.jvm  AUDIO_MIME_TYPE 
kotlin.jvm  Activity 
kotlin.jvm  ActivityCompat 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  AudioFormat 
kotlin.jvm  AudioRecord 
kotlin.jvm  BASE_URL 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  	ByteArray 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  CORE_PERMISSIONS 
kotlin.jvm  CallLog 
kotlin.jvm  CallRecorderApp 
kotlin.jvm  
CallRecording 
kotlin.jvm  CallRecordingDatabase 
kotlin.jvm  CallRecordingDisplay 
kotlin.jvm  CallRecordingRepository 
kotlin.jvm  CallRecordingService 
kotlin.jvm  CallRecordingsViewModel 
kotlin.jvm  CallType 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  Checkbox 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Class 
kotlin.jvm  Column 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  
Converters 
kotlin.jvm  CoroutineScope 
kotlin.jvm  CostEstimate 
kotlin.jvm  Date 
kotlin.jvm  Dispatchers 
kotlin.jvm  Divider 
kotlin.jvm  DropdownMenu 
kotlin.jvm  DropdownMenuItem 
kotlin.jvm  EXTRA_CALL_TYPE 
kotlin.jvm  EXTRA_CONTACT_NAME 
kotlin.jvm  EXTRA_MEDIA_PROJECTION_DATA 
kotlin.jvm  EXTRA_PHONE_NUMBER 
kotlin.jvm  EmptyRecordingsState 
kotlin.jvm  Environment 
kotlin.jvm  ErrorResult 
kotlin.jvm  	ErrorType 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileManager 
kotlin.jvm  FileOutputStream 
kotlin.jvm  FileProvider 
kotlin.jvm  
FontWeight 
kotlin.jvm  GsonConverterFactory 
kotlin.jvm  Handler 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  IllegalStateException 
kotlin.jvm  Intent 
kotlin.jvm  IntentFilter 
kotlin.jvm  
LazyColumn 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE 
kotlin.jvm  
MAX_FILE_SIZE 
kotlin.jvm  Manifest 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  MediaPlayer 
kotlin.jvm  
MediaRecorder 
kotlin.jvm  Modifier 
kotlin.jvm  
MultipartBody 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NETWORK_PERMISSIONS 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  OkHttpClient 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OpenAIApiService 
kotlin.jvm  OutlinedButton 
kotlin.jvm  PERMISSION_REQUEST_CODE 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  PermissionItem 
kotlin.jvm  PermissionManager 
kotlin.jvm  PhoneStateListener 
kotlin.jvm  
PlaybackState 
kotlin.jvm  PowerManager 
kotlin.jvm  R 
kotlin.jvm  RECORDINGS_FOLDER 
kotlin.jvm  RecordingControlScreen 
kotlin.jvm  
RecordingItem 
kotlin.jvm  RecordingsListScreen 
kotlin.jvm  Regex 
kotlin.jvm  RequestBody 
kotlin.jvm  Retrofit 
kotlin.jvm  Room 
kotlin.jvm  Row 
kotlin.jvm  Runnable 
kotlin.jvm  SERVICE_PERMISSIONS 
kotlin.jvm  START_STICKY 
kotlin.jvm  STORAGE_PERMISSIONS_LEGACY 
kotlin.jvm   SYSTEM_ALERT_WINDOW_REQUEST_CODE 
kotlin.jvm  Settings 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Spacer 
kotlin.jvm  SpecialPermissionItem 
kotlin.jvm  String 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  TEMP_FOLDER 
kotlin.jvm  TIMEOUT_SECONDS 
kotlin.jvm  TRANSCRIPTS_FOLDER 
kotlin.jvm  TRANSCRIPT_EXTENSION 
kotlin.jvm  TelephonyManager 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  TextOverflow 
kotlin.jvm  Thread 
kotlin.jvm  TimeUnit 
kotlin.jvm  TranscriptionQueueStatus 
kotlin.jvm  TranscriptionResult 
kotlin.jvm  Unit 
kotlin.jvm  Uri 
kotlin.jvm  Volatile 
kotlin.jvm  _currentFile 
kotlin.jvm  _currentPosition 
kotlin.jvm  	_duration 
kotlin.jvm  
_errorMessage 
kotlin.jvm  
_isLoading 
kotlin.jvm  _playbackState 
kotlin.jvm  _recordings 
kotlin.jvm  addAll 
kotlin.jvm  all 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  any 
kotlin.jvm  apiKey 
kotlin.jvm  
apiService 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  
asRequestBody 
kotlin.jvm  asStateFlow 
kotlin.jvm  audioRecord 
kotlin.jvm  buildString 
kotlin.jvm  cancel 
kotlin.jvm  checkAllPermissions 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  context 
kotlin.jvm  currentRecordingFile 
kotlin.jvm  	emptyList 
kotlin.jvm  	extension 
kotlin.jvm  fileManager 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  filter 
kotlin.jvm  find 
kotlin.jvm  flowOf 
kotlin.jvm  forEach 
kotlin.jvm  forEachIndexed 
kotlin.jvm  format 
kotlin.jvm  formatDuration 
kotlin.jvm  formatFileSize 
kotlin.jvm  handleCallLogChange 
kotlin.jvm  handleCallStateChange 
kotlin.jvm  handleOutgoingCall 
kotlin.jvm  height 
kotlin.jvm  indexOfFirst 
kotlin.jvm  invoke 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  isRecording 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  loadRecordings 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  mediaPlayer 
kotlin.jvm  mediaProjection 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  nameWithoutExtension 
kotlin.jvm  nextId 
kotlin.jvm  padding 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  readText 
kotlin.jvm  recordingStartTime 
kotlin.jvm  
recordings 
kotlin.jvm  rememberScrollState 
kotlin.jvm  	removeAll 
kotlin.jvm  replace 
kotlin.jvm  
repository 
kotlin.jvm  serviceScope 
kotlin.jvm  set 
kotlin.jvm  size 
kotlin.jvm  startPositionUpdates 
kotlin.jvm  startRecordingService 
kotlin.jvm  stopCurrentPlayback 
kotlin.jvm  stopPositionUpdates 
kotlin.jvm  
stopRecording 
kotlin.jvm  stopRecordingService 
kotlin.jvm  substringAfterLast 
kotlin.jvm  synchronized 
kotlin.jvm  toList 
kotlin.jvm  toMediaType 
kotlin.jvm  toTypedArray 
kotlin.jvm  transcribeAudio 
kotlin.jvm  use 
kotlin.jvm  verticalScroll 
kotlin.jvm  width 
kotlin.jvm  withContext 
kotlin.jvm  	writeText 
kotlin.jvm  "ACCESSIBILITY_SERVICE_REQUEST_CODE 
kotlin.ranges  ACTION_START_RECORDING 
kotlin.ranges  ACTION_STOP_RECORDING 
kotlin.ranges  AUDIO_EXTENSION 
kotlin.ranges  AUDIO_MIME_TYPE 
kotlin.ranges  Activity 
kotlin.ranges  ActivityCompat 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  AudioFormat 
kotlin.ranges  AudioRecord 
kotlin.ranges  BASE_URL 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  	ByteArray 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  CORE_PERMISSIONS 
kotlin.ranges  CallLog 
kotlin.ranges  CallRecorderApp 
kotlin.ranges  
CallRecording 
kotlin.ranges  CallRecordingDatabase 
kotlin.ranges  CallRecordingDisplay 
kotlin.ranges  CallRecordingRepository 
kotlin.ranges  CallRecordingService 
kotlin.ranges  CallRecordingsViewModel 
kotlin.ranges  CallType 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  Checkbox 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Class 
kotlin.ranges  Column 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  
Converters 
kotlin.ranges  CoroutineScope 
kotlin.ranges  CostEstimate 
kotlin.ranges  Date 
kotlin.ranges  Dispatchers 
kotlin.ranges  Divider 
kotlin.ranges  DropdownMenu 
kotlin.ranges  DropdownMenuItem 
kotlin.ranges  EXTRA_CALL_TYPE 
kotlin.ranges  EXTRA_CONTACT_NAME 
kotlin.ranges  EXTRA_MEDIA_PROJECTION_DATA 
kotlin.ranges  EXTRA_PHONE_NUMBER 
kotlin.ranges  EmptyRecordingsState 
kotlin.ranges  Environment 
kotlin.ranges  ErrorResult 
kotlin.ranges  	ErrorType 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileManager 
kotlin.ranges  FileOutputStream 
kotlin.ranges  FileProvider 
kotlin.ranges  
FontWeight 
kotlin.ranges  GsonConverterFactory 
kotlin.ranges  Handler 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IllegalStateException 
kotlin.ranges  Intent 
kotlin.ranges  IntentFilter 
kotlin.ranges  
LazyColumn 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE 
kotlin.ranges  
MAX_FILE_SIZE 
kotlin.ranges  Manifest 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  MediaPlayer 
kotlin.ranges  
MediaRecorder 
kotlin.ranges  Modifier 
kotlin.ranges  
MultipartBody 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NETWORK_PERMISSIONS 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  OkHttpClient 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OpenAIApiService 
kotlin.ranges  OutlinedButton 
kotlin.ranges  PERMISSION_REQUEST_CODE 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  PermissionItem 
kotlin.ranges  PermissionManager 
kotlin.ranges  PhoneStateListener 
kotlin.ranges  
PlaybackState 
kotlin.ranges  PowerManager 
kotlin.ranges  R 
kotlin.ranges  RECORDINGS_FOLDER 
kotlin.ranges  RecordingControlScreen 
kotlin.ranges  
RecordingItem 
kotlin.ranges  RecordingsListScreen 
kotlin.ranges  Regex 
kotlin.ranges  RequestBody 
kotlin.ranges  Retrofit 
kotlin.ranges  Room 
kotlin.ranges  Row 
kotlin.ranges  Runnable 
kotlin.ranges  SERVICE_PERMISSIONS 
kotlin.ranges  START_STICKY 
kotlin.ranges  STORAGE_PERMISSIONS_LEGACY 
kotlin.ranges   SYSTEM_ALERT_WINDOW_REQUEST_CODE 
kotlin.ranges  Settings 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Spacer 
kotlin.ranges  SpecialPermissionItem 
kotlin.ranges  String 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  TEMP_FOLDER 
kotlin.ranges  TIMEOUT_SECONDS 
kotlin.ranges  TRANSCRIPTS_FOLDER 
kotlin.ranges  TRANSCRIPT_EXTENSION 
kotlin.ranges  TelephonyManager 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  TextOverflow 
kotlin.ranges  Thread 
kotlin.ranges  TimeUnit 
kotlin.ranges  TranscriptionQueueStatus 
kotlin.ranges  TranscriptionResult 
kotlin.ranges  Unit 
kotlin.ranges  Uri 
kotlin.ranges  Volatile 
kotlin.ranges  _currentFile 
kotlin.ranges  _currentPosition 
kotlin.ranges  	_duration 
kotlin.ranges  
_errorMessage 
kotlin.ranges  
_isLoading 
kotlin.ranges  _playbackState 
kotlin.ranges  _recordings 
kotlin.ranges  addAll 
kotlin.ranges  all 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  any 
kotlin.ranges  apiKey 
kotlin.ranges  
apiService 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  
asRequestBody 
kotlin.ranges  asStateFlow 
kotlin.ranges  audioRecord 
kotlin.ranges  buildString 
kotlin.ranges  cancel 
kotlin.ranges  checkAllPermissions 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  context 
kotlin.ranges  currentRecordingFile 
kotlin.ranges  	emptyList 
kotlin.ranges  	extension 
kotlin.ranges  fileManager 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  filter 
kotlin.ranges  find 
kotlin.ranges  flowOf 
kotlin.ranges  forEach 
kotlin.ranges  forEachIndexed 
kotlin.ranges  format 
kotlin.ranges  formatDuration 
kotlin.ranges  formatFileSize 
kotlin.ranges  handleCallLogChange 
kotlin.ranges  handleCallStateChange 
kotlin.ranges  handleOutgoingCall 
kotlin.ranges  height 
kotlin.ranges  indexOfFirst 
kotlin.ranges  invoke 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  isRecording 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  loadRecordings 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  mediaPlayer 
kotlin.ranges  mediaProjection 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  nameWithoutExtension 
kotlin.ranges  nextId 
kotlin.ranges  padding 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  readText 
kotlin.ranges  recordingStartTime 
kotlin.ranges  
recordings 
kotlin.ranges  rememberScrollState 
kotlin.ranges  	removeAll 
kotlin.ranges  replace 
kotlin.ranges  
repository 
kotlin.ranges  serviceScope 
kotlin.ranges  set 
kotlin.ranges  size 
kotlin.ranges  startPositionUpdates 
kotlin.ranges  startRecordingService 
kotlin.ranges  stopCurrentPlayback 
kotlin.ranges  stopPositionUpdates 
kotlin.ranges  
stopRecording 
kotlin.ranges  stopRecordingService 
kotlin.ranges  substringAfterLast 
kotlin.ranges  synchronized 
kotlin.ranges  toList 
kotlin.ranges  toMediaType 
kotlin.ranges  toTypedArray 
kotlin.ranges  transcribeAudio 
kotlin.ranges  use 
kotlin.ranges  verticalScroll 
kotlin.ranges  width 
kotlin.ranges  withContext 
kotlin.ranges  	writeText 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin.sequences  ACTION_START_RECORDING kotlin.sequences  ACTION_STOP_RECORDING kotlin.sequences  AUDIO_EXTENSION kotlin.sequences  AUDIO_MIME_TYPE kotlin.sequences  Activity kotlin.sequences  ActivityCompat kotlin.sequences  ActivityResultContracts kotlin.sequences  	Alignment kotlin.sequences  Arrangement kotlin.sequences  AudioFormat kotlin.sequences  AudioRecord kotlin.sequences  BASE_URL kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  	ByteArray kotlin.sequences  
CHANNEL_ID kotlin.sequences  CORE_PERMISSIONS kotlin.sequences  CallLog kotlin.sequences  CallRecorderApp kotlin.sequences  
CallRecording kotlin.sequences  CallRecordingDatabase kotlin.sequences  CallRecordingDisplay kotlin.sequences  CallRecordingRepository kotlin.sequences  CallRecordingService kotlin.sequences  CallRecordingsViewModel kotlin.sequences  CallType kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  Checkbox kotlin.sequences  CircularProgressIndicator kotlin.sequences  Class kotlin.sequences  Column kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  
Converters kotlin.sequences  CoroutineScope kotlin.sequences  CostEstimate kotlin.sequences  Date kotlin.sequences  Dispatchers kotlin.sequences  Divider kotlin.sequences  DropdownMenu kotlin.sequences  DropdownMenuItem kotlin.sequences  EXTRA_CALL_TYPE kotlin.sequences  EXTRA_CONTACT_NAME kotlin.sequences  EXTRA_MEDIA_PROJECTION_DATA kotlin.sequences  EXTRA_PHONE_NUMBER kotlin.sequences  EmptyRecordingsState kotlin.sequences  Environment kotlin.sequences  ErrorResult kotlin.sequences  	ErrorType kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileManager kotlin.sequences  FileOutputStream kotlin.sequences  FileProvider kotlin.sequences  
FontWeight kotlin.sequences  GsonConverterFactory kotlin.sequences  Handler kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  IllegalStateException kotlin.sequences  Intent kotlin.sequences  IntentFilter kotlin.sequences  
LazyColumn kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin.sequences  
MAX_FILE_SIZE kotlin.sequences  Manifest kotlin.sequences  
MaterialTheme kotlin.sequences  MediaPlayer kotlin.sequences  
MediaRecorder kotlin.sequences  Modifier kotlin.sequences  
MultipartBody kotlin.sequences  MutableStateFlow kotlin.sequences  NETWORK_PERMISSIONS kotlin.sequences  NOTIFICATION_ID kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  OkHttpClient kotlin.sequences  OnConflictStrategy kotlin.sequences  OpenAIApiService kotlin.sequences  OutlinedButton kotlin.sequences  PERMISSION_REQUEST_CODE kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences  PermissionItem kotlin.sequences  PermissionManager kotlin.sequences  PhoneStateListener kotlin.sequences  
PlaybackState kotlin.sequences  PowerManager kotlin.sequences  R kotlin.sequences  RECORDINGS_FOLDER kotlin.sequences  RecordingControlScreen kotlin.sequences  
RecordingItem kotlin.sequences  RecordingsListScreen kotlin.sequences  Regex kotlin.sequences  RequestBody kotlin.sequences  Retrofit kotlin.sequences  Room kotlin.sequences  Row kotlin.sequences  Runnable kotlin.sequences  SERVICE_PERMISSIONS kotlin.sequences  START_STICKY kotlin.sequences  STORAGE_PERMISSIONS_LEGACY kotlin.sequences   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin.sequences  Settings kotlin.sequences  SimpleDateFormat kotlin.sequences  Spacer kotlin.sequences  SpecialPermissionItem kotlin.sequences  String kotlin.sequences  
SupervisorJob kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  TEMP_FOLDER kotlin.sequences  TIMEOUT_SECONDS kotlin.sequences  TRANSCRIPTS_FOLDER kotlin.sequences  TRANSCRIPT_EXTENSION kotlin.sequences  TelephonyManager kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  TextOverflow kotlin.sequences  Thread kotlin.sequences  TimeUnit kotlin.sequences  TranscriptionQueueStatus kotlin.sequences  TranscriptionResult kotlin.sequences  Unit kotlin.sequences  Uri kotlin.sequences  Volatile kotlin.sequences  _currentFile kotlin.sequences  _currentPosition kotlin.sequences  	_duration kotlin.sequences  
_errorMessage kotlin.sequences  
_isLoading kotlin.sequences  _playbackState kotlin.sequences  _recordings kotlin.sequences  addAll kotlin.sequences  all kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  any kotlin.sequences  apiKey kotlin.sequences  
apiService kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  
asRequestBody kotlin.sequences  asStateFlow kotlin.sequences  audioRecord kotlin.sequences  buildString kotlin.sequences  cancel kotlin.sequences  checkAllPermissions kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  context kotlin.sequences  currentRecordingFile kotlin.sequences  	emptyList kotlin.sequences  	extension kotlin.sequences  fileManager kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  flowOf kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  format kotlin.sequences  formatDuration kotlin.sequences  formatFileSize kotlin.sequences  handleCallLogChange kotlin.sequences  handleCallStateChange kotlin.sequences  handleOutgoingCall kotlin.sequences  height kotlin.sequences  indexOfFirst kotlin.sequences  invoke kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  isRecording kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  loadRecordings kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  mediaPlayer kotlin.sequences  mediaProjection kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  nameWithoutExtension kotlin.sequences  nextId kotlin.sequences  padding kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  readText kotlin.sequences  recordingStartTime kotlin.sequences  
recordings kotlin.sequences  rememberScrollState kotlin.sequences  	removeAll kotlin.sequences  replace kotlin.sequences  
repository kotlin.sequences  serviceScope kotlin.sequences  set kotlin.sequences  size kotlin.sequences  startPositionUpdates kotlin.sequences  startRecordingService kotlin.sequences  stopCurrentPlayback kotlin.sequences  stopPositionUpdates kotlin.sequences  
stopRecording kotlin.sequences  stopRecordingService kotlin.sequences  substringAfterLast kotlin.sequences  synchronized kotlin.sequences  toList kotlin.sequences  toMediaType kotlin.sequences  toTypedArray kotlin.sequences  transcribeAudio kotlin.sequences  use kotlin.sequences  verticalScroll kotlin.sequences  width kotlin.sequences  withContext kotlin.sequences  	writeText kotlin.sequences  "ACCESSIBILITY_SERVICE_REQUEST_CODE kotlin.text  ACTION_START_RECORDING kotlin.text  ACTION_STOP_RECORDING kotlin.text  AUDIO_EXTENSION kotlin.text  AUDIO_MIME_TYPE kotlin.text  Activity kotlin.text  ActivityCompat kotlin.text  ActivityResultContracts kotlin.text  	Alignment kotlin.text  Arrangement kotlin.text  AudioFormat kotlin.text  AudioRecord kotlin.text  BASE_URL kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  	ByteArray kotlin.text  
CHANNEL_ID kotlin.text  CORE_PERMISSIONS kotlin.text  CallLog kotlin.text  CallRecorderApp kotlin.text  
CallRecording kotlin.text  CallRecordingDatabase kotlin.text  CallRecordingDisplay kotlin.text  CallRecordingRepository kotlin.text  CallRecordingService kotlin.text  CallRecordingsViewModel kotlin.text  CallType kotlin.text  Card kotlin.text  CardDefaults kotlin.text  Checkbox kotlin.text  CircularProgressIndicator kotlin.text  Class kotlin.text  Column kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  
Converters kotlin.text  CoroutineScope kotlin.text  CostEstimate kotlin.text  Date kotlin.text  Dispatchers kotlin.text  Divider kotlin.text  DropdownMenu kotlin.text  DropdownMenuItem kotlin.text  EXTRA_CALL_TYPE kotlin.text  EXTRA_CONTACT_NAME kotlin.text  EXTRA_MEDIA_PROJECTION_DATA kotlin.text  EXTRA_PHONE_NUMBER kotlin.text  EmptyRecordingsState kotlin.text  Environment kotlin.text  ErrorResult kotlin.text  	ErrorType kotlin.text  	Exception kotlin.text  File kotlin.text  FileManager kotlin.text  FileOutputStream kotlin.text  FileProvider kotlin.text  
FontWeight kotlin.text  GsonConverterFactory kotlin.text  Handler kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  IllegalStateException kotlin.text  Intent kotlin.text  IntentFilter kotlin.text  
LazyColumn kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  $MANAGE_EXTERNAL_STORAGE_REQUEST_CODE kotlin.text  
MAX_FILE_SIZE kotlin.text  Manifest kotlin.text  
MaterialTheme kotlin.text  MediaPlayer kotlin.text  
MediaRecorder kotlin.text  Modifier kotlin.text  
MultipartBody kotlin.text  MutableStateFlow kotlin.text  NETWORK_PERMISSIONS kotlin.text  NOTIFICATION_ID kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  OkHttpClient kotlin.text  OnConflictStrategy kotlin.text  OpenAIApiService kotlin.text  OutlinedButton kotlin.text  PERMISSION_REQUEST_CODE kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text  PermissionItem kotlin.text  PermissionManager kotlin.text  PhoneStateListener kotlin.text  
PlaybackState kotlin.text  PowerManager kotlin.text  R kotlin.text  RECORDINGS_FOLDER kotlin.text  RecordingControlScreen kotlin.text  
RecordingItem kotlin.text  RecordingsListScreen kotlin.text  Regex kotlin.text  RequestBody kotlin.text  Retrofit kotlin.text  Room kotlin.text  Row kotlin.text  Runnable kotlin.text  SERVICE_PERMISSIONS kotlin.text  START_STICKY kotlin.text  STORAGE_PERMISSIONS_LEGACY kotlin.text   SYSTEM_ALERT_WINDOW_REQUEST_CODE kotlin.text  Settings kotlin.text  SimpleDateFormat kotlin.text  Spacer kotlin.text  SpecialPermissionItem kotlin.text  String kotlin.text  
SupervisorJob kotlin.text  System kotlin.text  TAG kotlin.text  TEMP_FOLDER kotlin.text  TIMEOUT_SECONDS kotlin.text  TRANSCRIPTS_FOLDER kotlin.text  TRANSCRIPT_EXTENSION kotlin.text  TelephonyManager kotlin.text  Text kotlin.text  	TextAlign kotlin.text  TextOverflow kotlin.text  Thread kotlin.text  TimeUnit kotlin.text  TranscriptionQueueStatus kotlin.text  TranscriptionResult kotlin.text  Unit kotlin.text  Uri kotlin.text  Volatile kotlin.text  _currentFile kotlin.text  _currentPosition kotlin.text  	_duration kotlin.text  
_errorMessage kotlin.text  
_isLoading kotlin.text  _playbackState kotlin.text  _recordings kotlin.text  addAll kotlin.text  all kotlin.text  android kotlin.text  androidx kotlin.text  any kotlin.text  apiKey kotlin.text  
apiService kotlin.text  apply kotlin.text  arrayOf kotlin.text  
asRequestBody kotlin.text  asStateFlow kotlin.text  audioRecord kotlin.text  buildString kotlin.text  cancel kotlin.text  checkAllPermissions kotlin.text  com kotlin.text  contains kotlin.text  context kotlin.text  currentRecordingFile kotlin.text  	emptyList kotlin.text  	extension kotlin.text  fileManager kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  filter kotlin.text  find kotlin.text  flowOf kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  formatDuration kotlin.text  formatFileSize kotlin.text  handleCallLogChange kotlin.text  handleCallStateChange kotlin.text  handleOutgoingCall kotlin.text  height kotlin.text  indexOfFirst kotlin.text  invoke kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  isRecording kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  loadRecordings kotlin.text  	lowercase kotlin.text  map kotlin.text  mapOf kotlin.text  mediaPlayer kotlin.text  mediaProjection kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  nameWithoutExtension kotlin.text  nextId kotlin.text  padding kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  readText kotlin.text  recordingStartTime kotlin.text  
recordings kotlin.text  rememberScrollState kotlin.text  	removeAll kotlin.text  replace kotlin.text  
repository kotlin.text  serviceScope kotlin.text  set kotlin.text  size kotlin.text  startPositionUpdates kotlin.text  startRecordingService kotlin.text  stopCurrentPlayback kotlin.text  stopPositionUpdates kotlin.text  
stopRecording kotlin.text  stopRecordingService kotlin.text  substringAfterLast kotlin.text  synchronized kotlin.text  toList kotlin.text  toMediaType kotlin.text  toTypedArray kotlin.text  transcribeAudio kotlin.text  use kotlin.text  verticalScroll kotlin.text  width kotlin.text  withContext kotlin.text  	writeText kotlin.text  invoke kotlin.text.Regex.Companion  ACTION_START_RECORDING kotlinx.coroutines  ACTION_STOP_RECORDING kotlinx.coroutines  Activity kotlinx.coroutines  AudioFormat kotlinx.coroutines  AudioRecord kotlinx.coroutines  Build kotlinx.coroutines  	ByteArray kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CallLog kotlinx.coroutines  
CallRecording kotlinx.coroutines  CallRecordingRepository kotlinx.coroutines  CallRecordingService kotlinx.coroutines  CallType kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Date kotlinx.coroutines  Dispatchers kotlinx.coroutines  EXTRA_CALL_TYPE kotlinx.coroutines  EXTRA_CONTACT_NAME kotlinx.coroutines  EXTRA_MEDIA_PROJECTION_DATA kotlinx.coroutines  EXTRA_PHONE_NUMBER kotlinx.coroutines  	Exception kotlinx.coroutines  File kotlinx.coroutines  FileOutputStream kotlinx.coroutines  Handler kotlinx.coroutines  IllegalStateException kotlinx.coroutines  Intent kotlinx.coroutines  IntentFilter kotlinx.coroutines  Job kotlinx.coroutines  Locale kotlinx.coroutines  Log kotlinx.coroutines  Looper kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  
MediaRecorder kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  
PendingIntent kotlinx.coroutines  PermissionManager kotlinx.coroutines  PhoneStateListener kotlinx.coroutines  PowerManager kotlinx.coroutines  R kotlinx.coroutines  START_STICKY kotlinx.coroutines  Service kotlinx.coroutines  SimpleDateFormat kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  TelephonyManager kotlinx.coroutines  Thread kotlinx.coroutines  Uri kotlinx.coroutines  android kotlinx.coroutines  apply kotlinx.coroutines  arrayOf kotlinx.coroutines  audioRecord kotlinx.coroutines  cancel kotlinx.coroutines  currentRecordingFile kotlinx.coroutines  handleCallLogChange kotlinx.coroutines  handleCallStateChange kotlinx.coroutines  handleOutgoingCall kotlinx.coroutines  isRecording kotlinx.coroutines  java kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  mediaProjection kotlinx.coroutines  recordingStartTime kotlinx.coroutines  
repository kotlinx.coroutines  serviceScope kotlinx.coroutines  
stopRecording kotlinx.coroutines  use kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  AudioFormat !kotlinx.coroutines.CoroutineScope  AudioRecord !kotlinx.coroutines.CoroutineScope  Build !kotlinx.coroutines.CoroutineScope  	ByteArray !kotlinx.coroutines.CoroutineScope  
CallRecording !kotlinx.coroutines.CoroutineScope  CallType !kotlinx.coroutines.CoroutineScope  Date !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  IllegalStateException !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  
MAX_FILE_SIZE !kotlinx.coroutines.CoroutineScope  MediaPlayer !kotlinx.coroutines.CoroutineScope  
MediaRecorder !kotlinx.coroutines.CoroutineScope  
MultipartBody !kotlinx.coroutines.CoroutineScope  RequestBody !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Thread !kotlinx.coroutines.CoroutineScope  TranscriptionResult !kotlinx.coroutines.CoroutineScope  Unit !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _recordings !kotlinx.coroutines.CoroutineScope  all !kotlinx.coroutines.CoroutineScope  apiKey !kotlinx.coroutines.CoroutineScope  
apiService !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  
asRequestBody !kotlinx.coroutines.CoroutineScope  audioRecord !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  checkAllPermissions !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  currentRecordingFile !kotlinx.coroutines.CoroutineScope  fileManager !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  getALL !kotlinx.coroutines.CoroutineScope  	getAPIKey !kotlinx.coroutines.CoroutineScope  
getAPIService !kotlinx.coroutines.CoroutineScope  getAPPLY !kotlinx.coroutines.CoroutineScope  getASRequestBody !kotlinx.coroutines.CoroutineScope  getAUDIORecord !kotlinx.coroutines.CoroutineScope  getAll !kotlinx.coroutines.CoroutineScope  	getApiKey !kotlinx.coroutines.CoroutineScope  
getApiService !kotlinx.coroutines.CoroutineScope  getApply !kotlinx.coroutines.CoroutineScope  getAsRequestBody !kotlinx.coroutines.CoroutineScope  getAudioRecord !kotlinx.coroutines.CoroutineScope  	getCANCEL !kotlinx.coroutines.CoroutineScope  getCHECKAllPermissions !kotlinx.coroutines.CoroutineScope  getCONTAINS !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  getCURRENTRecordingFile !kotlinx.coroutines.CoroutineScope  	getCancel !kotlinx.coroutines.CoroutineScope  getCheckAllPermissions !kotlinx.coroutines.CoroutineScope  getContains !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getCurrentRecordingFile !kotlinx.coroutines.CoroutineScope  getFILEManager !kotlinx.coroutines.CoroutineScope  getFIND !kotlinx.coroutines.CoroutineScope  getFileManager !kotlinx.coroutines.CoroutineScope  getFind !kotlinx.coroutines.CoroutineScope  getHANDLECallLogChange !kotlinx.coroutines.CoroutineScope  getHandleCallLogChange !kotlinx.coroutines.CoroutineScope  getINDEXOfFirst !kotlinx.coroutines.CoroutineScope  getISNullOrBlank !kotlinx.coroutines.CoroutineScope  getISRecording !kotlinx.coroutines.CoroutineScope  getIndexOfFirst !kotlinx.coroutines.CoroutineScope  getIsNullOrBlank !kotlinx.coroutines.CoroutineScope  getIsRecording !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLOADRecordings !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getLoadRecordings !kotlinx.coroutines.CoroutineScope  getMEDIAPlayer !kotlinx.coroutines.CoroutineScope  getMEDIAProjection !kotlinx.coroutines.CoroutineScope  getMediaPlayer !kotlinx.coroutines.CoroutineScope  getMediaProjection !kotlinx.coroutines.CoroutineScope  	getNEXTId !kotlinx.coroutines.CoroutineScope  	getNextId !kotlinx.coroutines.CoroutineScope  
getRECORDINGS !kotlinx.coroutines.CoroutineScope  getRECORDINGStartTime !kotlinx.coroutines.CoroutineScope  getREMOVEAll !kotlinx.coroutines.CoroutineScope  
getREPOSITORY !kotlinx.coroutines.CoroutineScope  getRecordingStartTime !kotlinx.coroutines.CoroutineScope  
getRecordings !kotlinx.coroutines.CoroutineScope  getRemoveAll !kotlinx.coroutines.CoroutineScope  
getRepository !kotlinx.coroutines.CoroutineScope  getSTOPCurrentPlayback !kotlinx.coroutines.CoroutineScope  getSTOPRecording !kotlinx.coroutines.CoroutineScope  getStopCurrentPlayback !kotlinx.coroutines.CoroutineScope  getStopRecording !kotlinx.coroutines.CoroutineScope  getTOMediaType !kotlinx.coroutines.CoroutineScope  getTRANSCRIBEAudio !kotlinx.coroutines.CoroutineScope  getToMediaType !kotlinx.coroutines.CoroutineScope  getTranscribeAudio !kotlinx.coroutines.CoroutineScope  getWRITEText !kotlinx.coroutines.CoroutineScope  getWriteText !kotlinx.coroutines.CoroutineScope  get_errorMessage !kotlinx.coroutines.CoroutineScope  
get_isLoading !kotlinx.coroutines.CoroutineScope  get_recordings !kotlinx.coroutines.CoroutineScope  handleCallLogChange !kotlinx.coroutines.CoroutineScope  indexOfFirst !kotlinx.coroutines.CoroutineScope  
isNullOrBlank !kotlinx.coroutines.CoroutineScope  isRecording !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadRecordings !kotlinx.coroutines.CoroutineScope  mediaPlayer !kotlinx.coroutines.CoroutineScope  mediaProjection !kotlinx.coroutines.CoroutineScope  nextId !kotlinx.coroutines.CoroutineScope  recordingStartTime !kotlinx.coroutines.CoroutineScope  
recordings !kotlinx.coroutines.CoroutineScope  	removeAll !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  stopCurrentPlayback !kotlinx.coroutines.CoroutineScope  
stopRecording !kotlinx.coroutines.CoroutineScope  toMediaType !kotlinx.coroutines.CoroutineScope  transcribeAudio !kotlinx.coroutines.CoroutineScope  	writeText !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  plus *kotlinx.coroutines.MainCoroutineDispatcher  	Exception kotlinx.coroutines.flow  File kotlinx.coroutines.flow  FileManager kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  MediaPlayer kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  
_errorMessage kotlinx.coroutines.flow  
_isLoading kotlinx.coroutines.flow  _recordings kotlinx.coroutines.flow  apply kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  fileManager kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  let kotlinx.coroutines.flow  loadRecordings kotlinx.coroutines.flow  mediaPlayer kotlinx.coroutines.flow  
repository kotlinx.coroutines.flow  stopCurrentPlayback kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  getCollectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  BASE_URL okhttp3  CostEstimate okhttp3  Dispatchers okhttp3  	Exception okhttp3  File okhttp3  GsonConverterFactory okhttp3  Log okhttp3  
MAX_FILE_SIZE okhttp3  	MediaType okhttp3  	Multipart okhttp3  
MultipartBody okhttp3  OkHttpClient okhttp3  OpenAIApiService okhttp3  POST okhttp3  Part okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  Retrofit okhttp3  String okhttp3  TAG okhttp3  TIMEOUT_SECONDS okhttp3  TimeUnit okhttp3  TranscriptionQueueStatus okhttp3  TranscriptionResult okhttp3  apiKey okhttp3  
apiService okhttp3  
asRequestBody okhttp3  com okhttp3  contains okhttp3  context okhttp3  	extension okhttp3  format okhttp3  
isNotEmpty okhttp3  
isNullOrBlank okhttp3  java okhttp3  let okhttp3  listOf okhttp3  	lowercase okhttp3  
mutableListOf okhttp3  toMediaType okhttp3  transcribeAudio okhttp3  withContext okhttp3  	writeText okhttp3  <SAM-CONSTRUCTOR> okhttp3.Interceptor  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  	Companion okhttp3.MediaType  toMediaType okhttp3.MediaType.Companion  Part okhttp3.MultipartBody  createFormData okhttp3.MultipartBody.Part  createFormData $okhttp3.MultipartBody.Part.Companion  Builder okhttp3.OkHttpClient  -addInterceptor okhttp3.OkHttpClient.Builder  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  
newBuilder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  	Companion okhttp3.RequestBody  create okhttp3.RequestBody  
asRequestBody okhttp3.RequestBody.Companion  create okhttp3.RequestBody.Companion  string okhttp3.ResponseBody  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  	errorBody retrofit2.Response  getISSuccessful retrofit2.Response  getIsSuccessful retrofit2.Response  isSuccessful retrofit2.Response  
setSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  BASE_URL retrofit2.http  CostEstimate retrofit2.http  Dispatchers retrofit2.http  	Exception retrofit2.http  File retrofit2.http  GsonConverterFactory retrofit2.http  Log retrofit2.http  
MAX_FILE_SIZE retrofit2.http  	Multipart retrofit2.http  
MultipartBody retrofit2.http  OkHttpClient retrofit2.http  OpenAIApiService retrofit2.http  POST retrofit2.http  Part retrofit2.http  RequestBody retrofit2.http  Retrofit retrofit2.http  String retrofit2.http  TAG retrofit2.http  TIMEOUT_SECONDS retrofit2.http  TimeUnit retrofit2.http  TranscriptionQueueStatus retrofit2.http  TranscriptionResult retrofit2.http  apiKey retrofit2.http  
apiService retrofit2.http  
asRequestBody retrofit2.http  com retrofit2.http  contains retrofit2.http  context retrofit2.http  	extension retrofit2.http  format retrofit2.http  
isNotEmpty retrofit2.http  
isNullOrBlank retrofit2.http  java retrofit2.http  let retrofit2.http  listOf retrofit2.http  	lowercase retrofit2.http  
mutableListOf retrofit2.http  toMediaType retrofit2.http  transcribeAudio retrofit2.http  withContext retrofit2.http  	writeText retrofit2.http  Log android.app  CallDetectionService android.app.Activity  CallRecordingService android.app.Activity  CallType android.app.Activity  	Exception android.app.Activity  Intent android.app.Activity  Log android.app.Activity  String android.app.Activity  android android.app.Activity  apply android.app.Activity  initializeCallDetection android.app.Activity  invoke android.app.Activity  java android.app.Activity  	onDestroy android.app.Activity  startCallRecording android.app.Activity  startForegroundService android.app.Activity  startService android.app.Activity  stopCallRecording android.app.Activity  Log android.app.Service  CallDetectionService android.content.Context  Log android.content.Context  android android.content.Context  initializeCallDetection android.content.Context  invoke android.content.Context  startCallRecording android.content.Context  stopCallRecording android.content.Context  CallDetectionService android.content.ContextWrapper  Log android.content.ContextWrapper  android android.content.ContextWrapper  initializeCallDetection android.content.ContextWrapper  invoke android.content.ContextWrapper  startCallRecording android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startService android.content.ContextWrapper  stopCallRecording android.content.ContextWrapper  CallDetectionService  android.view.ContextThemeWrapper  CallRecordingService  android.view.ContextThemeWrapper  CallType  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  initializeCallDetection  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  startCallRecording  android.view.ContextThemeWrapper  startForegroundService  android.view.ContextThemeWrapper  startService  android.view.ContextThemeWrapper  stopCallRecording  android.view.ContextThemeWrapper  CallDetectionService #androidx.activity.ComponentActivity  CallRecordingService #androidx.activity.ComponentActivity  CallType #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  initializeCallDetection #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  startCallRecording #androidx.activity.ComponentActivity  startForegroundService #androidx.activity.ComponentActivity  startService #androidx.activity.ComponentActivity  stopCallRecording #androidx.activity.ComponentActivity  CallDetectionService -androidx.activity.ComponentActivity.Companion  CallRecordingService -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  
getANDROID -androidx.activity.ComponentActivity.Companion  getAPPLY -androidx.activity.ComponentActivity.Companion  
getAndroid -androidx.activity.ComponentActivity.Companion  getApply -androidx.activity.ComponentActivity.Companion  invoke -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  CallDetectionService #androidx.core.app.ComponentActivity  CallRecordingService #androidx.core.app.ComponentActivity  CallType #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  initializeCallDetection #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  startCallRecording #androidx.core.app.ComponentActivity  startForegroundService #androidx.core.app.ComponentActivity  startService #androidx.core.app.ComponentActivity  stopCallRecording #androidx.core.app.ComponentActivity  CallDetectionService com.example.callrecorder  CallRecordingService com.example.callrecorder  	Exception com.example.callrecorder  Intent com.example.callrecorder  Log com.example.callrecorder  String com.example.callrecorder  android com.example.callrecorder  apply com.example.callrecorder  java com.example.callrecorder  CallDetectionService %com.example.callrecorder.MainActivity  CallRecordingService %com.example.callrecorder.MainActivity  CallType %com.example.callrecorder.MainActivity  	Exception %com.example.callrecorder.MainActivity  Intent %com.example.callrecorder.MainActivity  Log %com.example.callrecorder.MainActivity  String %com.example.callrecorder.MainActivity  android %com.example.callrecorder.MainActivity  apply %com.example.callrecorder.MainActivity  callDetectionService %com.example.callrecorder.MainActivity  
getANDROID %com.example.callrecorder.MainActivity  getAPPLY %com.example.callrecorder.MainActivity  
getAndroid %com.example.callrecorder.MainActivity  getApply %com.example.callrecorder.MainActivity  initializeCallDetection %com.example.callrecorder.MainActivity  invoke %com.example.callrecorder.MainActivity  java %com.example.callrecorder.MainActivity  startCallRecording %com.example.callrecorder.MainActivity  startForegroundService %com.example.callrecorder.MainActivity  startService %com.example.callrecorder.MainActivity  stopCallRecording %com.example.callrecorder.MainActivity  cleanup 5com.example.callrecorder.service.CallDetectionService  
initialize 5com.example.callrecorder.service.CallDetectionService  startListening 5com.example.callrecorder.service.CallDetectionService  Log 5com.example.callrecorder.service.CallRecordingService  Log ?com.example.callrecorder.service.CallRecordingService.Companion  CallDetectionService 	java.lang  Log 	java.util  CallDetectionService kotlin  CallDetectionService kotlin.annotation  CallDetectionService kotlin.collections  CallDetectionService kotlin.comparisons  CallDetectionService 	kotlin.io  CallDetectionService 
kotlin.jvm  CallDetectionService 
kotlin.ranges  CallDetectionService kotlin.sequences  CallDetectionService kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            