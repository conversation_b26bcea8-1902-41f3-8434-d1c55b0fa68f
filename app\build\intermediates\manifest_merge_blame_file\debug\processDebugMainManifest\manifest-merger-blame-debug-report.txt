1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.callrecorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Core permissions for call recording -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_CALL_LOG" />
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:5-72
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:22-69
15
16    <!-- Service permissions -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:5-88
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:22-85
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:5-94
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:22-91
20    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:5-85
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:22-82
21
22    <!-- Storage permissions -->
23    <uses-permission
23-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:5-108
24        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
24-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:22-78
25        android:maxSdkVersion="28" />
25-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:79-105
26    <uses-permission
26-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:5-107
27        android:name="android.permission.READ_EXTERNAL_STORAGE"
27-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:22-77
28        android:maxSdkVersion="32" />
28-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:78-104
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:5-111
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:22-79
30
31    <!-- Notification permission for Android 13+ -->
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:5-77
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:22-74
33
34    <!-- Network for OpenAI API -->
35    <uses-permission android:name="android.permission.INTERNET" />
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:5-67
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:22-64
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:5-79
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:22-76
37
38    <!-- Contact access for caller name -->
39    <uses-permission android:name="android.permission.READ_CONTACTS" />
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:5-72
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:22-69
40
41    <!-- Wake lock for recording service -->
42    <uses-permission android:name="android.permission.WAKE_LOCK" />
42-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:5-68
42-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:22-65
43
44    <!-- System alert window for overlay (if needed) -->
45    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:5-78
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:22-75
46
47    <permission
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
52
53    <application
53-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:5-79:19
54        android:allowBackup="true"
54-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:38:9-35
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:icon="@mipmap/ic_launcher"
58-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:39:9-43
59        android:label="@string/app_name"
59-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:40:9-41
60        android:roundIcon="@mipmap/ic_launcher_round"
60-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:41:9-54
61        android:supportsRtl="true"
61-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:42:9-35
62        android:testOnly="true"
63        android:theme="@style/Theme.CallRecorder" >
63-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:43:9-50
64        <activity
64-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:44:9-51:20
65            android:name="com.example.callrecorder.MainActivity"
65-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:45:13-41
66            android:exported="true" >
66-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:46:13-36
67            <intent-filter>
67-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:47:13-50:29
68                <action android:name="android.intent.action.MAIN" />
68-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:17-69
68-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:17-77
70-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:27-74
71            </intent-filter>
72        </activity>
73
74        <service
74-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:52:9-56:74
75            android:name="com.example.callrecorder.service.CallRecordingService"
75-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:53:13-57
76            android:enabled="true"
76-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:54:13-35
77            android:exported="false"
77-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:55:13-37
78            android:foregroundServiceType="microphone|mediaProjection" />
78-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:56:13-71
79        <service
79-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:57:9-67:19
80            android:name="com.example.callrecorder.service.CallAccessibilityService"
80-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:58:13-61
81            android:exported="false"
81-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:60:13-37
82            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
82-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:59:13-79
83            <intent-filter>
83-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:61:13-63:29
84                <action android:name="android.accessibilityservice.AccessibilityService" />
84-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:17-92
84-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:25-89
85            </intent-filter>
86
87            <meta-data
87-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:64:13-66:72
88                android:name="android.accessibilityservice"
88-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:65:17-60
89                android:resource="@xml/accessibility_service_config" />
89-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:66:17-69
90        </service>
91
92        <!-- FileProvider for sharing files on Android 7.0+ -->
93        <provider
94            android:name="androidx.core.content.FileProvider"
94-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:71:13-62
95            android:authorities="com.example.callrecorder.fileprovider"
95-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:72:13-64
96            android:exported="false"
96-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:73:13-37
97            android:grantUriPermissions="true" >
97-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:74:13-47
98            <meta-data
98-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:75:13-77:54
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:76:17-67
100                android:resource="@xml/file_paths" />
100-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:77:17-51
101        </provider>
102
103        <activity
103-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
104            android:name="androidx.compose.ui.tooling.PreviewActivity"
104-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
105            android:exported="true" />
105-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
106
107        <provider
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
108            android:name="androidx.startup.InitializationProvider"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
109            android:authorities="com.example.callrecorder.androidx-startup"
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
110            android:exported="false" >
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
111            <meta-data
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.emoji2.text.EmojiCompatInitializer"
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
113                android:value="androidx.startup" />
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
116                android:value="androidx.startup" />
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
117            <meta-data
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
119                android:value="androidx.startup" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
120        </provider>
121
122        <activity
122-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
123            android:name="androidx.activity.ComponentActivity"
123-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
124            android:exported="true" />
124-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
125
126        <service
126-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
127            android:name="androidx.room.MultiInstanceInvalidationService"
127-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
128            android:directBootAware="true"
128-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
129            android:exported="false" />
129-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
130
131        <receiver
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
132            android:name="androidx.profileinstaller.ProfileInstallReceiver"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
133            android:directBootAware="false"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
134            android:enabled="true"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
135            android:exported="true"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
136            android:permission="android.permission.DUMP" >
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
138                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
141                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
144                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
147                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
148            </intent-filter>
149        </receiver>
150    </application>
151
152</manifest>
