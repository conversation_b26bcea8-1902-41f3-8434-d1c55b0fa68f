1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.callrecorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.RECORD_AUDIO" />
11-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:5:5-71
11-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:5:22-68
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:5-75
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:22-72
13    <uses-permission android:name="android.permission.READ_CALL_LOG" />
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:5-77
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission
15-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:9:5-108
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:9:79-105
18    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:10:5-85
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:10:22-82
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:5-77
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:22-74
20    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:5-111
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:22-79
21
22    <permission
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:5-45:19
29        android:allowBackup="true"
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:15:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:16:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.CallRecorder" >
38-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:20:9-50
39        <activity
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:21:9-28:20
40            android:name="com.example.callrecorder.MainActivity"
40-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:13-41
41            android:exported="true" >
41-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:23:13-36
42            <intent-filter>
42-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:24:13-27:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:17-69
43-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:17-77
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:27-74
46            </intent-filter>
47        </activity>
48
49        <service
49-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:9-33:74
50            android:name="com.example.callrecorder.service.CallRecordingService"
50-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:30:13-57
51            android:enabled="true"
51-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:31:13-35
52            android:exported="false"
52-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:13-37
53            android:foregroundServiceType="microphone|mediaProjection" />
53-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:33:13-71
54        <service
54-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:34:9-44:19
55            android:name="com.example.callrecorder.service.CallAccessibilityService"
55-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:13-61
56            android:exported="false"
56-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:13-37
57            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
57-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:36:13-79
58            <intent-filter>
58-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:38:13-40:29
59                <action android:name="android.accessibilityservice.AccessibilityService" />
59-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:39:17-92
59-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:39:25-89
60            </intent-filter>
61
62            <meta-data
62-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:41:13-43:72
63                android:name="android.accessibilityservice"
63-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:42:17-60
64                android:resource="@xml/accessibility_service_config" />
64-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:43:17-69
65        </service>
66
67        <provider
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.example.callrecorder.androidx-startup"
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
