1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.callrecorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Core permissions for call recording -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_CALL_LOG" />
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:5-72
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:22-69
15
16    <!-- Service permissions -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:5-88
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:22-85
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:5-94
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:22-91
20    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:5-85
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:22-82
21
22    <!-- Storage permissions -->
23    <uses-permission
23-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:5-108
24        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
24-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:22-78
25        android:maxSdkVersion="28" />
25-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:79-105
26    <uses-permission
26-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:5-107
27        android:name="android.permission.READ_EXTERNAL_STORAGE"
27-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:22-77
28        android:maxSdkVersion="32" />
28-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:78-104
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:5-111
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:22-79
30
31    <!-- Notification permission for Android 13+ -->
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:5-77
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:22-74
33
34    <!-- Network for OpenAI API -->
35    <uses-permission android:name="android.permission.INTERNET" />
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:5-67
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:22-64
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:5-79
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:22-76
37
38    <!-- VoIP and WhatsApp call recording permissions -->
39    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:5-80
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:22-77
40    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
40-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:30:5-31:47
40-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:30:22-76
41
42    <!-- Contact access for caller name -->
43    <uses-permission android:name="android.permission.READ_CONTACTS" />
43-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:34:5-72
43-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:34:22-69
44
45    <!-- Wake lock for recording service -->
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:5-68
46-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:22-65
47
48    <!-- System alert window for overlay (if needed) -->
49    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
49-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:40:5-78
49-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:40:22-75
50
51    <permission
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
52        android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
56
57    <application
57-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:42:5-84:19
58        android:allowBackup="true"
58-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:43:9-35
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
60        android:debuggable="true"
61        android:extractNativeLibs="false"
62        android:icon="@mipmap/ic_launcher"
62-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:44:9-43
63        android:label="@string/app_name"
63-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:45:9-41
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:46:9-54
65        android:supportsRtl="true"
65-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:47:9-35
66        android:testOnly="true"
67        android:theme="@style/Theme.CallRecorder" >
67-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:9-50
68        <activity
68-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:9-56:20
69            android:name="com.example.callrecorder.MainActivity"
69-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:50:13-41
70            android:exported="true" >
70-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:51:13-36
71            <intent-filter>
71-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:52:13-55:29
72                <action android:name="android.intent.action.MAIN" />
72-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:53:17-69
72-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:53:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:54:17-77
74-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:54:27-74
75            </intent-filter>
76        </activity>
77
78        <service
78-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:57:9-61:74
79            android:name="com.example.callrecorder.service.CallRecordingService"
79-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:58:13-57
80            android:enabled="true"
80-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:59:13-35
81            android:exported="false"
81-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:60:13-37
82            android:foregroundServiceType="microphone|mediaProjection" />
82-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:61:13-71
83        <service
83-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:9-72:19
84            android:name="com.example.callrecorder.service.CallAccessibilityService"
84-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:63:13-61
85            android:exported="false"
85-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:65:13-37
86            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
86-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:64:13-79
87            <intent-filter>
87-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:66:13-68:29
88                <action android:name="android.accessibilityservice.AccessibilityService" />
88-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:67:17-92
88-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:67:25-89
89            </intent-filter>
90
91            <meta-data
91-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:69:13-71:72
92                android:name="android.accessibilityservice"
92-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:70:17-60
93                android:resource="@xml/accessibility_service_config" />
93-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:71:17-69
94        </service>
95
96        <!-- FileProvider for sharing files on Android 7.0+ -->
97        <provider
98            android:name="androidx.core.content.FileProvider"
98-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:76:13-62
99            android:authorities="com.example.callrecorder.fileprovider"
99-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:77:13-64
100            android:exported="false"
100-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:78:13-37
101            android:grantUriPermissions="true" >
101-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:79:13-47
102            <meta-data
102-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:80:13-82:54
103                android:name="android.support.FILE_PROVIDER_PATHS"
103-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:81:17-67
104                android:resource="@xml/file_paths" />
104-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:82:17-51
105        </provider>
106
107        <activity
107-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
108            android:name="androidx.compose.ui.tooling.PreviewActivity"
108-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
109            android:exported="true" />
109-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
110
111        <provider
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
112            android:name="androidx.startup.InitializationProvider"
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
113            android:authorities="com.example.callrecorder.androidx-startup"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
114            android:exported="false" >
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.emoji2.text.EmojiCompatInitializer"
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
117                android:value="androidx.startup" />
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <activity
126-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
127            android:name="androidx.activity.ComponentActivity"
127-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
128            android:exported="true" />
128-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
129
130        <service
130-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
131            android:name="androidx.room.MultiInstanceInvalidationService"
131-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
132            android:directBootAware="true"
132-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
133            android:exported="false" />
133-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
134
135        <receiver
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
136            android:name="androidx.profileinstaller.ProfileInstallReceiver"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
137            android:directBootAware="false"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
138            android:enabled="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
139            android:exported="true"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
140            android:permission="android.permission.DUMP" >
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
142                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
145                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
146            </intent-filter>
147            <intent-filter>
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
148                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
149            </intent-filter>
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
151                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
152            </intent-filter>
153        </receiver>
154    </application>
155
156</manifest>
