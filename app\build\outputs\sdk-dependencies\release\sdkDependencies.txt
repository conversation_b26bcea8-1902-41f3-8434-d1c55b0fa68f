# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "\324\253\314\350#V\203\2407M\324\351\247\217\324\026\353e\205VX\335\251\324\376K4\345\242\177\361g"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\to\311M\340\037\'\274x;\260\270\230KI\256\332h\217c)\3709=B\324\266uK\352\347\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\363\273bk\255\301~\362@[\243\212\330\350\226\326g\314&\002\202,\344n\221m\310]k/9\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.6.7"
  }
  digests {
    sha256: "]mN:A\023\263\327\201\371$\307\333\332\250\366Kj\202\355\347\316M\030H:\220w\245\302\2075"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: ")\a\230\270\214\342L\211wG\361\\\322Q\002\000Z\224\032,\364\323^\374\270\316/\026,!\365\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.0"
  }
  digests {
    sha256: "F\374\216\204-\232N\003\r\375\236\020\214;\300\203\020\371\"\275t!\362\237g\334\252\244\255\2547d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "v@;\261Y\237n*\334\233\2007.\373&t\270\313\355\342\260\361\346\350\\J\224\341\f{\224\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.9.0"
  }
  digests {
    sha256: "\372\261\306\362\026\304@\203\335\267\r\220\342H\3534PO\204kPZ\243\307\25022[ky\r\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.6.7"
  }
  digests {
    sha256: "KS\017\264V/\271\227\374\314k\2430r\300(\362\301n\333\034\352s<\346S\370\257\351\374\\\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.6.7"
  }
  digests {
    sha256: "q\272h\263gj\021\353\205t\257\267\370z\255\031\223?\r|<T1\221\214\276\350P\356\366\336y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\267\3628\001\261\305=\342\250m+\373\370H\351\021\332\237\177\325@\354]\023\'\214\327\366\270\305@V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\205\212\355\303D)\353\351X<7n\345S5\240\v\266\341\266\364\v\236L)\230\217\320s\361T\033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\247\343DW\0037\335\\\302}\263\016\256\312\031\005\247\254\031\262PW\244\\9\v\303\a\373$\274\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.6.7"
  }
  digests {
    sha256: "x\340\231\213\270\363\224\341v\223\337@\027\353\317\376h\262\303\233\212T\023\336f\2219v\250\235F\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.6.7"
  }
  digests {
    sha256: ";l\322v\021\342\373\320\227n\303\211ey\324^W\v&\226l\311\211\216\320\355\003\206P\207\277<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.6.7"
  }
  digests {
    sha256: "Vy\275[\226?\232)\006\237c\035\267\206\204\\yE\274y\2634+G\347\361>\227\023\374\336g"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\b\vu4\n\257\3046/*\373\232\223p\\\366d\262Srd\276r\353\366?\277\250\"\221c\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\372\362\006K\006\002\327\216f\227c\037[\004(\253\352%\027\212\3347\204\204\240v\005\330\312M6\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.11.0"
  }
  digests {
    sha256: "\216\257\1779\236\340\224\334\023I\\6\347\324\357G\036\247\357\2129\236My\311kP\375O\202E\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.1"
  }
  digests {
    sha256: "\234S\322L\345^x6\264NZ\205\263\366\372\000o\337\207B\324\241\250\r\215C-\356\246a\b\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\300\252B\036\023\333\217\002\277\352\317C\276ACd\361\204\236\357\372\364 \304\232\254G\223|9\tv"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.6.7"
  }
  digests {
    sha256: "kJ\r\216\023@\202\230w\f\370\3751\263<JZ\366h\344TQB\302\247\215\377U\241\306\177\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.6.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.6.7"
  }
  digests {
    sha256: "\215\356\005\326\033\327\253\343\222\346\363\324\177\006\332\222.\277\267\357\351\375\224\020t\314a}78\201\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.2.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.2.1"
  }
  digests {
    sha256: "\323D\340\3270`\315\344\273\300\355d\024i\206\312\037\256\016\016\311S\255\226\230q>\335+\321\026\000"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.7"
  }
  digests {
    sha256: "\365\005\256\351\212\372$\372\253\243n\n\242\370\313\351\267\245\200\b\272*\003(j\vz\036h\356\'\177"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.7"
  }
  digests {
    sha256: "\255@\304\255\256\204\200v\376\364\033tt\231\241\005\017ERS\234\017B\267Ge\344:w\336\256\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.7"
  }
  digests {
    sha256: "\205\233\374\222\3316\373\257\207\027?W\265\271\030\305R\372\335\\Z\223\036\376\026\327\216\232\330e\266\267"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.7"
  }
  digests {
    sha256: "9\331\275;\370\201\205\272\327\212\256\320\035\210\223\363u\334\237\016i\236P\227\377dA\242\335\212\257)"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.7"
  }
  digests {
    sha256: "\v^\aR\207 :6\364j\373L\355\243\226Gh\242x\311\263gh\344\270\313\262\224\357\001\324\364"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-compose"
    version: "1.1.0-alpha13"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-compose-android"
    version: "1.1.0-alpha13"
  }
  digests {
    sha256: "\201Yme\327\232\004/\b\322\263/\274\021q\204\324dk\212\223N\177\222\236n\343:\320V\ac"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.0-alpha13"
  }
  digests {
    sha256: "\201\277\243\370\302hC!\372\025\'p{\026\301\263eSv\351\261\322,\263NDv\373\334\377|\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 74
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 2
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 24
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 25
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 26
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 27
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 28
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 26
  library_dep_index: 19
  library_dep_index: 45
}
library_dependencies {
  library_index: 29
  library_dep_index: 9
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 26
  library_dep_index: 19
  library_dep_index: 45
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 30
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
  library_dep_index: 6
  library_dep_index: 50
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 65
  library_dep_index: 61
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 48
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 66
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 49
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 48
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 49
  library_dep_index: 36
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 24
  library_dep_index: 42
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 19
  library_dep_index: 45
}
library_dependencies {
  library_index: 40
  library_dep_index: 9
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 24
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 19
  library_dep_index: 45
}
library_dependencies {
  library_index: 41
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 26
  library_dep_index: 19
  library_dep_index: 45
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 45
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 6
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 46
  library_dep_index: 14
}
library_dependencies {
  library_index: 49
  library_dep_index: 36
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 50
  library_dep_index: 8
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 6
  library_dep_index: 30
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 34
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 0
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 30
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 53
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 30
  library_dep_index: 4
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 57
  library_dep_index: 53
}
library_dependencies {
  library_index: 65
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 72
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 72
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 53
  library_dep_index: 4
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 68
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 66
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 75
  library_dep_index: 37
  library_dep_index: 6
  library_dep_index: 76
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 82
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 85
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 75
}
library_dependencies {
  library_index: 77
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 78
  library_dep_index: 77
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 79
  library_dep_index: 6
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 81
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 37
  library_dep_index: 26
  library_dep_index: 38
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 9
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 81
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 38
}
library_dependencies {
  library_index: 85
  library_dep_index: 6
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 37
  library_dep_index: 6
  library_dep_index: 75
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 8
  library_dep_index: 80
  library_dep_index: 93
  library_dep_index: 9
  library_dep_index: 82
  library_dep_index: 16
  library_dep_index: 98
  library_dep_index: 85
  library_dep_index: 99
  library_dep_index: 77
  library_dep_index: 100
}
library_dependencies {
  library_index: 87
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 10
}
library_dependencies {
  library_index: 91
  library_dep_index: 75
  library_dep_index: 8
  library_dep_index: 92
}
library_dependencies {
  library_index: 93
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 95
  library_dep_index: 84
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 95
  library_dep_index: 6
}
library_dependencies {
  library_index: 96
  library_dep_index: 6
}
library_dependencies {
  library_index: 97
  library_dep_index: 6
}
library_dependencies {
  library_index: 98
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 10
}
library_dependencies {
  library_index: 99
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 100
  library_dep_index: 6
  library_dep_index: 82
  library_dep_index: 98
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 6
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 103
  library_dep_index: 105
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 4
  library_dep_index: 103
  library_dep_index: 105
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
}
library_dependencies {
  library_index: 104
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 101
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 30
  library_dep_index: 53
  library_dep_index: 101
  library_dep_index: 103
}
library_dependencies {
  library_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 108
  library_dep_index: 49
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 103
  library_dep_index: 105
  library_dep_index: 30
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 53
  library_dep_index: 24
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 44
  library_dep_index: 4
}
library_dependencies {
  library_index: 109
  library_dep_index: 49
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 110
  library_dep_index: 0
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 113
  library_dep_index: 112
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
  library_dep_index: 113
  library_dep_index: 111
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 112
}
library_dependencies {
  library_index: 111
  library_dep_index: 112
  library_dep_index: 112
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 110
}
library_dependencies {
  library_index: 112
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 48
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 111
  library_dep_index: 109
  library_dep_index: 113
  library_dep_index: 110
}
library_dependencies {
  library_index: 113
  library_dep_index: 36
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 112
  library_dep_index: 0
  library_dep_index: 112
  library_dep_index: 111
  library_dep_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
}
library_dependencies {
  library_index: 115
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 34
  library_dep_index: 57
  library_dep_index: 53
  library_dep_index: 116
  library_dep_index: 5
}
library_dependencies {
  library_index: 116
  library_dep_index: 6
}
library_dependencies {
  library_index: 117
  library_dep_index: 9
  library_dep_index: 18
  library_dep_index: 118
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 118
  library_dep_index: 6
  library_dep_index: 2
  library_dep_index: 119
  library_dep_index: 117
}
library_dependencies {
  library_index: 119
  library_dep_index: 118
  library_dep_index: 117
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 118
  library_dep_index: 117
}
library_dependencies {
  library_index: 120
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 6
  library_dep_index: 120
  library_dep_index: 0
  library_dep_index: 120
}
library_dependencies {
  library_index: 122
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 8
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
}
library_dependencies {
  library_index: 124
  library_dep_index: 125
  library_dep_index: 2
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 127
  library_dep_index: 123
  library_dep_index: 128
}
library_dependencies {
  library_index: 129
  library_dep_index: 124
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 75
  dependency_index: 86
  dependency_index: 49
  dependency_index: 34
  dependency_index: 101
  dependency_index: 63
  dependency_index: 107
  dependency_index: 109
  dependency_index: 114
  dependency_index: 39
  dependency_index: 40
  dependency_index: 28
  dependency_index: 41
  dependency_index: 29
  dependency_index: 36
  dependency_index: 117
  dependency_index: 119
  dependency_index: 20
  dependency_index: 122
  dependency_index: 123
  dependency_index: 127
  dependency_index: 129
  dependency_index: 128
  dependency_index: 95
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
