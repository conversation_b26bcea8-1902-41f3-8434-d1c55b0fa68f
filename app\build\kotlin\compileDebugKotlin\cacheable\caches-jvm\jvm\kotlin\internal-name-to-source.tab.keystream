%com/example/callrecorder/MainActivity<com/example/callrecorder/ComposableSingletons$MainActivityKtGcom/example/callrecorder/ComposableSingletons$MainActivityKt$lambda-1$15com/example/callrecorder/data/CallRecordingRepository=com/example/callrecorder/data/InMemoryCallRecordingRepositoryPcom/example/callrecorder/data/InMemoryCallRecordingRepository$getAllRecordings$2Pcom/example/callrecorder/data/InMemoryCallRecordingRepository$getRecordingById$2Lcom/example/callrecorder/data/InMemoryCallRecordingRepository$addRecording$2Ocom/example/callrecorder/data/InMemoryCallRecordingRepository$deleteRecording$2Qcom/example/callrecorder/data/InMemoryCallRecordingRepository$deleteRecording$2$1Scom/example/callrecorder/data/InMemoryCallRecordingRepository$updateTranscription$2-com/example/callrecorder/domain/CallRecording9com/example/callrecorder/service/CallAccessibilityService5com/example/callrecorder/service/CallRecordingService6com/example/callrecorder/service/MediaProjectionHelperBcom/example/callrecorder/ui/ComposableSingletons$CallRecorderAppKtMcom/example/callrecorder/ui/ComposableSingletons$CallRecorderAppKt$lambda-1$1Mcom/example/callrecorder/ui/ComposableSingletons$CallRecorderAppKt$lambda-2$1-com/example/callrecorder/ui/CallRecorderAppKt?com/example/callrecorder/ui/CallRecorderAppKt$CallRecorderApp$1Fcom/example/callrecorder/ui/CallRecorderAppKt$PreviewCallRecorderApp$1Kcom/example/callrecorder/ui/ComposableSingletons$CallRecordingsListScreenKtVcom/example/callrecorder/ui/ComposableSingletons$CallRecordingsListScreenKt$lambda-1$1Vcom/example/callrecorder/ui/ComposableSingletons$CallRecordingsListScreenKt$lambda-2$16com/example/callrecorder/ui/CallRecordingsListScreenKtScom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1Wcom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1$1$1scom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1$invoke$$inlined$items$default$1scom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1$invoke$$inlined$items$default$2scom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1$invoke$$inlined$items$default$3scom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$1$1$invoke$$inlined$items$default$4Qcom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingsListScreen$2Ncom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingItem$1$1$1Jcom/example/callrecorder/ui/CallRecordingsListScreenKt$CallRecordingItem$2(com/example/callrecorder/ui/MainScreenKt?com/example/callrecorder/ui/MainScreenKt$MainScreen$viewModel$15com/example/callrecorder/ui/MainScreenKt$MainScreen$15com/example/callrecorder/ui/MainScreenKt$MainScreen$25com/example/callrecorder/ui/MainScreenKt$MainScreen$3Mcom/example/callrecorder/ui/ComposableSingletons$PermissionAndConsentScreenKtXcom/example/callrecorder/ui/ComposableSingletons$PermissionAndConsentScreenKt$lambda-1$1Xcom/example/callrecorder/ui/ComposableSingletons$PermissionAndConsentScreenKt$lambda-2$1Xcom/example/callrecorder/ui/ComposableSingletons$PermissionAndConsentScreenKt$lambda-3$1Xcom/example/callrecorder/ui/ComposableSingletons$PermissionAndConsentScreenKt$lambda-4$18com/example/callrecorder/ui/PermissionAndConsentScreenKt`com/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$launcher$1$1Ucom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$1Ucom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$2Wcom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$2$1Ucom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$3Wcom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$3$1Ucom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$4Ucom/example/callrecorder/ui/PermissionAndConsentScreenKt$PermissionAndConsentScreen$5.com/example/callrecorder/ui/PlaybackControlsKtCcom/example/callrecorder/ui/PlaybackControlsKt$PlaybackControls$1$1_com/example/callrecorder/ui/PlaybackControlsKt$PlaybackControls$1$1$invoke$$inlined$onDispose$1Ecom/example/callrecorder/ui/PlaybackControlsKt$PlaybackControls$2$1$1Ccom/example/callrecorder/ui/PlaybackControlsKt$PlaybackControls$2$2Acom/example/callrecorder/ui/PlaybackControlsKt$PlaybackControls$31com/example/callrecorder/ui/PrivacyPolicyScreenKtGcom/example/callrecorder/ui/PrivacyPolicyScreenKt$PrivacyPolicyScreen$1Gcom/example/callrecorder/ui/ComposableSingletons$StartRecordingButtonKtRcom/example/callrecorder/ui/ComposableSingletons$StartRecordingButtonKt$lambda-1$12com/example/callrecorder/ui/StartRecordingButtonKtRcom/example/callrecorder/ui/StartRecordingButtonKt$StartRecordingButton$launcher$1Icom/example/callrecorder/ui/StartRecordingButtonKt$StartRecordingButton$1Icom/example/callrecorder/ui/StartRecordingButtonKt$StartRecordingButton$2=com/example/callrecorder/ui/viewmodel/CallRecordingsViewModelNcom/example/callrecorder/ui/viewmodel/CallRecordingsViewModel$loadRecordings$1Ocom/example/callrecorder/ui/viewmodel/CallRecordingsViewModel$deleteRecording$15com/example/callrecorder/util/RecordingServiceStarter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    