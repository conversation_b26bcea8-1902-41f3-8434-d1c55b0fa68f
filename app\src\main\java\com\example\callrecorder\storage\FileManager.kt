package com.example.callrecorder.storage

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import androidx.core.content.FileProvider
import com.example.callrecorder.data.CallType
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * File manager for handling call recording storage with Scoped Storage compliance
 * 
 * Android Version Storage Behavior:
 * - API 24-28: Traditional external storage with permissions
 * - API 29 (Android 10): Scoped Storage introduced, app-specific directories preferred
 * - API 30+ (Android 11+): Scoped Storage enforced, MANAGE_EXTERNAL_STORAGE for full access
 * - API 33+ (Android 13): Granular media permissions
 */
class FileManager(private val context: Context) {
    
    companion object {
        private const val RECORDINGS_FOLDER = "recordings"
        private const val TRANSCRIPTS_FOLDER = "transcripts"
        private const val TEMP_FOLDER = "temp"
        
        // File extensions
        const val AUDIO_EXTENSION = ".m4a"
        const val TRANSCRIPT_EXTENSION = ".txt"
        
        // MIME types
        const val AUDIO_MIME_TYPE = "audio/mp4"
        const val TEXT_MIME_TYPE = "text/plain"
    }
    
    /**
     * Get the recordings directory (app-specific external storage)
     */
    fun getRecordingsDirectory(): File {
        val recordingsDir = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+: Use app-specific external storage (no permission required)
            File(context.getExternalFilesDir(null), RECORDINGS_FOLDER)
        } else {
            // Android 9 and below: Use traditional external storage
            File(Environment.getExternalStorageDirectory(), "CallRecorder/$RECORDINGS_FOLDER")
        }
        
        if (!recordingsDir.exists()) {
            recordingsDir.mkdirs()
        }
        
        return recordingsDir
    }
    
    /**
     * Get the transcripts directory
     */
    fun getTranscriptsDirectory(): File {
        val transcriptsDir = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            File(context.getExternalFilesDir(null), TRANSCRIPTS_FOLDER)
        } else {
            File(Environment.getExternalStorageDirectory(), "CallRecorder/$TRANSCRIPTS_FOLDER")
        }
        
        if (!transcriptsDir.exists()) {
            transcriptsDir.mkdirs()
        }
        
        return transcriptsDir
    }
    
    /**
     * Get the temporary files directory
     */
    fun getTempDirectory(): File {
        val tempDir = File(context.cacheDir, TEMP_FOLDER)
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }
        return tempDir
    }
    
    /**
     * Create a new recording file with proper naming convention
     */
    fun createRecordingFile(phoneNumber: String, callType: CallType): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val callTypePrefix = when (callType) {
            CallType.INCOMING -> "IN"
            CallType.OUTGOING -> "OUT"
            CallType.MISSED -> "MISSED"
        }
        
        // Sanitize phone number for filename
        val sanitizedNumber = phoneNumber.replace(Regex("[^\\d+]"), "")
        val fileName = "${callTypePrefix}_${sanitizedNumber}_$timestamp$AUDIO_EXTENSION"
        
        return File(getRecordingsDirectory(), fileName)
    }
    
    /**
     * Create a transcript file for a recording
     */
    fun createTranscriptFile(recordingFile: File): File {
        val baseName = recordingFile.nameWithoutExtension
        val fileName = "$baseName$TRANSCRIPT_EXTENSION"
        return File(getTranscriptsDirectory(), fileName)
    }
    
    /**
     * Get file size in human-readable format
     */
    fun getFormattedFileSize(file: File): String {
        val sizeBytes = file.length()
        val kb = sizeBytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$sizeBytes B"
        }
    }
    
    /**
     * Get total storage used by recordings
     */
    fun getTotalStorageUsed(): Long {
        var totalSize = 0L
        
        // Calculate recordings size
        val recordingsDir = getRecordingsDirectory()
        if (recordingsDir.exists()) {
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    totalSize += file.length()
                }
            }
        }
        
        // Calculate transcripts size
        val transcriptsDir = getTranscriptsDirectory()
        if (transcriptsDir.exists()) {
            transcriptsDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    totalSize += file.length()
                }
            }
        }
        
        return totalSize
    }
    
    /**
     * Delete a recording file and its associated transcript
     */
    fun deleteRecording(recordingFile: File): Boolean {
        var success = true
        
        try {
            // Delete the recording file
            if (recordingFile.exists()) {
                success = recordingFile.delete()
            }
            
            // Delete associated transcript if exists
            val transcriptFile = File(
                getTranscriptsDirectory(),
                recordingFile.nameWithoutExtension + TRANSCRIPT_EXTENSION
            )
            if (transcriptFile.exists()) {
                transcriptFile.delete()
            }
            
        } catch (e: Exception) {
            e.printStackTrace()
            success = false
        }
        
        return success
    }
    
    /**
     * Share a recording file
     */
    fun shareRecording(recordingFile: File): Intent? {
        return try {
            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Use FileProvider for Android 7.0+
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    recordingFile
                )
            } else {
                Uri.fromFile(recordingFile)
            }
            
            Intent(Intent.ACTION_SEND).apply {
                type = AUDIO_MIME_TYPE
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, "Call Recording")
                putExtra(Intent.EXTRA_TEXT, "Sharing call recording: ${recordingFile.name}")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Get URI for a file (for media player or sharing)
     */
    fun getFileUri(file: File): Uri? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
            } else {
                Uri.fromFile(file)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Check if external storage is available for writing
     */
    fun isExternalStorageWritable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }
    
    /**
     * Check if external storage is available for reading
     */
    fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return state == Environment.MEDIA_MOUNTED || state == Environment.MEDIA_MOUNTED_READ_ONLY
    }
    
    /**
     * Clean up old recordings (older than specified days)
     */
    fun cleanupOldRecordings(daysOld: Int): Int {
        val cutoffTime = System.currentTimeMillis() - (daysOld * 24 * 60 * 60 * 1000L)
        var deletedCount = 0
        
        try {
            val recordingsDir = getRecordingsDirectory()
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile && file.lastModified() < cutoffTime) {
                    if (deleteRecording(file)) {
                        deletedCount++
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return deletedCount
    }
    
    /**
     * Get available storage space
     */
    fun getAvailableStorageSpace(): Long {
        return try {
            val recordingsDir = getRecordingsDirectory()
            recordingsDir.freeSpace
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * Write transcript to file
     */
    fun writeTranscript(transcriptFile: File, transcript: String): Boolean {
        return try {
            transcriptFile.writeText(transcript)
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Read transcript from file
     */
    fun readTranscript(transcriptFile: File): String? {
        return try {
            if (transcriptFile.exists()) {
                transcriptFile.readText()
            } else {
                null
            }
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
}
