package com.example.callrecorder;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0005\u001a\u00020\u0006H\u0002J\u0012\u0010\u0007\u001a\u00020\u00062\b\u0010\b\u001a\u0004\u0018\u00010\tH\u0014J\b\u0010\n\u001a\u00020\u0006H\u0014J\"\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\rH\u0002J\b\u0010\u0011\u001a\u00020\u0006H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/callrecorder/MainActivity;", "Landroidx/activity/ComponentActivity;", "()V", "callDetectionService", "Lcom/example/callrecorder/service/CallDetectionService;", "initializeCallDetection", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "startCallRecording", "phoneNumber", "", "callType", "Lcom/example/callrecorder/data/CallType;", "contactName", "stopCallRecording", "app_debug"})
public final class MainActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.Nullable()
    private com.example.callrecorder.service.CallDetectionService callDetectionService;
    
    public MainActivity() {
        super(0);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeCallDetection() {
    }
    
    private final void startCallRecording(java.lang.String phoneNumber, com.example.callrecorder.data.CallType callType, java.lang.String contactName) {
    }
    
    private final void stopCallRecording() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
}