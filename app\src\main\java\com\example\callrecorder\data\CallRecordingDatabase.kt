package com.example.callrecorder.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters

/**
 * Room database for call recordings
 * 
 * Database version 1: Initial schema with CallRecording entity
 * Future versions should include migration strategies
 */
@Database(
    entities = [CallRecording::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class CallRecordingDatabase : RoomDatabase() {
    
    abstract fun callRecordingDao(): CallRecordingDao
    
    companion object {
        @Volatile
        private var INSTANCE: CallRecordingDatabase? = null
        
        fun getDatabase(context: Context): CallRecordingDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CallRecordingDatabase::class.java,
                    "call_recording_database"
                )
                .fallbackToDestructiveMigration() // For development only
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
