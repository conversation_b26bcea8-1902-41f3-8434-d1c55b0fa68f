package com.example.callrecorder.ai;

/**
 * Transcription manager for handling batch transcriptions and queue
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u0014H\u0082@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0002\u0010\u0018R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\rR\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/example/callrecorder/ai/TranscriptionManager;", "", "context", "Landroid/content/Context;", "openAIService", "Lcom/example/callrecorder/ai/OpenAIService;", "repository", "error/NonExistentClass", "fileManager", "Lcom/example/callrecorder/storage/FileManager;", "(Landroid/content/Context;Lcom/example/callrecorder/ai/OpenAIService;Lerror/NonExistentClass;Lcom/example/callrecorder/storage/FileManager;)V", "isProcessing", "", "Lerror/NonExistentClass;", "transcriptionQueue", "", "", "getQueueStatus", "Lcom/example/callrecorder/ai/TranscriptionQueueStatus;", "processQueue", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "queueForTranscription", "recordingId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "transcribeRecording", "app_release"})
public final class TranscriptionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.callrecorder.ai.OpenAIService openAIService = null;
    @org.jetbrains.annotations.NotNull()
    private final error.NonExistentClass repository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.callrecorder.storage.FileManager fileManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Long> transcriptionQueue = null;
    private boolean isProcessing = false;
    
    public TranscriptionManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.ai.OpenAIService openAIService, @org.jetbrains.annotations.NotNull()
    error.NonExistentClass repository, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.storage.FileManager fileManager) {
        super();
    }
    
    /**
     * Add recording to transcription queue
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queueForTranscription(long recordingId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Process transcription queue
     */
    private final java.lang.Object processQueue(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Transcribe a single recording
     */
    private final java.lang.Object transcribeRecording(long recordingId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Get queue status
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.callrecorder.ai.TranscriptionQueueStatus getQueueStatus() {
        return null;
    }
}