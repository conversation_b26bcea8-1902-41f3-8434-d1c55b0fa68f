package com.example.callrecorder.ui

import android.content.Context
import android.media.MediaPlayer
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.example.callrecorder.domain.CallRecording

@Composable
fun PlaybackControls(recording: CallRecording) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer: MediaPlayer? by remember { mutableStateOf(null) }

    DisposableEffect(recording.id) {
        onDispose {
            mediaPlayer?.release()
            mediaPlayer = null
        }
    }

    Row {
        Button(onClick = {
            if (!isPlaying) {
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(recording.filePath)
                    setOnCompletionListener {
                        isPlaying = false
                    }
                    prepare()
                    start()
                }
                isPlaying = true
            } else {
                mediaPlayer?.stop()
                isPlaying = false
            }
        }) {
            Text(if (isPlaying) "Stop" else "Play")
        }
        Spacer(modifier = Modifier.width(8.dp))
    }
}
