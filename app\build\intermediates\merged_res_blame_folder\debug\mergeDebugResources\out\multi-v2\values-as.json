{"logs": [{"outputFile": "com.example.callrecorder.app-mergeDebugResources-61:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\4732a0661345d7666aa559ef67f47092\\transformed\\jetified-foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "16949,17034", "endColumns": "84,87", "endOffsets": "17029,17117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\fba63af1cc2824d2492e5346a43ea817\\transformed\\appcompat-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,16427", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,16505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\ae1fe1024e90fb6ad9a5df9a70112b97\\transformed\\core-1.13.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,16589", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,16685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\a8ffa01484e5d0b2260db025a53ae0cf\\transformed\\jetified-ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "48,49,50,51,52,56,57,170,171,172,173,175,176,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4526,4621,4706,4799,4897,5203,5300,15859,15948,16038,16106,16259,16342,16510,16690,16765,16831", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "4616,4701,4794,4892,4979,5295,5394,15943,16033,16101,16173,16337,16422,16584,16760,16826,16944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d6004821a46f58cb297f64956cd160cd\\transformed\\material-1.11.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1048,1137,1202,1261,1347,1411,1475,1538,1608,1672,1726,1831,1889,1951,2005,2077,2194,2281,2364,2504,2581,2662,2789,2880,2957,3011,3062,3128,3198,3275,3362,3437,3508,3585,3654,3723,3830,3921,3993,4082,4171,4245,4317,4403,4453,4532,4598,4678,4762,4824,4888,4951,5020,5120,5215,5307,5399,5457,5512", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "267,349,427,504,590,674,776,899,978,1043,1132,1197,1256,1342,1406,1470,1533,1603,1667,1721,1826,1884,1946,2000,2072,2189,2276,2359,2499,2576,2657,2784,2875,2952,3006,3057,3123,3193,3270,3357,3432,3503,3580,3649,3718,3825,3916,3988,4077,4166,4240,4312,4398,4448,4527,4593,4673,4757,4819,4883,4946,5015,5115,5210,5302,5394,5452,5507,5588"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,4984,5049,5138,5399,11608,11694,11758,11822,11885,11955,12019,12073,12178,12236,12298,12352,12424,12541,12628,12711,12851,12928,13009,13136,13227,13304,13358,13409,13475,13545,13622,13709,13784,13855,13932,14001,14070,14177,14268,14340,14429,14518,14592,14664,14750,14800,14879,14945,15025,15109,15171,15235,15298,15367,15467,15562,15654,15746,15804,16178", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,174", "endColumns": "12,81,77,76,85,83,101,122,78,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,82,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,5044,5133,5198,5453,11689,11753,11817,11880,11950,12014,12068,12173,12231,12293,12347,12419,12536,12623,12706,12846,12923,13004,13131,13222,13299,13353,13404,13470,13540,13617,13704,13779,13850,13927,13996,14065,14172,14263,14335,14424,14513,14587,14659,14745,14795,14874,14940,15020,15104,15166,15230,15293,15362,15462,15557,15649,15741,15799,15854,16254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\f481f24d3159e23b2c7b2fc7d3454301\\transformed\\jetified-material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4638,4730,4839,4919,5004,5105,5210,5308,5410,5497,5604,5703,5807,5928,6008,6111", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4633,4725,4834,4914,4999,5100,5205,5303,5405,5492,5599,5698,5802,5923,6003,6106,6200"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5458,5577,5697,5810,5928,6024,6118,6229,6373,6494,6636,6721,6819,6914,7013,7129,7257,7360,7491,7621,7750,7930,8050,8168,8292,8425,8521,8617,8738,8864,8961,9071,9179,9315,9459,9569,9671,9748,9849,9950,10041,10133,10242,10322,10407,10508,10613,10711,10813,10900,11007,11106,11210,11331,11411,11514", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "5572,5692,5805,5923,6019,6113,6224,6368,6489,6631,6716,6814,6909,7008,7124,7252,7355,7486,7616,7745,7925,8045,8163,8287,8420,8516,8612,8733,8859,8956,9066,9174,9310,9454,9564,9666,9743,9844,9945,10036,10128,10237,10317,10402,10503,10608,10706,10808,10895,11002,11101,11205,11326,11406,11509,11603"}}]}]}