/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/example/callrecorder/MainActivity.kt; :app/src/main/java/com/example/callrecorder/MainActivity.kt; :app/src/main/java/com/example/callrecorder/MainActivity.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.kt? >app/src/main/java/com/example/callrecorder/ai/OpenAIService.ktG Fapp/src/main/java/com/example/callrecorder/audio/AudioPlayerManager.ktG Fapp/src/main/java/com/example/callrecorder/audio/AudioPlayerManager.ktG Fapp/src/main/java/com/example/callrecorder/audio/AudioPlayerManager.ktG Fapp/src/main/java/com/example/callrecorder/audio/AudioPlayerManager.ktA @app/src/main/java/com/example/callrecorder/data/CallRecording.ktA @app/src/main/java/com/example/callrecorder/data/CallRecording.ktA @app/src/main/java/com/example/callrecorder/data/CallRecording.ktD Capp/src/main/java/com/example/callrecorder/data/CallRecordingDao.ktI Happ/src/main/java/com/example/callrecorder/data/CallRecordingDatabase.ktI Happ/src/main/java/com/example/callrecorder/data/CallRecordingDatabase.ktK Japp/src/main/java/com/example/callrecorder/data/CallRecordingRepository.kt> =app/src/main/java/com/example/callrecorder/data/Converters.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktC Bapp/src/main/java/com/example/callrecorder/domain/CallRecording.ktK Japp/src/main/java/com/example/callrecorder/permission/PermissionManager.ktK Japp/src/main/java/com/example/callrecorder/permission/PermissionManager.ktO Napp/src/main/java/com/example/callrecorder/service/CallAccessibilityService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallDetectionService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktL Kapp/src/main/java/com/example/callrecorder/service/MediaProjectionHelper.ktB Aapp/src/main/java/com/example/callrecorder/storage/FileManager.ktB Aapp/src/main/java/com/example/callrecorder/storage/FileManager.ktB Aapp/src/main/java/com/example/callrecorder/storage/FileManager.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktE Dapp/src/main/java/com/example/callrecorder/ui/PrivacyPolicyScreen.ktE Dapp/src/main/java/com/example/callrecorder/ui/PrivacyPolicyScreen.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktG Fapp/src/main/java/com/example/callrecorder/ui/dialogs/ConsentDialog.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/screens/PermissionScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktP Oapp/src/main/java/com/example/callrecorder/ui/screens/RecordingControlScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktN Mapp/src/main/java/com/example/callrecorder/ui/screens/RecordingsListScreen.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.kt@ ?app/src/main/java/com/example/callrecorder/util/ErrorHandler.kt@ ?app/src/main/java/com/example/callrecorder/util/ErrorHandler.kt@ ?app/src/main/java/com/example/callrecorder/util/ErrorHandler.kt@ ?app/src/main/java/com/example/callrecorder/util/ErrorHandler.ktK Japp/src/main/java/com/example/callrecorder/util/RecordingServiceStarter.kt