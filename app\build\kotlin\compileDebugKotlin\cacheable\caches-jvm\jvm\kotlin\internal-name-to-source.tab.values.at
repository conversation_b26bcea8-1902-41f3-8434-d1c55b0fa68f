/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/example/callrecorder/MainActivity.kt; :app/src/main/java/com/example/callrecorder/MainActivity.kt; :app/src/main/java/com/example/callrecorder/MainActivity.ktK Japp/src/main/java/com/example/callrecorder/data/CallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktS Rapp/src/main/java/com/example/callrecorder/data/InMemoryCallRecordingRepository.ktC Bapp/src/main/java/com/example/callrecorder/domain/CallRecording.ktO Napp/src/main/java/com/example/callrecorder/service/CallAccessibilityService.ktK Japp/src/main/java/com/example/callrecorder/service/CallRecordingService.ktL Kapp/src/main/java/com/example/callrecorder/service/MediaProjectionHelper.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktA @app/src/main/java/com/example/callrecorder/ui/CallRecorderApp.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.ktJ Iapp/src/main/java/com/example/callrecorder/ui/CallRecordingsListScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.kt< ;app/src/main/java/com/example/callrecorder/ui/MainScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktL Kapp/src/main/java/com/example/callrecorder/ui/PermissionAndConsentScreen.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktB Aapp/src/main/java/com/example/callrecorder/ui/PlaybackControls.ktE Dapp/src/main/java/com/example/callrecorder/ui/PrivacyPolicyScreen.ktE Dapp/src/main/java/com/example/callrecorder/ui/PrivacyPolicyScreen.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktF Eapp/src/main/java/com/example/callrecorder/ui/StartRecordingButton.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktS Rapp/src/main/java/com/example/callrecorder/ui/viewmodel/CallRecordingsViewModel.ktK Japp/src/main/java/com/example/callrecorder/util/RecordingServiceStarter.kt