package com.example.callrecorder.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u0006\u0004"}, d2 = {"PermissionAndConsentScreen", "", "onPermissionsGranted", "Lkotlin/Function0;", "app_debug"})
public final class PermissionAndConsentScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void PermissionAndConsentScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionsGranted) {
    }
}