package com.example.callrecorder.ai;

/**
 * OpenAI API service interface
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001JX\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\b2\n\b\u0003\u0010\t\u001a\u0004\u0018\u00010\b2\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\b2\n\b\u0003\u0010\u000b\u001a\u0004\u0018\u00010\b2\n\b\u0003\u0010\f\u001a\u0004\u0018\u00010\bH\u00a7@\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/callrecorder/ai/OpenAIApiService;", "", "transcribeAudio", "Lretrofit2/Response;", "Lcom/example/callrecorder/ai/TranscriptionResponse;", "file", "Lokhttp3/MultipartBody$Part;", "model", "Lokhttp3/RequestBody;", "language", "prompt", "responseFormat", "temperature", "(Lokhttp3/MultipartBody$Part;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface OpenAIApiService {
    
    @retrofit2.http.Multipart()
    @retrofit2.http.POST(value = "audio/transcriptions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object transcribeAudio(@retrofit2.http.Part()
    @org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part file, @retrofit2.http.Part(value = "model")
    @org.jetbrains.annotations.NotNull()
    okhttp3.RequestBody model, @retrofit2.http.Part(value = "language")
    @org.jetbrains.annotations.Nullable()
    okhttp3.RequestBody language, @retrofit2.http.Part(value = "prompt")
    @org.jetbrains.annotations.Nullable()
    okhttp3.RequestBody prompt, @retrofit2.http.Part(value = "response_format")
    @org.jetbrains.annotations.Nullable()
    okhttp3.RequestBody responseFormat, @retrofit2.http.Part(value = "temperature")
    @org.jetbrains.annotations.Nullable()
    okhttp3.RequestBody temperature, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.callrecorder.ai.TranscriptionResponse>> $completion);
    
    /**
     * OpenAI API service interface
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}