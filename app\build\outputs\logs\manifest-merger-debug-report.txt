-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:70:9-78:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:74:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:72:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:73:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:71:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:2:1-81:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\fb97f9c2319f7a9720c0cc20f156d813\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\daf4f9cd6fb4c668a14c72918564b691\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddc0f7b1d36315427b3b94245daed209\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae27a955873c3325260ee5c5d3200b94\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a328adb251609b0a25c3e2722fc4c91\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7d33c864b7bb128bdefcc5a6ca29856\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fba63af1cc2824d2492e5346a43ea817\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\eba5d52d81a090156fc480df4b436622\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a1e6485fa667389bf301596aad53276\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83426123506a9f00085f2a3d0abb887c\transformed\jetified-activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\add3946acdc6e9e6fd979649704e79d9\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f481f24d3159e23b2c7b2fc7d3454301\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c950e91bf8e4335fbe5da69159f4f7b5\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c57cda1d8ff7d6bb2cf72738f1ad6fd\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ed3af94b8739c3888456890e53de0ec9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\024ed04d30f412b603f78a641454e7df\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0-alpha13] C:\Users\<USER>\.gradle\caches\8.10\transforms\04dcd93478568785e2fe2399acbfeb7b\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\4732a0661345d7666aa559ef67f47092\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0b15a12852f7ab2270bf3e1acbc9952f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\18026ccf177b770611de475615964afc\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\592e4287f6f7d7a00050272a6ef06307\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a4a1a2591784a6d537d4996287664fc0\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\74312a8f365a5b6834204ab8a7e4415d\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8bdfd49a2a1a91d0dcef2081465e8ee5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b966d6ff224f3f10650495ce3dff1ad\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca2df663123a860b92b2821e4b7277b0\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\277a4823f3abdb862139558dc2022506\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b80ce3ea448fe64042c2ec19d868381\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1c0faa1b502066af25b6bc0063dc9ba1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\28a138994d51d2cfdda9d8109ba87a41\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\773ab555ec04fc00a9404af776f65324\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\851300842e4c4bc1868a18752108c809\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24a415696fb99ae483e32c50d9d9b920\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2508b7d82f5d7c353a024f11f2187d2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31aecbd5bc837d97aa51ddb68a584d7c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\64935ad010c8795329b97aec016589a7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c7747767b52f9c62679736508bfe988\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3372269eaa45fea798835e46d26c8aa7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\93e355649c5cd71e96940de0a2295a61\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c08fe4abe250583505a7f1d81de1f38f\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1f1eb3fad1c5a7b30b780b63b085e730\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\087f22a6d2c84d6aacabbb00b143edf4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\14ea4fbb813013b1290d6a0012e59ead\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\04c0b45942cdbd2d6741175858fe97e5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24c04e910d6e4b1659b57f13d5437b99\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a1726711e317e7bdaa82d794c4597c3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203ee40ff80a01b21bb751d905487a95\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d693ca8b91782f4f7203d1b962e033f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6c6dad1bc3fc36f582301d53e61490cf\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c7f121490c7db7f1061ac86ec0732a6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\13b8d01a978b2b20c6cad176665fca7b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\94ca1f3a5b853c4919c3f9574ef1a3b0\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\00edfec3e4c3756f1270fb729bb6eb13\transformed\jetified-lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e58cca23a5c8ba8f7bc63535eec5691a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e7f7d011f16540aebcb51879cc7dd62\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac911541b54f69b5439f11267356d8a6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6ec2aa96b84baf41774004ee5b446661\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8ffa01484e5d0b2260db025a53ae0cf\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c29f333d4ee4537c685f5f165879c21f\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\acdd455cd6a36b15552f687e179716f4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\23b17963b87f0b0f8e0f59ca997548b5\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\3a92b880b0e0a518f0ce0ea506fd8762\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dc72252246cf1ece88e48a4c85d08f9\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\27c6702847efbee43fa380332a187019\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b3a2db18bb0cdca53fe0ff42ef3fe2e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\14292ea35a4a56ca08296d2e2dc48dc6\transformed\documentfile-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\aba6476dbb75437c06cbd72c44961048\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f5b6a837ec86c67337f23a14a0f91bf4\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e29bf595e02081ba297284238420822\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7775899679f5abf60b9407d9a55c98e1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c2c94e78a9a2d4124ec5107c6a1802ea\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b0868f609f549a2ae3973ff04da3888a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebc2af0765a485584e9e386c705411d1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:22-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:5-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:22-91
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:5-85
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:22-82
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:5-108
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:79-105
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:5-107
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:78-104
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:5-111
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:80-108
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:22-79
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:22-76
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:22-69
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:22-65
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:22-75
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:5-79:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:5-79:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b3a2db18bb0cdca53fe0ff42ef3fe2e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b3a2db18bb0cdca53fe0ff42ef3fe2e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:42:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:40:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:41:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:39:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:38:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:43:9-50
activity#com.example.callrecorder.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:44:9-51:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:46:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:45:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:47:13-50:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:27-74
service#com.example.callrecorder.service.CallRecordingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:52:9-56:74
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:54:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:55:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:56:13-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:53:13-57
service#com.example.callrecorder.service.CallAccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:57:9-67:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:60:13-37
	android:permission
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:59:13-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:58:13-61
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:61:13-63:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:17-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:64:13-66:72
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:66:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:65:17-60
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:75:13-77:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:77:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:76:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\fb97f9c2319f7a9720c0cc20f156d813\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\fb97f9c2319f7a9720c0cc20f156d813\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\daf4f9cd6fb4c668a14c72918564b691\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\daf4f9cd6fb4c668a14c72918564b691\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddc0f7b1d36315427b3b94245daed209\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddc0f7b1d36315427b3b94245daed209\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae27a955873c3325260ee5c5d3200b94\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae27a955873c3325260ee5c5d3200b94\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a328adb251609b0a25c3e2722fc4c91\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\6a328adb251609b0a25c3e2722fc4c91\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d6004821a46f58cb297f64956cd160cd\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7d33c864b7bb128bdefcc5a6ca29856\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7d33c864b7bb128bdefcc5a6ca29856\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fc5184c430aa23a7695564d3013cb798\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fba63af1cc2824d2492e5346a43ea817\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\fba63af1cc2824d2492e5346a43ea817\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\eba5d52d81a090156fc480df4b436622\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\eba5d52d81a090156fc480df4b436622\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a1e6485fa667389bf301596aad53276\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a1e6485fa667389bf301596aad53276\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83426123506a9f00085f2a3d0abb887c\transformed\jetified-activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83426123506a9f00085f2a3d0abb887c\transformed\jetified-activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\add3946acdc6e9e6fd979649704e79d9\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\add3946acdc6e9e6fd979649704e79d9\transformed\jetified-activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f481f24d3159e23b2c7b2fc7d3454301\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f481f24d3159e23b2c7b2fc7d3454301\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c950e91bf8e4335fbe5da69159f4f7b5\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c950e91bf8e4335fbe5da69159f4f7b5\transformed\jetified-activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c57cda1d8ff7d6bb2cf72738f1ad6fd\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c57cda1d8ff7d6bb2cf72738f1ad6fd\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ed3af94b8739c3888456890e53de0ec9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ed3af94b8739c3888456890e53de0ec9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\024ed04d30f412b603f78a641454e7df\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\024ed04d30f412b603f78a641454e7df\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0-alpha13] C:\Users\<USER>\.gradle\caches\8.10\transforms\04dcd93478568785e2fe2399acbfeb7b\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout-compose-android:1.1.0-alpha13] C:\Users\<USER>\.gradle\caches\8.10\transforms\04dcd93478568785e2fe2399acbfeb7b\transformed\jetified-constraintlayout-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\4732a0661345d7666aa559ef67f47092\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\4732a0661345d7666aa559ef67f47092\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0b15a12852f7ab2270bf3e1acbc9952f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0b15a12852f7ab2270bf3e1acbc9952f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\18026ccf177b770611de475615964afc\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\18026ccf177b770611de475615964afc\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\592e4287f6f7d7a00050272a6ef06307\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\592e4287f6f7d7a00050272a6ef06307\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a4a1a2591784a6d537d4996287664fc0\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a4a1a2591784a6d537d4996287664fc0\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\74312a8f365a5b6834204ab8a7e4415d\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\74312a8f365a5b6834204ab8a7e4415d\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8bdfd49a2a1a91d0dcef2081465e8ee5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8bdfd49a2a1a91d0dcef2081465e8ee5\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b966d6ff224f3f10650495ce3dff1ad\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b966d6ff224f3f10650495ce3dff1ad\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca2df663123a860b92b2821e4b7277b0\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca2df663123a860b92b2821e4b7277b0\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\277a4823f3abdb862139558dc2022506\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\277a4823f3abdb862139558dc2022506\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b80ce3ea448fe64042c2ec19d868381\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b80ce3ea448fe64042c2ec19d868381\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1c0faa1b502066af25b6bc0063dc9ba1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1c0faa1b502066af25b6bc0063dc9ba1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\28a138994d51d2cfdda9d8109ba87a41\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\28a138994d51d2cfdda9d8109ba87a41\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\773ab555ec04fc00a9404af776f65324\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\773ab555ec04fc00a9404af776f65324\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\851300842e4c4bc1868a18752108c809\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\851300842e4c4bc1868a18752108c809\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24a415696fb99ae483e32c50d9d9b920\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24a415696fb99ae483e32c50d9d9b920\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2508b7d82f5d7c353a024f11f2187d2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2508b7d82f5d7c353a024f11f2187d2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31aecbd5bc837d97aa51ddb68a584d7c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\31aecbd5bc837d97aa51ddb68a584d7c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\64935ad010c8795329b97aec016589a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\64935ad010c8795329b97aec016589a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c7747767b52f9c62679736508bfe988\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5c7747767b52f9c62679736508bfe988\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3372269eaa45fea798835e46d26c8aa7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3372269eaa45fea798835e46d26c8aa7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\93e355649c5cd71e96940de0a2295a61\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\93e355649c5cd71e96940de0a2295a61\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c08fe4abe250583505a7f1d81de1f38f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c08fe4abe250583505a7f1d81de1f38f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1f1eb3fad1c5a7b30b780b63b085e730\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1f1eb3fad1c5a7b30b780b63b085e730\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\087f22a6d2c84d6aacabbb00b143edf4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\087f22a6d2c84d6aacabbb00b143edf4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\14ea4fbb813013b1290d6a0012e59ead\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\14ea4fbb813013b1290d6a0012e59ead\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\04c0b45942cdbd2d6741175858fe97e5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\04c0b45942cdbd2d6741175858fe97e5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24c04e910d6e4b1659b57f13d5437b99\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\24c04e910d6e4b1659b57f13d5437b99\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a1726711e317e7bdaa82d794c4597c3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a1726711e317e7bdaa82d794c4597c3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203ee40ff80a01b21bb751d905487a95\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203ee40ff80a01b21bb751d905487a95\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d693ca8b91782f4f7203d1b962e033f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d693ca8b91782f4f7203d1b962e033f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6c6dad1bc3fc36f582301d53e61490cf\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6c6dad1bc3fc36f582301d53e61490cf\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c7f121490c7db7f1061ac86ec0732a6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3c7f121490c7db7f1061ac86ec0732a6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\13b8d01a978b2b20c6cad176665fca7b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\13b8d01a978b2b20c6cad176665fca7b\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\94ca1f3a5b853c4919c3f9574ef1a3b0\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\94ca1f3a5b853c4919c3f9574ef1a3b0\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\00edfec3e4c3756f1270fb729bb6eb13\transformed\jetified-lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\00edfec3e4c3756f1270fb729bb6eb13\transformed\jetified-lifecycle-runtime-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e58cca23a5c8ba8f7bc63535eec5691a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e58cca23a5c8ba8f7bc63535eec5691a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e7f7d011f16540aebcb51879cc7dd62\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e7f7d011f16540aebcb51879cc7dd62\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac911541b54f69b5439f11267356d8a6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ac911541b54f69b5439f11267356d8a6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6ec2aa96b84baf41774004ee5b446661\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6ec2aa96b84baf41774004ee5b446661\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8ffa01484e5d0b2260db025a53ae0cf\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8ffa01484e5d0b2260db025a53ae0cf\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c29f333d4ee4537c685f5f165879c21f\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c29f333d4ee4537c685f5f165879c21f\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\acdd455cd6a36b15552f687e179716f4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\acdd455cd6a36b15552f687e179716f4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\23b17963b87f0b0f8e0f59ca997548b5\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\23b17963b87f0b0f8e0f59ca997548b5\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\3a92b880b0e0a518f0ce0ea506fd8762\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\3a92b880b0e0a518f0ce0ea506fd8762\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dc72252246cf1ece88e48a4c85d08f9\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\0dc72252246cf1ece88e48a4c85d08f9\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\27c6702847efbee43fa380332a187019\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\27c6702847efbee43fa380332a187019\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b3a2db18bb0cdca53fe0ff42ef3fe2e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b3a2db18bb0cdca53fe0ff42ef3fe2e6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\14292ea35a4a56ca08296d2e2dc48dc6\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\14292ea35a4a56ca08296d2e2dc48dc6\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\aba6476dbb75437c06cbd72c44961048\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\aba6476dbb75437c06cbd72c44961048\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f5b6a837ec86c67337f23a14a0f91bf4\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f5b6a837ec86c67337f23a14a0f91bf4\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e29bf595e02081ba297284238420822\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0e29bf595e02081ba297284238420822\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7775899679f5abf60b9407d9a55c98e1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7775899679f5abf60b9407d9a55c98e1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c2c94e78a9a2d4124ec5107c6a1802ea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c2c94e78a9a2d4124ec5107c6a1802ea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b0868f609f549a2ae3973ff04da3888a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b0868f609f549a2ae3973ff04da3888a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebc2af0765a485584e9e386c705411d1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebc2af0765a485584e9e386c705411d1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\746f52b5ecf76bec414ea2edb55a966b\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eda4f819e80e4664673dcaab03ee67a7\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.10\transforms\b89d1804dcedc1a047a2d8eb7bf8e1c7\transformed\jetified-ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
