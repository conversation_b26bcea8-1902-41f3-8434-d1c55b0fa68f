package com.example.callrecorder.data

import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import java.util.*

class CallRecordingRepository(context: Context) {
    fun getAllRecordings(): Flow<List<CallRecordingDisplay>> = flowOf(emptyList())
    suspend fun getRecordingById(id: Long): CallRecording? = null
    suspend fun insertRecording(recording: CallRecording): Long = 0L
    suspend fun deleteRecordingById(id: Long) {}
    suspend fun updateTranscriptionStatus(id: Long, isTranscribed: Boolean, transcriptPath: String?) {}
}
