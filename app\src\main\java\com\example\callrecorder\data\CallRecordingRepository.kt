package com.example.callrecorder.data

import com.example.callrecorder.domain.CallRecording

interface CallRecordingRepository {
    suspend fun getAllRecordings(): List<CallRecording>
    suspend fun getRecordingById(id: Long): CallRecording?
    suspend fun addRecording(recording: CallRecording): Long
    suspend fun deleteRecording(id: Long)
    suspend fun updateTranscription(id: Long, transcription: String)
}
