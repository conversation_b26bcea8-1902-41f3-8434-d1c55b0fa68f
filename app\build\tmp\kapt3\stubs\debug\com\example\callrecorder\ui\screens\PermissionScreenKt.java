package com.example.callrecorder.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0000\u001a \u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0003H\u0007\u001a\u0016\u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a.\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a0\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u001e\u0010\u0010\u001a\u001a\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u0012\u0012\u0004\u0012\u00020\u00010\u0011H\u0002\u00a8\u0006\u0013"}, d2 = {"PermissionItem", "", "permission", "", "isGranted", "", "explanation", "PermissionScreen", "onPermissionsGranted", "Lkotlin/Function0;", "SpecialPermissionItem", "title", "onRequest", "checkAllPermissions", "permissionManager", "Lcom/example/callrecorder/permission/PermissionManager;", "onResult", "Lkotlin/Function1;", "", "app_debug"})
public final class PermissionScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void PermissionScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPermissionsGranted) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PermissionItem(@org.jetbrains.annotations.NotNull()
    java.lang.String permission, boolean isGranted, @org.jetbrains.annotations.NotNull()
    java.lang.String explanation) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SpecialPermissionItem(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String explanation, boolean isGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRequest) {
    }
    
    private static final void checkAllPermissions(com.example.callrecorder.permission.PermissionManager permissionManager, kotlin.jvm.functions.Function1<? super java.util.Map<java.lang.String, java.lang.Boolean>, kotlin.Unit> onResult) {
    }
}