package com.example.callrecorder.ui.dialogs

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog

/**
 * Privacy consent dialog that must be shown before recording any call
 * Ensures compliance with privacy laws and regulations
 */
@Composable
fun ConsentDialog(
    isVisible: Boolean,
    onConsentGiven: () -> Unit,
    onConsentDenied: () -> Unit,
    onDismiss: () -> Unit
) {
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            ConsentDialogContent(
                onConsentGiven = onConsentGiven,
                onConsentDenied = onConsentDenied
            )
        }
    }
}

@Composable
private fun ConsentDialogContent(
    onConsentGiven: () -> Unit,
    onConsentDenied: () -> Unit
) {
    var hasReadTerms by remember { mutableStateOf(false) }
    var acknowledgeCompliance by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Warning icon
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "Warning",
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.error
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Title
            Text(
                text = "Call Recording Consent",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Legal notice
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                ),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "⚠️ IMPORTANT LEGAL NOTICE",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.padding(12.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Terms and conditions
            Text(
                text = buildString {
                    append("By using this call recording feature, you acknowledge and agree to the following:\n\n")
                    
                    append("1. LEGAL COMPLIANCE\n")
                    append("• You are solely responsible for complying with all applicable laws and regulations regarding call recording in your jurisdiction.\n")
                    append("• In many countries/states, you must inform all parties that the call is being recorded.\n")
                    append("• Some jurisdictions require explicit consent from all parties before recording.\n\n")
                    
                    append("2. PRIVACY RESPONSIBILITIES\n")
                    append("• You must respect the privacy rights of all call participants.\n")
                    append("• You are responsible for securing and protecting recorded content.\n")
                    append("• You must not use recordings for illegal purposes.\n\n")
                    
                    append("3. TECHNICAL LIMITATIONS\n")
                    append("• Call recording may not work on all devices or Android versions.\n")
                    append("• Android 10+ has restrictions that may prevent call audio capture.\n")
                    append("• Recording quality depends on device capabilities.\n\n")
                    
                    append("4. LIABILITY DISCLAIMER\n")
                    append("• The app developers are not responsible for any legal consequences of your use of this feature.\n")
                    append("• You use this feature at your own risk and responsibility.\n")
                    append("• No warranty is provided regarding recording functionality or legal compliance.")
                },
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Checkboxes
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = hasReadTerms,
                    onCheckedChange = { hasReadTerms = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "I have read and understood the above terms",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = acknowledgeCompliance,
                    onCheckedChange = { acknowledgeCompliance = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "I acknowledge my responsibility to comply with all applicable laws",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                OutlinedButton(
                    onClick = onConsentDenied,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("I Do Not Consent")
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Button(
                    onClick = onConsentGiven,
                    enabled = hasReadTerms && acknowledgeCompliance,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("I Consent & Agree")
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Additional warning
            Text(
                text = "This consent applies to all future call recordings until you disable the feature.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Quick consent dialog for individual calls
 */
@Composable
fun QuickConsentDialog(
    isVisible: Boolean,
    phoneNumber: String,
    contactName: String?,
    onConsentGiven: () -> Unit,
    onConsentDenied: () -> Unit,
    onDismiss: () -> Unit
) {
    if (isVisible) {
        AlertDialog(
            onDismissRequest = onDismiss,
            icon = {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = "Warning",
                    tint = MaterialTheme.colorScheme.error
                )
            },
            title = {
                Text("Record This Call?")
            },
            text = {
                Column {
                    Text(
                        text = "Do you want to record the call with ${contactName ?: phoneNumber}?"
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "⚠️ Ensure you comply with local laws. You may need to inform the other party.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            confirmButton = {
                Button(onClick = onConsentGiven) {
                    Text("Record")
                }
            },
            dismissButton = {
                OutlinedButton(onClick = onConsentDenied) {
                    Text("Don't Record")
                }
            }
        )
    }
}
