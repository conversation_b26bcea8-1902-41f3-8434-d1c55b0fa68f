<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Kotlin Android project for a call recording app targeting API 24+ and SDK 35. Use MVVM architecture, Jetpack Compose for UI, and implement call recording using MediaProjection/AccessibilityService for Android 10+. Ensure all permissions, privacy, and legal compliance are handled. Include comments about platform limitations and user consent.
