package com.example.callrecorder.data

import com.example.callrecorder.domain.CallRecording
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class InMemoryCallRecordingRepository : CallRecordingRepository {
    private val recordings = mutableListOf<CallRecording>()
    private var nextId = 1L

    override suspend fun getAllRecordings(): List<CallRecording> = withContext(Dispatchers.IO) {
        recordings.toList()
    }

    override suspend fun getRecordingById(id: Long): CallRecording? = withContext(Dispatchers.IO) {
        recordings.find { it.id == id }
    }

    override suspend fun addRecording(recording: CallRecording): Long = withContext(Dispatchers.IO) {
        val newRecording = recording.copy(id = nextId++)
        recordings.add(newRecording)
        newRecording.id
    }

    override suspend fun deleteRecording(id: Long) = withContext(Dispatchers.IO) {
        recordings.removeAll { it.id == id }
        Unit
    }

    override suspend fun updateTranscription(id: Long, transcription: String) = withContext(Dispatchers.IO) {
        val index = recordings.indexOfFirst { it.id == id }
        if (index != -1) {
            recordings[index] = recordings[index].copy(transcription = transcription)
        }
    }
}
