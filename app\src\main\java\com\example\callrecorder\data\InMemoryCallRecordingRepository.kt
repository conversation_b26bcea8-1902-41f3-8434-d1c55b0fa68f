package com.example.callrecorder.data

import com.example.callrecorder.domain.CallRecording
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class InMemoryCallRecordingRepository : CallRecordingRepository {
    private val recordings = mutableListOf<CallRecording>()
    private var nextId = 1L

    override suspend fun getAllRecordings(): List<CallRecording> = withContext(Dispatchers.IO) {
        recordings.toList()
    }

    override suspend fun getRecordingById(id: Long): CallRecording? = withContext(Dispatchers.IO) {
        recordings.find { it.id == id }
    }

    override suspend fun addRecording(recording: CallRecording): Long = withContext(Dispatchers.IO) {
        val newRecording = recording.copy(id = nextId++)
        recordings.add(newRecording)
        newRecording.id
    }

    override suspend fun deleteRecording(id: Long) = withContext(Dispatchers.IO) {
        recordings.removeAll { it.id == id }
        Unit
    }

    override suspend fun updateTranscription(id: Long, transcription: String) = withContext(Dispatchers.IO) {
        val index = recordings.indexOfFirst { it.id == id }
        if (index != -1) {
            recordings[index] = recordings[index].copy(transcription = transcription)
        }
    }

    /**
     * Convert CallRecording to CallRecordingDisplay with formatted data
     */
    private fun CallRecording.toDisplayObject(): CallRecordingDisplay {
        return CallRecordingDisplay(
            id = id,
            phoneNumber = phoneNumber,
            contactName = contactName,
            callType = callType,
            callDate = callDate,
            duration = formatDuration(duration),
            filePath = filePath,
            fileSize = formatFileSize(fileSize),
            isTranscribed = isTranscribed,
            transcriptPath = transcriptPath
        )
    }

    /**
     * Format duration in milliseconds to readable string
     */
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            minutes > 0 -> String.format("%d:%02d", minutes, seconds % 60)
            else -> String.format("0:%02d", seconds)
        }
    }

    /**
     * Format file size in bytes to readable string
     */
    private fun formatFileSize(sizeBytes: Long): String {
        val kb = sizeBytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$sizeBytes B"
        }
    }
}
