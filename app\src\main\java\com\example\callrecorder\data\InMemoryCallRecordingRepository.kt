package com.example.callrecorder.data

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import java.util.Date

class InMemoryCallRecordingRepository {
    private val recordings = mutableListOf<CallRecording>()
    private var nextId = 1L

    fun getAllRecordings(): Flow<List<CallRecordingDisplay>> {
        return flowOf(recordings.map { it.toDisplayObject() })
    }

    suspend fun getRecordingById(id: Long): CallRecording? = withContext(Dispatchers.IO) {
        recordings.find { it.id == id }
    }

    suspend fun insertRecording(recording: CallRecording): Long = withContext(Dispatchers.IO) {
        val newRecording = recording.copy(id = nextId++)
        recordings.add(newRecording)
        newRecording.id
    }

    suspend fun deleteRecordingById(id: Long) = withContext(Dispatchers.IO) {
        recordings.removeAll { it.id == id }
        Unit
    }

    suspend fun updateTranscriptionStatus(id: Long, isTranscribed: Boolean, transcriptPath: String?) = withContext(Dispatchers.IO) {
        val index = recordings.indexOfFirst { it.id == id }
        if (index != -1) {
            // Create a new recording with updated transcription info
            val oldRecording = recordings[index]
            recordings[index] = CallRecording(
                id = oldRecording.id,
                phoneNumber = oldRecording.phoneNumber,
                contactName = oldRecording.contactName,
                callType = oldRecording.callType,
                callDate = oldRecording.callDate,
                duration = oldRecording.duration,
                filePath = oldRecording.filePath,
                fileSize = oldRecording.fileSize,
                recordingDuration = oldRecording.recordingDuration,
                isTranscribed = isTranscribed,
                transcriptPath = transcriptPath,
                createdAt = oldRecording.createdAt,
                updatedAt = Date()
            )
        }
    }

    /**
     * Convert CallRecording to CallRecordingDisplay with formatted data
     */
    private fun CallRecording.toDisplayObject(): CallRecordingDisplay {
        return CallRecordingDisplay(
            id = id,
            phoneNumber = phoneNumber,
            contactName = contactName,
            callType = callType,
            callDate = callDate,
            duration = formatDuration(duration),
            filePath = filePath,
            fileSize = formatFileSize(fileSize),
            isTranscribed = isTranscribed,
            transcriptPath = transcriptPath
        )
    }

    /**
     * Format duration in milliseconds to readable string
     */
    private fun formatDuration(durationMs: Long): String {
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60

        return when {
            hours > 0 -> String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60)
            minutes > 0 -> String.format("%d:%02d", minutes, seconds % 60)
            else -> String.format("0:%02d", seconds)
        }
    }

    /**
     * Format file size in bytes to readable string
     */
    private fun formatFileSize(sizeBytes: Long): String {
        val kb = sizeBytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$sizeBytes B"
        }
    }
}
