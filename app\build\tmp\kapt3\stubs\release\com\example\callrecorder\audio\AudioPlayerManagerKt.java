package com.example.callrecorder.audio;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\f\n\u0000\n\u0002\u0010\u000e\n\u0002\u0010\b\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u00a8\u0006\u0003"}, d2 = {"formatTime", "", "", "app_release"})
public final class AudioPlayerManagerKt {
    
    /**
     * Extension functions for formatting time
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatTime(int $this$formatTime) {
        return null;
    }
}