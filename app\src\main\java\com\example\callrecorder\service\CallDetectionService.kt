package com.example.callrecorder.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.media.AudioManager
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.CallLog
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.util.Log
import com.example.callrecorder.data.CallType
import com.example.callrecorder.permission.PermissionManager
import kotlinx.coroutines.*

/**
 * Service for detecting incoming and outgoing calls
 * 
 * Android Version Limitations:
 * - Android 9+ (API 28+): Background execution limits affect call detection
 * - Android 10+ (API 29+): Scoped storage affects call log access
 * - Android 11+ (API 30+): Package visibility restrictions
 * - Android 12+ (API 31+): Exact alarm restrictions
 * - Android 13+ (API 33+): Runtime notification permission required
 */
class CallDetectionService {
    
    companion object {
        private const val TAG = "CallDetectionService"
    }
    
    private var context: Context? = null
    private var telephonyManager: TelephonyManager? = null
    private var phoneStateListener: PhoneStateListener? = null
    private var callLogObserver: ContentObserver? = null
    private var outgoingCallReceiver: BroadcastReceiver? = null
    private var audioManager: AudioManager? = null
    private var audioFocusChangeListener: AudioManager.OnAudioFocusChangeListener? = null

    private var isListening = false
    private var lastCallState = TelephonyManager.CALL_STATE_IDLE
    private var currentPhoneNumber: String? = null
    private var callStartTime: Long = 0
    private var isVoIPCallActive = false
    private var lastAudioMode = AudioManager.MODE_NORMAL
    
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var onCallDetected: ((phoneNumber: String, callType: CallType, contactName: String?) -> Unit)? = null
    private var onCallEnded: ((phoneNumber: String, callType: CallType, duration: Long) -> Unit)? = null
    
    /**
     * Initialize call detection service
     */
    fun initialize(
        context: Context,
        onCallDetected: (phoneNumber: String, callType: CallType, contactName: String?) -> Unit,
        onCallEnded: (phoneNumber: String, callType: CallType, duration: Long) -> Unit
    ) {
        this.context = context
        this.onCallDetected = onCallDetected
        this.onCallEnded = onCallEnded
        
        telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

        setupPhoneStateListener()
        setupCallLogObserver()
        setupOutgoingCallReceiver()
        setupVoIPCallDetection()
    }
    
    /**
     * Start listening for calls
     */
    fun startListening() {
        if (isListening) return
        
        val permissionManager = PermissionManager(context!!)
        if (!permissionManager.areAllPermissionsGranted()) {
            Log.w(TAG, "Required permissions not granted")
            return
        }
        
        try {
            // Register phone state listener
            phoneStateListener?.let { listener ->
                telephonyManager?.listen(listener, PhoneStateListener.LISTEN_CALL_STATE)
            }
            
            // Register call log observer
            callLogObserver?.let { observer ->
                context?.contentResolver?.registerContentObserver(
                    CallLog.Calls.CONTENT_URI,
                    true,
                    observer
                )
            }
            
            // Register outgoing call receiver
            outgoingCallReceiver?.let { receiver ->
                val filter = IntentFilter(Intent.ACTION_NEW_OUTGOING_CALL)
                context?.registerReceiver(receiver, filter)
            }
            
            isListening = true
            Log.d(TAG, "Call detection started")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting call detection", e)
        }
    }
    
    /**
     * Stop listening for calls
     */
    fun stopListening() {
        if (!isListening) return
        
        try {
            // Unregister phone state listener
            phoneStateListener?.let { listener ->
                telephonyManager?.listen(listener, PhoneStateListener.LISTEN_NONE)
            }
            
            // Unregister call log observer
            callLogObserver?.let { observer ->
                context?.contentResolver?.unregisterContentObserver(observer)
            }
            
            // Unregister outgoing call receiver
            outgoingCallReceiver?.let { receiver ->
                context?.unregisterReceiver(receiver)
            }
            
            isListening = false
            Log.d(TAG, "Call detection stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping call detection", e)
        }
    }
    
    /**
     * Setup phone state listener for call state changes
     */
    private fun setupPhoneStateListener() {
        phoneStateListener = object : PhoneStateListener() {
            override fun onCallStateChanged(state: Int, phoneNumber: String?) {
                super.onCallStateChanged(state, phoneNumber)
                handleCallStateChange(state, phoneNumber)
            }
        }
    }
    
    /**
     * Setup call log observer for detecting call details
     */
    private fun setupCallLogObserver() {
        callLogObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                // Handle call log changes (for getting additional call details)
                serviceScope.launch {
                    handleCallLogChange()
                }
            }
        }
    }
    
    /**
     * Setup broadcast receiver for outgoing calls
     */
    private fun setupOutgoingCallReceiver() {
        outgoingCallReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == Intent.ACTION_NEW_OUTGOING_CALL) {
                    val phoneNumber = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER)
                    phoneNumber?.let {
                        handleOutgoingCall(it)
                    }
                }
            }
        }
    }

    /**
     * Setup VoIP call detection using audio focus changes
     */
    private fun setupVoIPCallDetection() {
        audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> {
                    // Audio focus gained - might be end of VoIP call
                    if (isVoIPCallActive) {
                        handleVoIPCallEnd()
                    }
                }
                AudioManager.AUDIOFOCUS_LOSS,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                    // Audio focus lost - might be start of VoIP call
                    checkForVoIPCall()
                }
            }
        }

        // Monitor audio mode changes
        val handler = Handler(Looper.getMainLooper())
        val audioModeChecker = object : Runnable {
            override fun run() {
                checkAudioModeChanges()
                handler.postDelayed(this, 1000) // Check every second
            }
        }
        handler.post(audioModeChecker)
    }

    /**
     * Check for VoIP call based on audio mode and running apps
     */
    private fun checkForVoIPCall() {
        val currentAudioMode = audioManager?.mode ?: AudioManager.MODE_NORMAL

        // Check if audio mode indicates a call
        if (currentAudioMode == AudioManager.MODE_IN_COMMUNICATION ||
            currentAudioMode == AudioManager.MODE_IN_CALL) {

            // Check if it's not a regular phone call
            if (lastCallState == TelephonyManager.CALL_STATE_IDLE && !isVoIPCallActive) {
                Log.d(TAG, "VoIP call detected - Audio mode: $currentAudioMode")
                handleVoIPCallStart()
            }
        }
    }

    /**
     * Check for audio mode changes that might indicate VoIP calls
     */
    private fun checkAudioModeChanges() {
        val currentAudioMode = audioManager?.mode ?: AudioManager.MODE_NORMAL

        if (currentAudioMode != lastAudioMode) {
            Log.d(TAG, "Audio mode changed: $lastAudioMode -> $currentAudioMode")

            when (currentAudioMode) {
                AudioManager.MODE_IN_COMMUNICATION,
                AudioManager.MODE_IN_CALL -> {
                    if (lastCallState == TelephonyManager.CALL_STATE_IDLE && !isVoIPCallActive) {
                        handleVoIPCallStart()
                    }
                }
                AudioManager.MODE_NORMAL -> {
                    if (isVoIPCallActive) {
                        handleVoIPCallEnd()
                    }
                }
            }

            lastAudioMode = currentAudioMode
        }
    }

    /**
     * Handle VoIP call start
     */
    private fun handleVoIPCallStart() {
        if (isVoIPCallActive) return

        isVoIPCallActive = true
        callStartTime = System.currentTimeMillis()
        currentPhoneNumber = "VoIP Call" // Generic identifier for VoIP calls

        Log.d(TAG, "VoIP call started")
        onCallDetected?.invoke("VoIP Call", CallType.INCOMING, "WhatsApp/VoIP")
    }

    /**
     * Handle VoIP call end
     */
    private fun handleVoIPCallEnd() {
        if (!isVoIPCallActive) return

        val duration = System.currentTimeMillis() - callStartTime
        Log.d(TAG, "VoIP call ended, duration: $duration ms")

        onCallEnded?.invoke(currentPhoneNumber ?: "VoIP Call", CallType.INCOMING, duration)

        isVoIPCallActive = false
        currentPhoneNumber = null
        callStartTime = 0
    }
    
    /**
     * Handle call state changes
     */
    private fun handleCallStateChange(state: Int, phoneNumber: String?) {
        Log.d(TAG, "Call state changed: $state, number: $phoneNumber")
        
        when (state) {
            TelephonyManager.CALL_STATE_RINGING -> {
                // Incoming call
                phoneNumber?.let { number ->
                    currentPhoneNumber = number
                    val contactName = getContactName(number)
                    onCallDetected?.invoke(number, CallType.INCOMING, contactName)
                }
            }
            
            TelephonyManager.CALL_STATE_OFFHOOK -> {
                // Call answered or outgoing call connected
                if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                    // Incoming call answered
                    callStartTime = System.currentTimeMillis()
                } else if (lastCallState == TelephonyManager.CALL_STATE_IDLE) {
                    // Outgoing call connected
                    callStartTime = System.currentTimeMillis()
                }
            }
            
            TelephonyManager.CALL_STATE_IDLE -> {
                // Call ended
                if (lastCallState != TelephonyManager.CALL_STATE_IDLE) {
                    currentPhoneNumber?.let { number ->
                        val duration = System.currentTimeMillis() - callStartTime
                        val callType = if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                            CallType.INCOMING
                        } else {
                            CallType.OUTGOING
                        }
                        onCallEnded?.invoke(number, callType, duration)
                    }
                    currentPhoneNumber = null
                    callStartTime = 0
                }
            }
        }
        
        lastCallState = state
    }
    
    /**
     * Handle outgoing call detection
     */
    private fun handleOutgoingCall(phoneNumber: String) {
        Log.d(TAG, "Outgoing call detected: $phoneNumber")
        currentPhoneNumber = phoneNumber
        val contactName = getContactName(phoneNumber)
        onCallDetected?.invoke(phoneNumber, CallType.OUTGOING, contactName)
    }
    
    /**
     * Handle call log changes
     */
    private suspend fun handleCallLogChange() {
        // This can be used to get additional call information
        // from the call log if needed
    }
    
    /**
     * Get contact name for phone number
     */
    private fun getContactName(phoneNumber: String): String? {
        return try {
            val context = this.context ?: return null
            val uri = Uri.withAppendedPath(
                android.provider.ContactsContract.PhoneLookup.CONTENT_FILTER_URI,
                Uri.encode(phoneNumber)
            )
            
            val cursor = context.contentResolver.query(
                uri,
                arrayOf(android.provider.ContactsContract.PhoneLookup.DISPLAY_NAME),
                null,
                null,
                null
            )
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex(android.provider.ContactsContract.PhoneLookup.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        return it.getString(nameIndex)
                    }
                }
            }
            
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting contact name", e)
            null
        }
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopListening()
        serviceScope.cancel()
        context = null
        onCallDetected = null
        onCallEnded = null
    }
}
