package com.example.callrecorder.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.CallLog
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.util.Log
import com.example.callrecorder.data.CallType
import com.example.callrecorder.permission.PermissionManager
import kotlinx.coroutines.*

/**
 * Service for detecting incoming and outgoing calls
 * 
 * Android Version Limitations:
 * - Android 9+ (API 28+): Background execution limits affect call detection
 * - Android 10+ (API 29+): Scoped storage affects call log access
 * - Android 11+ (API 30+): Package visibility restrictions
 * - Android 12+ (API 31+): Exact alarm restrictions
 * - Android 13+ (API 33+): Runtime notification permission required
 */
class CallDetectionService {
    
    companion object {
        private const val TAG = "CallDetectionService"
    }
    
    private var context: Context? = null
    private var telephonyManager: TelephonyManager? = null
    private var phoneStateListener: PhoneStateListener? = null
    private var callLogObserver: ContentObserver? = null
    private var outgoingCallReceiver: BroadcastReceiver? = null
    
    private var isListening = false
    private var lastCallState = TelephonyManager.CALL_STATE_IDLE
    private var currentPhoneNumber: String? = null
    private var callStartTime: Long = 0
    
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var onCallDetected: ((phoneNumber: String, callType: CallType, contactName: String?) -> Unit)? = null
    private var onCallEnded: ((phoneNumber: String, callType: CallType, duration: Long) -> Unit)? = null
    
    /**
     * Initialize call detection service
     */
    fun initialize(
        context: Context,
        onCallDetected: (phoneNumber: String, callType: CallType, contactName: String?) -> Unit,
        onCallEnded: (phoneNumber: String, callType: CallType, duration: Long) -> Unit
    ) {
        this.context = context
        this.onCallDetected = onCallDetected
        this.onCallEnded = onCallEnded
        
        telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        setupPhoneStateListener()
        setupCallLogObserver()
        setupOutgoingCallReceiver()
    }
    
    /**
     * Start listening for calls
     */
    fun startListening() {
        if (isListening) return
        
        val permissionManager = PermissionManager(context!!)
        if (!permissionManager.areAllPermissionsGranted()) {
            Log.w(TAG, "Required permissions not granted")
            return
        }
        
        try {
            // Register phone state listener
            phoneStateListener?.let { listener ->
                telephonyManager?.listen(listener, PhoneStateListener.LISTEN_CALL_STATE)
            }
            
            // Register call log observer
            callLogObserver?.let { observer ->
                context?.contentResolver?.registerContentObserver(
                    CallLog.Calls.CONTENT_URI,
                    true,
                    observer
                )
            }
            
            // Register outgoing call receiver
            outgoingCallReceiver?.let { receiver ->
                val filter = IntentFilter(Intent.ACTION_NEW_OUTGOING_CALL)
                context?.registerReceiver(receiver, filter)
            }
            
            isListening = true
            Log.d(TAG, "Call detection started")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error starting call detection", e)
        }
    }
    
    /**
     * Stop listening for calls
     */
    fun stopListening() {
        if (!isListening) return
        
        try {
            // Unregister phone state listener
            phoneStateListener?.let { listener ->
                telephonyManager?.listen(listener, PhoneStateListener.LISTEN_NONE)
            }
            
            // Unregister call log observer
            callLogObserver?.let { observer ->
                context?.contentResolver?.unregisterContentObserver(observer)
            }
            
            // Unregister outgoing call receiver
            outgoingCallReceiver?.let { receiver ->
                context?.unregisterReceiver(receiver)
            }
            
            isListening = false
            Log.d(TAG, "Call detection stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping call detection", e)
        }
    }
    
    /**
     * Setup phone state listener for call state changes
     */
    private fun setupPhoneStateListener() {
        phoneStateListener = object : PhoneStateListener() {
            override fun onCallStateChanged(state: Int, phoneNumber: String?) {
                super.onCallStateChanged(state, phoneNumber)
                handleCallStateChange(state, phoneNumber)
            }
        }
    }
    
    /**
     * Setup call log observer for detecting call details
     */
    private fun setupCallLogObserver() {
        callLogObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                // Handle call log changes (for getting additional call details)
                serviceScope.launch {
                    handleCallLogChange()
                }
            }
        }
    }
    
    /**
     * Setup broadcast receiver for outgoing calls
     */
    private fun setupOutgoingCallReceiver() {
        outgoingCallReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == Intent.ACTION_NEW_OUTGOING_CALL) {
                    val phoneNumber = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER)
                    phoneNumber?.let {
                        handleOutgoingCall(it)
                    }
                }
            }
        }
    }
    
    /**
     * Handle call state changes
     */
    private fun handleCallStateChange(state: Int, phoneNumber: String?) {
        Log.d(TAG, "Call state changed: $state, number: $phoneNumber")
        
        when (state) {
            TelephonyManager.CALL_STATE_RINGING -> {
                // Incoming call
                phoneNumber?.let { number ->
                    currentPhoneNumber = number
                    val contactName = getContactName(number)
                    onCallDetected?.invoke(number, CallType.INCOMING, contactName)
                }
            }
            
            TelephonyManager.CALL_STATE_OFFHOOK -> {
                // Call answered or outgoing call connected
                if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                    // Incoming call answered
                    callStartTime = System.currentTimeMillis()
                } else if (lastCallState == TelephonyManager.CALL_STATE_IDLE) {
                    // Outgoing call connected
                    callStartTime = System.currentTimeMillis()
                }
            }
            
            TelephonyManager.CALL_STATE_IDLE -> {
                // Call ended
                if (lastCallState != TelephonyManager.CALL_STATE_IDLE) {
                    currentPhoneNumber?.let { number ->
                        val duration = System.currentTimeMillis() - callStartTime
                        val callType = if (lastCallState == TelephonyManager.CALL_STATE_RINGING) {
                            CallType.INCOMING
                        } else {
                            CallType.OUTGOING
                        }
                        onCallEnded?.invoke(number, callType, duration)
                    }
                    currentPhoneNumber = null
                    callStartTime = 0
                }
            }
        }
        
        lastCallState = state
    }
    
    /**
     * Handle outgoing call detection
     */
    private fun handleOutgoingCall(phoneNumber: String) {
        Log.d(TAG, "Outgoing call detected: $phoneNumber")
        currentPhoneNumber = phoneNumber
        val contactName = getContactName(phoneNumber)
        onCallDetected?.invoke(phoneNumber, CallType.OUTGOING, contactName)
    }
    
    /**
     * Handle call log changes
     */
    private suspend fun handleCallLogChange() {
        // This can be used to get additional call information
        // from the call log if needed
    }
    
    /**
     * Get contact name for phone number
     */
    private fun getContactName(phoneNumber: String): String? {
        return try {
            val context = this.context ?: return null
            val uri = Uri.withAppendedPath(
                android.provider.ContactsContract.PhoneLookup.CONTENT_FILTER_URI,
                Uri.encode(phoneNumber)
            )
            
            val cursor = context.contentResolver.query(
                uri,
                arrayOf(android.provider.ContactsContract.PhoneLookup.DISPLAY_NAME),
                null,
                null,
                null
            )
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex(android.provider.ContactsContract.PhoneLookup.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        return it.getString(nameIndex)
                    }
                }
            }
            
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting contact name", e)
            null
        }
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopListening()
        serviceScope.cancel()
        context = null
        onCallDetected = null
        onCallEnded = null
    }
}
