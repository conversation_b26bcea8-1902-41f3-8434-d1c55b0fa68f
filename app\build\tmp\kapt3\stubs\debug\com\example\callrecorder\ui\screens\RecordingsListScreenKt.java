package com.example.callrecorder.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001aP\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\u001a\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"EmptyRecordingsState", "", "RecordingItem", "recording", "Lcom/example/callrecorder/data/CallRecordingDisplay;", "fileManager", "Lcom/example/callrecorder/storage/FileManager;", "onPlay", "Lkotlin/Function0;", "onShare", "onDelete", "onViewTranscript", "RecordingsListScreen", "viewModel", "Lcom/example/callrecorder/ui/viewmodel/CallRecordingsViewModel;", "modifier", "Landroidx/compose/ui/Modifier;", "app_debug"})
public final class RecordingsListScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void RecordingsListScreen(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EmptyRecordingsState() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RecordingItem(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecordingDisplay recording, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.storage.FileManager fileManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPlay, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShare, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onViewTranscript) {
    }
}