package com.example.callrecorder.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for CallRecording entity
 */
@Dao
interface CallRecordingDao {
    
    @Query("SELECT * FROM call_recordings ORDER BY callDate DESC")
    fun getAllRecordings(): Flow<List<CallRecording>>
    
    @Query("SELECT * FROM call_recordings WHERE id = :id")
    suspend fun getRecordingById(id: Long): CallRecording?
    
    @Query("SELECT * FROM call_recordings WHERE phoneNumber = :phoneNumber ORDER BY callDate DESC")
    fun getRecordingsByPhoneNumber(phoneNumber: String): Flow<List<CallRecording>>
    
    @Query("SELECT * FROM call_recordings WHERE callType = :callType ORDER BY callDate DESC")
    fun getRecordingsByType(callType: CallType): Flow<List<CallRecording>>
    
    @Query("SELECT * FROM call_recordings WHERE callDate BETWEEN :startDate AND :endDate ORDER BY callDate DESC")
    fun getRecordingsByDateRange(startDate: Date, endDate: Date): Flow<List<CallRecording>>
    
    @Query("SELECT * FROM call_recordings WHERE isTranscribed = :isTranscribed ORDER BY callDate DESC")
    fun getRecordingsByTranscriptionStatus(isTranscribed: Boolean): Flow<List<CallRecording>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecording(recording: CallRecording): Long
    
    @Update
    suspend fun updateRecording(recording: CallRecording)
    
    @Delete
    suspend fun deleteRecording(recording: CallRecording)
    
    @Query("DELETE FROM call_recordings WHERE id = :id")
    suspend fun deleteRecordingById(id: Long)
    
    @Query("DELETE FROM call_recordings")
    suspend fun deleteAllRecordings()
    
    @Query("SELECT COUNT(*) FROM call_recordings")
    suspend fun getRecordingCount(): Int
    
    @Query("SELECT SUM(fileSize) FROM call_recordings")
    suspend fun getTotalFileSize(): Long?
    
    @Query("UPDATE call_recordings SET isTranscribed = :isTranscribed, transcriptPath = :transcriptPath, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateTranscriptionStatus(id: Long, isTranscribed: Boolean, transcriptPath: String?, updatedAt: Date)
}
