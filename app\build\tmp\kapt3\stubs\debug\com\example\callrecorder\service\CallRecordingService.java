package com.example.callrecorder.service;

/**
 * Foreground service for call recording using MediaProjection API
 *
 * Android Version Limitations:
 * - Android 10+ (API 29+): Cannot use AudioSource.VOICE_CALL, must use MediaProjection
 * - Android 11+ (API 30+): Additional privacy restrictions on audio capture
 * - Android 12+ (API 31+): Stricter background service limitations
 * - Android 13+ (API 33+): Runtime notification permission required
 * - Android 14+ (API 34+): Enhanced foreground service type restrictions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u000b\u0018\u0000 12\u00020\u0001:\u00011B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0002J\b\u0010\u0018\u001a\u00020\u0017H\u0002J\u0018\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0018\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0014\u0010 \u001a\u0004\u0018\u00010!2\b\u0010\"\u001a\u0004\u0018\u00010#H\u0016J\b\u0010$\u001a\u00020\u0017H\u0016J\b\u0010%\u001a\u00020\u0017H\u0016J\"\u0010&\u001a\u00020\'2\b\u0010\"\u001a\u0004\u0018\u00010#2\u0006\u0010(\u001a\u00020\'2\u0006\u0010)\u001a\u00020\'H\u0016J\u0010\u0010*\u001a\u00020\u00172\u0006\u0010+\u001a\u00020\u0006H\u0002J\b\u0010,\u001a\u00020\u0017H\u0002J,\u0010-\u001a\u00020\u00172\b\u0010.\u001a\u0004\u0018\u00010#2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\b\u0010/\u001a\u0004\u0018\u00010\u001bH\u0002J\b\u00100\u001a\u00020\u0017H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0018\u00010\u0014R\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/example/callrecorder/service/CallRecordingService;", "Landroid/app/Service;", "()V", "audioRecord", "Landroid/media/AudioRecord;", "currentRecordingFile", "Ljava/io/File;", "isRecording", "", "mediaProjection", "Landroid/media/projection/MediaProjection;", "recordingJob", "Lkotlinx/coroutines/Job;", "recordingStartTime", "", "repository", "Lcom/example/callrecorder/data/CallRecordingRepository;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "wakeLock", "Landroid/os/PowerManager$WakeLock;", "Landroid/os/PowerManager;", "acquireWakeLock", "", "createNotificationChannel", "createRecordingFile", "phoneNumber", "", "callType", "Lcom/example/callrecorder/data/CallType;", "createRecordingNotification", "Landroid/app/Notification;", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "onDestroy", "onStartCommand", "", "flags", "startId", "saveRecordingToDatabase", "file", "startAudioRecording", "startRecording", "mediaProjectionData", "contactName", "stopRecording", "Companion", "app_debug"})
public final class CallRecordingService extends android.app.Service {
    public static final int NOTIFICATION_ID = 1001;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_ID = "call_recording_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_RECORDING = "start_recording";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_RECORDING = "stop_recording";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_MEDIA_PROJECTION_DATA = "media_projection_data";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_PHONE_NUMBER = "phone_number";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_CALL_TYPE = "call_type";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_CONTACT_NAME = "contact_name";
    @org.jetbrains.annotations.Nullable()
    private android.media.projection.MediaProjection mediaProjection;
    @org.jetbrains.annotations.Nullable()
    private android.media.AudioRecord audioRecord;
    private boolean isRecording = false;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job recordingJob;
    @org.jetbrains.annotations.Nullable()
    private android.os.PowerManager.WakeLock wakeLock;
    @org.jetbrains.annotations.Nullable()
    private java.io.File currentRecordingFile;
    private long recordingStartTime = 0L;
    private com.example.callrecorder.data.CallRecordingRepository repository;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.service.CallRecordingService.Companion Companion = null;
    
    public CallRecordingService() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    private final void createNotificationChannel() {
    }
    
    private final void acquireWakeLock() {
    }
    
    private final void startRecording(android.content.Intent mediaProjectionData, java.lang.String phoneNumber, com.example.callrecorder.data.CallType callType, java.lang.String contactName) {
    }
    
    private final java.io.File createRecordingFile(java.lang.String phoneNumber, com.example.callrecorder.data.CallType callType) {
        return null;
    }
    
    private final android.app.Notification createRecordingNotification(java.lang.String phoneNumber, com.example.callrecorder.data.CallType callType) {
        return null;
    }
    
    private final void startAudioRecording() {
    }
    
    private final void stopRecording() {
    }
    
    private final void saveRecordingToDatabase(java.io.File file) {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/callrecorder/service/CallRecordingService$Companion;", "", "()V", "ACTION_START_RECORDING", "", "ACTION_STOP_RECORDING", "CHANNEL_ID", "EXTRA_CALL_TYPE", "EXTRA_CONTACT_NAME", "EXTRA_MEDIA_PROJECTION_DATA", "EXTRA_PHONE_NUMBER", "NOTIFICATION_ID", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}