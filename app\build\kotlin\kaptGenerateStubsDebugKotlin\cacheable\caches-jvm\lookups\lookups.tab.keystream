  Manifest android  
permission android.Manifest  ACCESS_NETWORK_STATE android.Manifest.permission  FOREGROUND_SERVICE android.Manifest.permission  INTERNET android.Manifest.permission  
READ_CALL_LOG android.Manifest.permission  
READ_CONTACTS android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  	WAKE_LOCK android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  AccessibilityService android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  Activity android.app  Application android.app  CoroutineScope android.app  Dispatchers android.app  Job android.app  Notification android.app  Service android.app  
SupervisorJob android.app  Bundle android.app.Activity  CallDetectionService android.app.Activity  CallType android.app.Activity  String android.app.Activity  AccessibilityEvent android.app.Service  AudioRecord android.app.Service  Boolean android.app.Service  CallRecordingRepository android.app.Service  CallType android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  File android.app.Service  FileOutputStream android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  Job android.app.Service  Long android.app.Service  MediaProjection android.app.Service  Notification android.app.Service  PowerManager android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  BroadcastReceiver android.content  Context android.content  Intent android.content  IntentFilter android.content  AccessibilityEvent android.content.Context  AudioRecord android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CallDetectionService android.content.Context  CallRecordingRepository android.content.Context  CallType android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  File android.content.Context  FileOutputStream android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  Job android.content.Context  Long android.content.Context  MediaProjection android.content.Context  Notification android.content.Context  PowerManager android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  AccessibilityEvent android.content.ContextWrapper  AudioRecord android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  CallDetectionService android.content.ContextWrapper  CallRecordingRepository android.content.ContextWrapper  CallType android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  File android.content.ContextWrapper  FileOutputStream android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Job android.content.ContextWrapper  Long android.content.ContextWrapper  MediaProjection android.content.ContextWrapper  Notification android.content.ContextWrapper  PowerManager android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  PackageManager android.content.pm  ContentObserver android.database  AudioFormat 
android.media  AudioManager 
android.media  AudioRecord 
android.media  MediaPlayer 
android.media  
MediaRecorder 
android.media  MODE_NORMAL android.media.AudioManager  OnAudioFocusChangeListener android.media.AudioManager  MediaProjection android.media.projection  MediaProjectionManager android.media.projection  Uri android.net  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  WakeLock android.os.PowerManager  CallLog android.provider  Settings android.provider  PhoneStateListener android.telephony  TelephonyManager android.telephony  CALL_STATE_IDLE "android.telephony.TelephonyManager  Log android.util  Bundle  android.view.ContextThemeWrapper  CallDetectionService  android.view.ContextThemeWrapper  CallType  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  AccessibilityEvent android.view.accessibility  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  CallDetectionService #androidx.activity.ComponentActivity  CallType #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  ActivityResultContracts !androidx.activity.result.contract  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Icons androidx.compose.material.icons  Check &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  Button androidx.compose.material3  
Composable androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  
Composable androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  
FontWeight androidx.compose.ui.text.font  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  dp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  CallDetectionService #androidx.core.app.ComponentActivity  CallType #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Activity #androidx.lifecycle.AndroidViewModel  ActivityResultLauncher #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  Intent #androidx.lifecycle.AndroidViewModel  MediaProjection #androidx.lifecycle.AndroidViewModel  MediaProjectionManager #androidx.lifecycle.AndroidViewModel  Activity androidx.lifecycle.ViewModel  ActivityResultLauncher androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CallRecordingDisplay androidx.lifecycle.ViewModel  CallRecordingRepository androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  FileManager androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Intent androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MediaPlayer androidx.lifecycle.ViewModel  MediaProjection androidx.lifecycle.ViewModel  MediaProjectionManager androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  CallRecordingDao androidx.room.RoomDatabase  CallRecordingDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  MainActivity com.example.callrecorder  R com.example.callrecorder  String com.example.callrecorder  Bundle %com.example.callrecorder.MainActivity  CallDetectionService %com.example.callrecorder.MainActivity  CallType %com.example.callrecorder.MainActivity  String %com.example.callrecorder.MainActivity  Boolean com.example.callrecorder.ai  CostEstimate com.example.callrecorder.ai  Double com.example.callrecorder.ai  Int com.example.callrecorder.ai  List com.example.callrecorder.ai  Long com.example.callrecorder.ai  	Multipart com.example.callrecorder.ai  
MultipartBody com.example.callrecorder.ai  OpenAIApiService com.example.callrecorder.ai  
OpenAIService com.example.callrecorder.ai  POST com.example.callrecorder.ai  Part com.example.callrecorder.ai  RequestBody com.example.callrecorder.ai  String com.example.callrecorder.ai  TranscriptionManager com.example.callrecorder.ai  TranscriptionQueueStatus com.example.callrecorder.ai  TranscriptionResponse com.example.callrecorder.ai  TranscriptionResult com.example.callrecorder.ai  com com.example.callrecorder.ai  
mutableListOf com.example.callrecorder.ai  Double (com.example.callrecorder.ai.CostEstimate  String (com.example.callrecorder.ai.CostEstimate  	Multipart ,com.example.callrecorder.ai.OpenAIApiService  
MultipartBody ,com.example.callrecorder.ai.OpenAIApiService  POST ,com.example.callrecorder.ai.OpenAIApiService  Part ,com.example.callrecorder.ai.OpenAIApiService  RequestBody ,com.example.callrecorder.ai.OpenAIApiService  Response ,com.example.callrecorder.ai.OpenAIApiService  TranscriptionResponse ,com.example.callrecorder.ai.OpenAIApiService  Boolean )com.example.callrecorder.ai.OpenAIService  Context )com.example.callrecorder.ai.OpenAIService  CostEstimate )com.example.callrecorder.ai.OpenAIService  File )com.example.callrecorder.ai.OpenAIService  List )com.example.callrecorder.ai.OpenAIService  OpenAIApiService )com.example.callrecorder.ai.OpenAIService  String )com.example.callrecorder.ai.OpenAIService  TranscriptionResult )com.example.callrecorder.ai.OpenAIService  Boolean 3com.example.callrecorder.ai.OpenAIService.Companion  Context 3com.example.callrecorder.ai.OpenAIService.Companion  CostEstimate 3com.example.callrecorder.ai.OpenAIService.Companion  File 3com.example.callrecorder.ai.OpenAIService.Companion  List 3com.example.callrecorder.ai.OpenAIService.Companion  OpenAIApiService 3com.example.callrecorder.ai.OpenAIService.Companion  String 3com.example.callrecorder.ai.OpenAIService.Companion  TranscriptionResult 3com.example.callrecorder.ai.OpenAIService.Companion  CallRecordingRepository 0com.example.callrecorder.ai.TranscriptionManager  Context 0com.example.callrecorder.ai.TranscriptionManager  Long 0com.example.callrecorder.ai.TranscriptionManager  
OpenAIService 0com.example.callrecorder.ai.TranscriptionManager  TranscriptionQueueStatus 0com.example.callrecorder.ai.TranscriptionManager  com 0com.example.callrecorder.ai.TranscriptionManager  getMUTABLEListOf 0com.example.callrecorder.ai.TranscriptionManager  getMutableListOf 0com.example.callrecorder.ai.TranscriptionManager  
mutableListOf 0com.example.callrecorder.ai.TranscriptionManager  Boolean 4com.example.callrecorder.ai.TranscriptionQueueStatus  Int 4com.example.callrecorder.ai.TranscriptionQueueStatus  Double 1com.example.callrecorder.ai.TranscriptionResponse  SerializedName 1com.example.callrecorder.ai.TranscriptionResponse  String 1com.example.callrecorder.ai.TranscriptionResponse  Double /com.example.callrecorder.ai.TranscriptionResult  String /com.example.callrecorder.ai.TranscriptionResult  TranscriptionResult /com.example.callrecorder.ai.TranscriptionResult  String 5com.example.callrecorder.ai.TranscriptionResult.Error  Double 7com.example.callrecorder.ai.TranscriptionResult.Success  String 7com.example.callrecorder.ai.TranscriptionResult.Success  AudioPlayerManager com.example.callrecorder.audio  Boolean com.example.callrecorder.audio  Int com.example.callrecorder.audio  MutableStateFlow com.example.callrecorder.audio  
PlaybackState com.example.callrecorder.audio  Runnable com.example.callrecorder.audio  String com.example.callrecorder.audio  asStateFlow com.example.callrecorder.audio  
formatTime com.example.callrecorder.audio  Boolean 1com.example.callrecorder.audio.AudioPlayerManager  Context 1com.example.callrecorder.audio.AudioPlayerManager  File 1com.example.callrecorder.audio.AudioPlayerManager  Int 1com.example.callrecorder.audio.AudioPlayerManager  MediaPlayer 1com.example.callrecorder.audio.AudioPlayerManager  MutableStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  
PlaybackState 1com.example.callrecorder.audio.AudioPlayerManager  Runnable 1com.example.callrecorder.audio.AudioPlayerManager  	StateFlow 1com.example.callrecorder.audio.AudioPlayerManager  String 1com.example.callrecorder.audio.AudioPlayerManager  Uri 1com.example.callrecorder.audio.AudioPlayerManager  _currentFile 1com.example.callrecorder.audio.AudioPlayerManager  _currentPosition 1com.example.callrecorder.audio.AudioPlayerManager  	_duration 1com.example.callrecorder.audio.AudioPlayerManager  _playbackState 1com.example.callrecorder.audio.AudioPlayerManager  asStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  getASStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  getAsStateFlow 1com.example.callrecorder.audio.AudioPlayerManager  IDLE ,com.example.callrecorder.audio.PlaybackState  Boolean com.example.callrecorder.data  
CallRecording com.example.callrecorder.data  CallRecordingDao com.example.callrecorder.data  CallRecordingDatabase com.example.callrecorder.data  CallRecordingDisplay com.example.callrecorder.data  CallRecordingRepository com.example.callrecorder.data  CallType com.example.callrecorder.data  
Converters com.example.callrecorder.data  Dao com.example.callrecorder.data  Date com.example.callrecorder.data  Delete com.example.callrecorder.data  Dispatchers com.example.callrecorder.data  InMemoryCallRecordingRepository com.example.callrecorder.data  Insert com.example.callrecorder.data  Int com.example.callrecorder.data  List com.example.callrecorder.data  Long com.example.callrecorder.data  OnConflictStrategy com.example.callrecorder.data  Query com.example.callrecorder.data  String com.example.callrecorder.data  Unit com.example.callrecorder.data  Update com.example.callrecorder.data  Volatile com.example.callrecorder.data  indexOfFirst com.example.callrecorder.data  
mutableListOf com.example.callrecorder.data  
recordings com.example.callrecorder.data  	removeAll com.example.callrecorder.data  withContext com.example.callrecorder.data  Boolean +com.example.callrecorder.data.CallRecording  CallType +com.example.callrecorder.data.CallRecording  Date +com.example.callrecorder.data.CallRecording  Long +com.example.callrecorder.data.CallRecording  
PrimaryKey +com.example.callrecorder.data.CallRecording  String +com.example.callrecorder.data.CallRecording  callDate +com.example.callrecorder.data.CallRecording  callType +com.example.callrecorder.data.CallRecording  contactName +com.example.callrecorder.data.CallRecording  	createdAt +com.example.callrecorder.data.CallRecording  duration +com.example.callrecorder.data.CallRecording  filePath +com.example.callrecorder.data.CallRecording  fileSize +com.example.callrecorder.data.CallRecording  id +com.example.callrecorder.data.CallRecording  phoneNumber +com.example.callrecorder.data.CallRecording  recordingDuration +com.example.callrecorder.data.CallRecording  Boolean .com.example.callrecorder.data.CallRecordingDao  
CallRecording .com.example.callrecorder.data.CallRecordingDao  CallType .com.example.callrecorder.data.CallRecordingDao  Date .com.example.callrecorder.data.CallRecordingDao  Delete .com.example.callrecorder.data.CallRecordingDao  Flow .com.example.callrecorder.data.CallRecordingDao  Insert .com.example.callrecorder.data.CallRecordingDao  Int .com.example.callrecorder.data.CallRecordingDao  List .com.example.callrecorder.data.CallRecordingDao  Long .com.example.callrecorder.data.CallRecordingDao  OnConflictStrategy .com.example.callrecorder.data.CallRecordingDao  Query .com.example.callrecorder.data.CallRecordingDao  String .com.example.callrecorder.data.CallRecordingDao  Update .com.example.callrecorder.data.CallRecordingDao  CallRecordingDao 3com.example.callrecorder.data.CallRecordingDatabase  CallRecordingDatabase 3com.example.callrecorder.data.CallRecordingDatabase  Context 3com.example.callrecorder.data.CallRecordingDatabase  Volatile 3com.example.callrecorder.data.CallRecordingDatabase  CallRecordingDao =com.example.callrecorder.data.CallRecordingDatabase.Companion  CallRecordingDatabase =com.example.callrecorder.data.CallRecordingDatabase.Companion  Context =com.example.callrecorder.data.CallRecordingDatabase.Companion  Volatile =com.example.callrecorder.data.CallRecordingDatabase.Companion  Boolean 2com.example.callrecorder.data.CallRecordingDisplay  CallType 2com.example.callrecorder.data.CallRecordingDisplay  Date 2com.example.callrecorder.data.CallRecordingDisplay  Long 2com.example.callrecorder.data.CallRecordingDisplay  String 2com.example.callrecorder.data.CallRecordingDisplay  Boolean 5com.example.callrecorder.data.CallRecordingRepository  
CallRecording 5com.example.callrecorder.data.CallRecordingRepository  CallRecordingDisplay 5com.example.callrecorder.data.CallRecordingRepository  Context 5com.example.callrecorder.data.CallRecordingRepository  Flow 5com.example.callrecorder.data.CallRecordingRepository  List 5com.example.callrecorder.data.CallRecordingRepository  Long 5com.example.callrecorder.data.CallRecordingRepository  String 5com.example.callrecorder.data.CallRecordingRepository  CallType (com.example.callrecorder.data.Converters  Date (com.example.callrecorder.data.Converters  Long (com.example.callrecorder.data.Converters  String (com.example.callrecorder.data.Converters  
TypeConverter (com.example.callrecorder.data.Converters  Boolean =com.example.callrecorder.data.InMemoryCallRecordingRepository  
CallRecording =com.example.callrecorder.data.InMemoryCallRecordingRepository  CallRecordingDisplay =com.example.callrecorder.data.InMemoryCallRecordingRepository  Date =com.example.callrecorder.data.InMemoryCallRecordingRepository  Dispatchers =com.example.callrecorder.data.InMemoryCallRecordingRepository  Flow =com.example.callrecorder.data.InMemoryCallRecordingRepository  List =com.example.callrecorder.data.InMemoryCallRecordingRepository  Long =com.example.callrecorder.data.InMemoryCallRecordingRepository  String =com.example.callrecorder.data.InMemoryCallRecordingRepository  Unit =com.example.callrecorder.data.InMemoryCallRecordingRepository  getINDEXOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getIndexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMUTABLEListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getMutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  getREMOVEAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  getRemoveAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWITHContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  getWithContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  indexOfFirst =com.example.callrecorder.data.InMemoryCallRecordingRepository  
mutableListOf =com.example.callrecorder.data.InMemoryCallRecordingRepository  
recordings =com.example.callrecorder.data.InMemoryCallRecordingRepository  	removeAll =com.example.callrecorder.data.InMemoryCallRecordingRepository  withContext =com.example.callrecorder.data.InMemoryCallRecordingRepository  
CallRecording com.example.callrecorder.domain  Long com.example.callrecorder.domain  String com.example.callrecorder.domain  Long -com.example.callrecorder.domain.CallRecording  String -com.example.callrecorder.domain.CallRecording  Array #com.example.callrecorder.permission  Boolean #com.example.callrecorder.permission  Int #com.example.callrecorder.permission  IntArray #com.example.callrecorder.permission  List #com.example.callrecorder.permission  Manifest #com.example.callrecorder.permission  PermissionManager #com.example.callrecorder.permission  String #com.example.callrecorder.permission  Unit #com.example.callrecorder.permission  arrayOf #com.example.callrecorder.permission  Activity 5com.example.callrecorder.permission.PermissionManager  Array 5com.example.callrecorder.permission.PermissionManager  Boolean 5com.example.callrecorder.permission.PermissionManager  Context 5com.example.callrecorder.permission.PermissionManager  Int 5com.example.callrecorder.permission.PermissionManager  IntArray 5com.example.callrecorder.permission.PermissionManager  List 5com.example.callrecorder.permission.PermissionManager  Manifest 5com.example.callrecorder.permission.PermissionManager  String 5com.example.callrecorder.permission.PermissionManager  Unit 5com.example.callrecorder.permission.PermissionManager  arrayOf 5com.example.callrecorder.permission.PermissionManager  Activity ?com.example.callrecorder.permission.PermissionManager.Companion  Array ?com.example.callrecorder.permission.PermissionManager.Companion  Boolean ?com.example.callrecorder.permission.PermissionManager.Companion  Context ?com.example.callrecorder.permission.PermissionManager.Companion  Int ?com.example.callrecorder.permission.PermissionManager.Companion  IntArray ?com.example.callrecorder.permission.PermissionManager.Companion  List ?com.example.callrecorder.permission.PermissionManager.Companion  Manifest ?com.example.callrecorder.permission.PermissionManager.Companion  String ?com.example.callrecorder.permission.PermissionManager.Companion  Unit ?com.example.callrecorder.permission.PermissionManager.Companion  arrayOf ?com.example.callrecorder.permission.PermissionManager.Companion  
getARRAYOf ?com.example.callrecorder.permission.PermissionManager.Companion  
getArrayOf ?com.example.callrecorder.permission.PermissionManager.Companion  AudioManager  com.example.callrecorder.service  Boolean  com.example.callrecorder.service  CallAccessibilityService  com.example.callrecorder.service  CallDetectionService  com.example.callrecorder.service  CallRecordingService  com.example.callrecorder.service  CoroutineScope  com.example.callrecorder.service  Dispatchers  com.example.callrecorder.service  Int  com.example.callrecorder.service  Job  com.example.callrecorder.service  Long  com.example.callrecorder.service  MediaProjectionHelper  com.example.callrecorder.service  Notification  com.example.callrecorder.service  Runnable  com.example.callrecorder.service  Service  com.example.callrecorder.service  String  com.example.callrecorder.service  
SupervisorJob  com.example.callrecorder.service  TelephonyManager  com.example.callrecorder.service  Unit  com.example.callrecorder.service  AccessibilityEvent 9com.example.callrecorder.service.CallAccessibilityService  AudioManager 5com.example.callrecorder.service.CallDetectionService  BroadcastReceiver 5com.example.callrecorder.service.CallDetectionService  CallType 5com.example.callrecorder.service.CallDetectionService  ContentObserver 5com.example.callrecorder.service.CallDetectionService  Context 5com.example.callrecorder.service.CallDetectionService  CoroutineScope 5com.example.callrecorder.service.CallDetectionService  Dispatchers 5com.example.callrecorder.service.CallDetectionService  Handler 5com.example.callrecorder.service.CallDetectionService  Int 5com.example.callrecorder.service.CallDetectionService  Long 5com.example.callrecorder.service.CallDetectionService  PhoneStateListener 5com.example.callrecorder.service.CallDetectionService  Runnable 5com.example.callrecorder.service.CallDetectionService  String 5com.example.callrecorder.service.CallDetectionService  
SupervisorJob 5com.example.callrecorder.service.CallDetectionService  TelephonyManager 5com.example.callrecorder.service.CallDetectionService  Unit 5com.example.callrecorder.service.CallDetectionService  AudioManager ?com.example.callrecorder.service.CallDetectionService.Companion  BroadcastReceiver ?com.example.callrecorder.service.CallDetectionService.Companion  CallType ?com.example.callrecorder.service.CallDetectionService.Companion  ContentObserver ?com.example.callrecorder.service.CallDetectionService.Companion  Context ?com.example.callrecorder.service.CallDetectionService.Companion  CoroutineScope ?com.example.callrecorder.service.CallDetectionService.Companion  Dispatchers ?com.example.callrecorder.service.CallDetectionService.Companion  Handler ?com.example.callrecorder.service.CallDetectionService.Companion  Int ?com.example.callrecorder.service.CallDetectionService.Companion  Long ?com.example.callrecorder.service.CallDetectionService.Companion  PhoneStateListener ?com.example.callrecorder.service.CallDetectionService.Companion  Runnable ?com.example.callrecorder.service.CallDetectionService.Companion  String ?com.example.callrecorder.service.CallDetectionService.Companion  
SupervisorJob ?com.example.callrecorder.service.CallDetectionService.Companion  TelephonyManager ?com.example.callrecorder.service.CallDetectionService.Companion  Unit ?com.example.callrecorder.service.CallDetectionService.Companion  AudioRecord 5com.example.callrecorder.service.CallRecordingService  Boolean 5com.example.callrecorder.service.CallRecordingService  CallRecordingRepository 5com.example.callrecorder.service.CallRecordingService  CallType 5com.example.callrecorder.service.CallRecordingService  CoroutineScope 5com.example.callrecorder.service.CallRecordingService  Dispatchers 5com.example.callrecorder.service.CallRecordingService  File 5com.example.callrecorder.service.CallRecordingService  FileOutputStream 5com.example.callrecorder.service.CallRecordingService  IBinder 5com.example.callrecorder.service.CallRecordingService  Int 5com.example.callrecorder.service.CallRecordingService  Intent 5com.example.callrecorder.service.CallRecordingService  Job 5com.example.callrecorder.service.CallRecordingService  Long 5com.example.callrecorder.service.CallRecordingService  MediaProjection 5com.example.callrecorder.service.CallRecordingService  Notification 5com.example.callrecorder.service.CallRecordingService  PowerManager 5com.example.callrecorder.service.CallRecordingService  String 5com.example.callrecorder.service.CallRecordingService  
SupervisorJob 5com.example.callrecorder.service.CallRecordingService  AudioRecord ?com.example.callrecorder.service.CallRecordingService.Companion  Boolean ?com.example.callrecorder.service.CallRecordingService.Companion  CallRecordingRepository ?com.example.callrecorder.service.CallRecordingService.Companion  CallType ?com.example.callrecorder.service.CallRecordingService.Companion  CoroutineScope ?com.example.callrecorder.service.CallRecordingService.Companion  Dispatchers ?com.example.callrecorder.service.CallRecordingService.Companion  File ?com.example.callrecorder.service.CallRecordingService.Companion  FileOutputStream ?com.example.callrecorder.service.CallRecordingService.Companion  IBinder ?com.example.callrecorder.service.CallRecordingService.Companion  Int ?com.example.callrecorder.service.CallRecordingService.Companion  Intent ?com.example.callrecorder.service.CallRecordingService.Companion  Job ?com.example.callrecorder.service.CallRecordingService.Companion  Long ?com.example.callrecorder.service.CallRecordingService.Companion  MediaProjection ?com.example.callrecorder.service.CallRecordingService.Companion  Notification ?com.example.callrecorder.service.CallRecordingService.Companion  PowerManager ?com.example.callrecorder.service.CallRecordingService.Companion  String ?com.example.callrecorder.service.CallRecordingService.Companion  
SupervisorJob ?com.example.callrecorder.service.CallRecordingService.Companion  Activity 6com.example.callrecorder.service.MediaProjectionHelper  ActivityResultLauncher 6com.example.callrecorder.service.MediaProjectionHelper  Application 6com.example.callrecorder.service.MediaProjectionHelper  Int 6com.example.callrecorder.service.MediaProjectionHelper  Intent 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjection 6com.example.callrecorder.service.MediaProjectionHelper  MediaProjectionManager 6com.example.callrecorder.service.MediaProjectionHelper  Boolean  com.example.callrecorder.storage  FileManager  com.example.callrecorder.storage  Int  com.example.callrecorder.storage  Long  com.example.callrecorder.storage  String  com.example.callrecorder.storage  Boolean ,com.example.callrecorder.storage.FileManager  CallType ,com.example.callrecorder.storage.FileManager  Context ,com.example.callrecorder.storage.FileManager  File ,com.example.callrecorder.storage.FileManager  Int ,com.example.callrecorder.storage.FileManager  Intent ,com.example.callrecorder.storage.FileManager  Long ,com.example.callrecorder.storage.FileManager  String ,com.example.callrecorder.storage.FileManager  Uri ,com.example.callrecorder.storage.FileManager  Boolean 6com.example.callrecorder.storage.FileManager.Companion  CallType 6com.example.callrecorder.storage.FileManager.Companion  Context 6com.example.callrecorder.storage.FileManager.Companion  File 6com.example.callrecorder.storage.FileManager.Companion  Int 6com.example.callrecorder.storage.FileManager.Companion  Intent 6com.example.callrecorder.storage.FileManager.Companion  Long 6com.example.callrecorder.storage.FileManager.Companion  String 6com.example.callrecorder.storage.FileManager.Companion  Uri 6com.example.callrecorder.storage.FileManager.Companion  CallRecorderApp com.example.callrecorder.ui  
Composable com.example.callrecorder.ui  
ConsentScreen com.example.callrecorder.ui  
MainScreen com.example.callrecorder.ui  PermissionAndConsentScreen com.example.callrecorder.ui  PlaybackControls com.example.callrecorder.ui  PreviewCallRecorderApp com.example.callrecorder.ui  PrivacyPolicyScreen com.example.callrecorder.ui  StartRecordingButton com.example.callrecorder.ui  Unit com.example.callrecorder.ui  Boolean #com.example.callrecorder.ui.dialogs  
Composable #com.example.callrecorder.ui.dialogs  
ConsentDialog #com.example.callrecorder.ui.dialogs  ConsentDialogContent #com.example.callrecorder.ui.dialogs  QuickConsentDialog #com.example.callrecorder.ui.dialogs  String #com.example.callrecorder.ui.dialogs  Unit #com.example.callrecorder.ui.dialogs  Boolean #com.example.callrecorder.ui.screens  
Composable #com.example.callrecorder.ui.screens  EmptyRecordingsState #com.example.callrecorder.ui.screens  Map #com.example.callrecorder.ui.screens  PermissionItem #com.example.callrecorder.ui.screens  PermissionScreen #com.example.callrecorder.ui.screens  RecordingControlScreen #com.example.callrecorder.ui.screens  
RecordingItem #com.example.callrecorder.ui.screens  RecordingsListScreen #com.example.callrecorder.ui.screens  SpecialPermissionItem #com.example.callrecorder.ui.screens  String #com.example.callrecorder.ui.screens  Unit #com.example.callrecorder.ui.screens  checkAllPermissions #com.example.callrecorder.ui.screens  startRecordingService #com.example.callrecorder.ui.screens  startWhatsAppRecording #com.example.callrecorder.ui.screens  stopRecordingService #com.example.callrecorder.ui.screens  Boolean %com.example.callrecorder.ui.viewmodel  CallRecordingsViewModel %com.example.callrecorder.ui.viewmodel  List %com.example.callrecorder.ui.viewmodel  MutableStateFlow %com.example.callrecorder.ui.viewmodel  	StateFlow %com.example.callrecorder.ui.viewmodel  String %com.example.callrecorder.ui.viewmodel  asStateFlow %com.example.callrecorder.ui.viewmodel  	emptyList %com.example.callrecorder.ui.viewmodel  Boolean =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  CallRecordingDisplay =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  CallRecordingRepository =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Context =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  FileManager =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  List =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  MediaPlayer =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  MutableStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	StateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  String =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
_errorMessage =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  
_isLoading =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  _recordings =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  asStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  	emptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getASStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getAsStateFlow =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEMPTYList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  getEmptyList =com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel  Boolean com.example.callrecorder.util  ErrorHandler com.example.callrecorder.util  ErrorResult com.example.callrecorder.util  	ErrorType com.example.callrecorder.util  	Exception com.example.callrecorder.util  List com.example.callrecorder.util  RecordingServiceStarter com.example.callrecorder.util  String com.example.callrecorder.util  Context *com.example.callrecorder.util.ErrorHandler  ErrorResult *com.example.callrecorder.util.ErrorHandler  	Exception *com.example.callrecorder.util.ErrorHandler  List *com.example.callrecorder.util.ErrorHandler  String *com.example.callrecorder.util.ErrorHandler  Context 4com.example.callrecorder.util.ErrorHandler.Companion  ErrorResult 4com.example.callrecorder.util.ErrorHandler.Companion  	Exception 4com.example.callrecorder.util.ErrorHandler.Companion  List 4com.example.callrecorder.util.ErrorHandler.Companion  String 4com.example.callrecorder.util.ErrorHandler.Companion  Boolean )com.example.callrecorder.util.ErrorResult  	ErrorType )com.example.callrecorder.util.ErrorResult  String )com.example.callrecorder.util.ErrorResult  Context 5com.example.callrecorder.util.RecordingServiceStarter  Intent 5com.example.callrecorder.util.RecordingServiceStarter  SerializedName com.google.gson.annotations  File java.io  FileOutputStream java.io  IOException java.io  AudioManager 	java.lang  
CallRecording 	java.lang  
Converters 	java.lang  CoroutineScope 	java.lang  Date 	java.lang  Dispatchers 	java.lang  Manifest 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  
PlaybackState 	java.lang  Runnable 	java.lang  
SupervisorJob 	java.lang  TelephonyManager 	java.lang  Unit 	java.lang  arrayOf 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  indexOfFirst 	java.lang  
mutableListOf 	java.lang  
recordings 	java.lang  	removeAll 	java.lang  withContext 	java.lang  SimpleDateFormat 	java.text  
Composable 	java.util  CoroutineScope 	java.util  Date 	java.util  Dispatchers 	java.util  Job 	java.util  Notification 	java.util  Service 	java.util  
SupervisorJob 	java.util  TimeUnit java.util.concurrent  Array kotlin  AudioManager kotlin  Boolean kotlin  
CallRecording kotlin  
Converters kotlin  CoroutineScope kotlin  Date kotlin  Dispatchers kotlin  Double kotlin  	Exception kotlin  	Function1 kotlin  Int kotlin  IntArray kotlin  Long kotlin  Manifest kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  
PlaybackState kotlin  Runnable kotlin  String kotlin  
SupervisorJob kotlin  TelephonyManager kotlin  Unit kotlin  Volatile kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  indexOfFirst kotlin  
mutableListOf kotlin  
recordings kotlin  	removeAll kotlin  withContext kotlin  AudioManager kotlin.annotation  
CallRecording kotlin.annotation  
Converters kotlin.annotation  CoroutineScope kotlin.annotation  Date kotlin.annotation  Dispatchers kotlin.annotation  	Exception kotlin.annotation  Manifest kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  
PlaybackState kotlin.annotation  Runnable kotlin.annotation  
SupervisorJob kotlin.annotation  TelephonyManager kotlin.annotation  Unit kotlin.annotation  Volatile kotlin.annotation  arrayOf kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  indexOfFirst kotlin.annotation  
mutableListOf kotlin.annotation  
recordings kotlin.annotation  	removeAll kotlin.annotation  withContext kotlin.annotation  AudioManager kotlin.collections  
CallRecording kotlin.collections  
Converters kotlin.collections  CoroutineScope kotlin.collections  Date kotlin.collections  Dispatchers kotlin.collections  	Exception kotlin.collections  List kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  
PlaybackState kotlin.collections  Runnable kotlin.collections  
SupervisorJob kotlin.collections  TelephonyManager kotlin.collections  Unit kotlin.collections  Volatile kotlin.collections  arrayOf kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  indexOfFirst kotlin.collections  
mutableListOf kotlin.collections  
recordings kotlin.collections  	removeAll kotlin.collections  withContext kotlin.collections  getINDEXOfFirst kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  AudioManager kotlin.comparisons  
CallRecording kotlin.comparisons  
Converters kotlin.comparisons  CoroutineScope kotlin.comparisons  Date kotlin.comparisons  Dispatchers kotlin.comparisons  	Exception kotlin.comparisons  Manifest kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  
PlaybackState kotlin.comparisons  Runnable kotlin.comparisons  
SupervisorJob kotlin.comparisons  TelephonyManager kotlin.comparisons  Unit kotlin.comparisons  Volatile kotlin.comparisons  arrayOf kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  indexOfFirst kotlin.comparisons  
mutableListOf kotlin.comparisons  
recordings kotlin.comparisons  	removeAll kotlin.comparisons  withContext kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  AudioManager 	kotlin.io  
CallRecording 	kotlin.io  
Converters 	kotlin.io  CoroutineScope 	kotlin.io  Date 	kotlin.io  Dispatchers 	kotlin.io  	Exception 	kotlin.io  Manifest 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  
PlaybackState 	kotlin.io  Runnable 	kotlin.io  
SupervisorJob 	kotlin.io  TelephonyManager 	kotlin.io  Unit 	kotlin.io  Volatile 	kotlin.io  arrayOf 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  indexOfFirst 	kotlin.io  
mutableListOf 	kotlin.io  
recordings 	kotlin.io  	removeAll 	kotlin.io  withContext 	kotlin.io  AudioManager 
kotlin.jvm  
CallRecording 
kotlin.jvm  
Converters 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Date 
kotlin.jvm  Dispatchers 
kotlin.jvm  	Exception 
kotlin.jvm  Manifest 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  
PlaybackState 
kotlin.jvm  Runnable 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  TelephonyManager 
kotlin.jvm  Unit 
kotlin.jvm  Volatile 
kotlin.jvm  arrayOf 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  indexOfFirst 
kotlin.jvm  
mutableListOf 
kotlin.jvm  
recordings 
kotlin.jvm  	removeAll 
kotlin.jvm  withContext 
kotlin.jvm  AudioManager 
kotlin.ranges  
CallRecording 
kotlin.ranges  
Converters 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Date 
kotlin.ranges  Dispatchers 
kotlin.ranges  	Exception 
kotlin.ranges  Manifest 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  
PlaybackState 
kotlin.ranges  Runnable 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  TelephonyManager 
kotlin.ranges  Unit 
kotlin.ranges  Volatile 
kotlin.ranges  arrayOf 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  indexOfFirst 
kotlin.ranges  
mutableListOf 
kotlin.ranges  
recordings 
kotlin.ranges  	removeAll 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  AudioManager kotlin.sequences  
CallRecording kotlin.sequences  
Converters kotlin.sequences  CoroutineScope kotlin.sequences  Date kotlin.sequences  Dispatchers kotlin.sequences  	Exception kotlin.sequences  Manifest kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  
PlaybackState kotlin.sequences  Runnable kotlin.sequences  
SupervisorJob kotlin.sequences  TelephonyManager kotlin.sequences  Unit kotlin.sequences  Volatile kotlin.sequences  arrayOf kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  indexOfFirst kotlin.sequences  
mutableListOf kotlin.sequences  
recordings kotlin.sequences  	removeAll kotlin.sequences  withContext kotlin.sequences  AudioManager kotlin.text  
CallRecording kotlin.text  
Converters kotlin.text  CoroutineScope kotlin.text  Date kotlin.text  Dispatchers kotlin.text  	Exception kotlin.text  Manifest kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  
PlaybackState kotlin.text  Runnable kotlin.text  
SupervisorJob kotlin.text  TelephonyManager kotlin.text  Unit kotlin.text  Volatile kotlin.text  arrayOf kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  indexOfFirst kotlin.text  
mutableListOf kotlin.text  
recordings kotlin.text  	removeAll kotlin.text  withContext kotlin.text  AudioManager kotlinx.coroutines  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Notification kotlinx.coroutines  Runnable kotlinx.coroutines  Service kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TelephonyManager kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  
CallRecording !kotlinx.coroutines.CoroutineScope  Date !kotlinx.coroutines.CoroutineScope  Unit !kotlinx.coroutines.CoroutineScope  getINDEXOfFirst !kotlinx.coroutines.CoroutineScope  getIndexOfFirst !kotlinx.coroutines.CoroutineScope  
getRECORDINGS !kotlinx.coroutines.CoroutineScope  getREMOVEAll !kotlinx.coroutines.CoroutineScope  
getRecordings !kotlinx.coroutines.CoroutineScope  getRemoveAll !kotlinx.coroutines.CoroutineScope  indexOfFirst !kotlinx.coroutines.CoroutineScope  
recordings !kotlinx.coroutines.CoroutineScope  	removeAll !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  	MediaType okhttp3  	Multipart okhttp3  
MultipartBody okhttp3  POST okhttp3  Part okhttp3  RequestBody okhttp3  com okhttp3  
mutableListOf okhttp3  	Companion okhttp3.MediaType  toMediaType okhttp3.MediaType.Companion  Part okhttp3.MultipartBody  	Companion okhttp3.RequestBody  
asRequestBody okhttp3.RequestBody.Companion  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  	Multipart retrofit2.http  
MultipartBody retrofit2.http  POST retrofit2.http  Part retrofit2.http  RequestBody retrofit2.http  com retrofit2.http  
mutableListOf retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        