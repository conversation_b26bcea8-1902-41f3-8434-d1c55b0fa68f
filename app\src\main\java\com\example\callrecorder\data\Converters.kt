package com.example.callrecorder.data

import androidx.room.TypeConverter
import java.util.Date

/**
 * Room type converters for custom data types
 */
class Converters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
    
    @TypeConverter
    fun fromCallType(callType: CallType): String {
        return callType.name
    }
    
    @TypeConverter
    fun toCallType(callType: String): CallType {
        return CallType.valueOf(callType)
    }
}
