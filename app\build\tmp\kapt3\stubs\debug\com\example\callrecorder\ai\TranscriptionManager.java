package com.example.callrecorder.ai;

/**
 * Transcription manager for handling batch transcriptions and queue
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010!\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u0015\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u0010\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/example/callrecorder/ai/TranscriptionManager;", "", "context", "Landroid/content/Context;", "openAIService", "Lcom/example/callrecorder/ai/OpenAIService;", "repository", "Lcom/example/callrecorder/data/CallRecordingRepository;", "fileManager", "Lcom/example/callrecorder/storage/FileManager;", "(Landroid/content/Context;Lcom/example/callrecorder/ai/OpenAIService;Lcom/example/callrecorder/data/CallRecordingRepository;Lcom/example/callrecorder/storage/FileManager;)V", "isProcessing", "", "transcriptionQueue", "", "", "getQueueStatus", "Lcom/example/callrecorder/ai/TranscriptionQueueStatus;", "processQueue", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "queueForTranscription", "recordingId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "transcribeRecording", "app_debug"})
public final class TranscriptionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.callrecorder.ai.OpenAIService openAIService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.callrecorder.data.CallRecordingRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.callrecorder.storage.FileManager fileManager = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.Long> transcriptionQueue = null;
    private boolean isProcessing = false;
    
    public TranscriptionManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.ai.OpenAIService openAIService, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecordingRepository repository, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.storage.FileManager fileManager) {
        super();
    }
    
    /**
     * Add recording to transcription queue
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queueForTranscription(long recordingId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Process transcription queue
     */
    private final java.lang.Object processQueue(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Transcribe a single recording
     */
    private final java.lang.Object transcribeRecording(long recordingId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Get queue status
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.callrecorder.ai.TranscriptionQueueStatus getQueueStatus() {
        return null;
    }
}