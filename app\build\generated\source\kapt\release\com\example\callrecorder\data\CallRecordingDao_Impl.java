package com.example.callrecorder.data;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CallRecordingDao_Impl implements CallRecordingDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CallRecording> __insertionAdapterOfCallRecording;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<CallRecording> __deletionAdapterOfCallRecording;

  private final EntityDeletionOrUpdateAdapter<CallRecording> __updateAdapterOfCallRecording;

  private final SharedSQLiteStatement __preparedStmtOfDeleteRecordingById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllRecordings;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTranscriptionStatus;

  public CallRecordingDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCallRecording = new EntityInsertionAdapter<CallRecording>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `call_recordings` (`id`,`phoneNumber`,`contactName`,`callType`,`callDate`,`duration`,`filePath`,`fileSize`,`recordingDuration`,`isTranscribed`,`transcriptPath`,`createdAt`,`updatedAt`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CallRecording entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPhoneNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPhoneNumber());
        }
        if (entity.getContactName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getContactName());
        }
        final String _tmp = __converters.fromCallType(entity.getCallType());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCallDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getDuration());
        if (entity.getFilePath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFilePath());
        }
        statement.bindLong(8, entity.getFileSize());
        statement.bindLong(9, entity.getRecordingDuration());
        final int _tmp_2 = entity.isTranscribed() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        if (entity.getTranscriptPath() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTranscriptPath());
        }
        final Long _tmp_3 = __converters.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp_3);
        }
        final Long _tmp_4 = __converters.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_4);
        }
      }
    };
    this.__deletionAdapterOfCallRecording = new EntityDeletionOrUpdateAdapter<CallRecording>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `call_recordings` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CallRecording entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfCallRecording = new EntityDeletionOrUpdateAdapter<CallRecording>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `call_recordings` SET `id` = ?,`phoneNumber` = ?,`contactName` = ?,`callType` = ?,`callDate` = ?,`duration` = ?,`filePath` = ?,`fileSize` = ?,`recordingDuration` = ?,`isTranscribed` = ?,`transcriptPath` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CallRecording entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPhoneNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPhoneNumber());
        }
        if (entity.getContactName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getContactName());
        }
        final String _tmp = __converters.fromCallType(entity.getCallType());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getCallDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getDuration());
        if (entity.getFilePath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFilePath());
        }
        statement.bindLong(8, entity.getFileSize());
        statement.bindLong(9, entity.getRecordingDuration());
        final int _tmp_2 = entity.isTranscribed() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        if (entity.getTranscriptPath() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTranscriptPath());
        }
        final Long _tmp_3 = __converters.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp_3);
        }
        final Long _tmp_4 = __converters.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_4);
        }
        statement.bindLong(14, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteRecordingById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM call_recordings WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllRecordings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM call_recordings";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTranscriptionStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE call_recordings SET isTranscribed = ?, transcriptPath = ?, updatedAt = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertRecording(final CallRecording recording,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfCallRecording.insertAndReturnId(recording);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteRecording(final CallRecording recording,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCallRecording.handle(recording);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRecording(final CallRecording recording,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCallRecording.handle(recording);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteRecordingById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteRecordingById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteRecordingById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllRecordings(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllRecordings.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllRecordings.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTranscriptionStatus(final long id, final boolean isTranscribed,
      final String transcriptPath, final Date updatedAt,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTranscriptionStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isTranscribed ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        if (transcriptPath == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, transcriptPath);
        }
        _argIndex = 3;
        final Long _tmp_1 = __converters.dateToTimestamp(updatedAt);
        if (_tmp_1 == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp_1);
        }
        _argIndex = 4;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateTranscriptionStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CallRecording>> getAllRecordings() {
    final String _sql = "SELECT * FROM call_recordings ORDER BY callDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"call_recordings"}, new Callable<List<CallRecording>>() {
      @Override
      @NonNull
      public List<CallRecording> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CallRecording> _result = new ArrayList<CallRecording>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CallRecording _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp);
            final Date _tmpCallDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_1);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_2 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_3);
            final Date _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_4);
            _item = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecordingById(final long id,
      final Continuation<? super CallRecording> $completion) {
    final String _sql = "SELECT * FROM call_recordings WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CallRecording>() {
      @Override
      @Nullable
      public CallRecording call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final CallRecording _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp);
            final Date _tmpCallDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_1);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_2 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_3);
            final Date _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_4);
            _result = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CallRecording>> getRecordingsByPhoneNumber(final String phoneNumber) {
    final String _sql = "SELECT * FROM call_recordings WHERE phoneNumber = ? ORDER BY callDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (phoneNumber == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, phoneNumber);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"call_recordings"}, new Callable<List<CallRecording>>() {
      @Override
      @NonNull
      public List<CallRecording> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CallRecording> _result = new ArrayList<CallRecording>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CallRecording _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp);
            final Date _tmpCallDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_1);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_2 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_3);
            final Date _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_4);
            _item = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CallRecording>> getRecordingsByType(final CallType callType) {
    final String _sql = "SELECT * FROM call_recordings WHERE callType = ? ORDER BY callDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromCallType(callType);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"call_recordings"}, new Callable<List<CallRecording>>() {
      @Override
      @NonNull
      public List<CallRecording> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CallRecording> _result = new ArrayList<CallRecording>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CallRecording _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp_1);
            final Date _tmpCallDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_3 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_4);
            final Date _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_5);
            _item = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CallRecording>> getRecordingsByDateRange(final Date startDate,
      final Date endDate) {
    final String _sql = "SELECT * FROM call_recordings WHERE callDate BETWEEN ? AND ? ORDER BY callDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"call_recordings"}, new Callable<List<CallRecording>>() {
      @Override
      @NonNull
      public List<CallRecording> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CallRecording> _result = new ArrayList<CallRecording>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CallRecording _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp_2);
            final Date _tmpCallDate;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_3);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_4 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_5);
            final Date _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_6);
            _item = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CallRecording>> getRecordingsByTranscriptionStatus(final boolean isTranscribed) {
    final String _sql = "SELECT * FROM call_recordings WHERE isTranscribed = ? ORDER BY callDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final int _tmp = isTranscribed ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"call_recordings"}, new Callable<List<CallRecording>>() {
      @Override
      @NonNull
      public List<CallRecording> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhoneNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "phoneNumber");
          final int _cursorIndexOfContactName = CursorUtil.getColumnIndexOrThrow(_cursor, "contactName");
          final int _cursorIndexOfCallType = CursorUtil.getColumnIndexOrThrow(_cursor, "callType");
          final int _cursorIndexOfCallDate = CursorUtil.getColumnIndexOrThrow(_cursor, "callDate");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfFileSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fileSize");
          final int _cursorIndexOfRecordingDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "recordingDuration");
          final int _cursorIndexOfIsTranscribed = CursorUtil.getColumnIndexOrThrow(_cursor, "isTranscribed");
          final int _cursorIndexOfTranscriptPath = CursorUtil.getColumnIndexOrThrow(_cursor, "transcriptPath");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CallRecording> _result = new ArrayList<CallRecording>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CallRecording _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhoneNumber;
            if (_cursor.isNull(_cursorIndexOfPhoneNumber)) {
              _tmpPhoneNumber = null;
            } else {
              _tmpPhoneNumber = _cursor.getString(_cursorIndexOfPhoneNumber);
            }
            final String _tmpContactName;
            if (_cursor.isNull(_cursorIndexOfContactName)) {
              _tmpContactName = null;
            } else {
              _tmpContactName = _cursor.getString(_cursorIndexOfContactName);
            }
            final CallType _tmpCallType;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCallType)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfCallType);
            }
            _tmpCallType = __converters.toCallType(_tmp_1);
            final Date _tmpCallDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCallDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCallDate);
            }
            _tmpCallDate = __converters.fromTimestamp(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final long _tmpFileSize;
            _tmpFileSize = _cursor.getLong(_cursorIndexOfFileSize);
            final long _tmpRecordingDuration;
            _tmpRecordingDuration = _cursor.getLong(_cursorIndexOfRecordingDuration);
            final boolean _tmpIsTranscribed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsTranscribed);
            _tmpIsTranscribed = _tmp_3 != 0;
            final String _tmpTranscriptPath;
            if (_cursor.isNull(_cursorIndexOfTranscriptPath)) {
              _tmpTranscriptPath = null;
            } else {
              _tmpTranscriptPath = _cursor.getString(_cursorIndexOfTranscriptPath);
            }
            final Date _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.fromTimestamp(_tmp_4);
            final Date _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = __converters.fromTimestamp(_tmp_5);
            _item = new CallRecording(_tmpId,_tmpPhoneNumber,_tmpContactName,_tmpCallType,_tmpCallDate,_tmpDuration,_tmpFilePath,_tmpFileSize,_tmpRecordingDuration,_tmpIsTranscribed,_tmpTranscriptPath,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecordingCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM call_recordings";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalFileSize(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(fileSize) FROM call_recordings";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
