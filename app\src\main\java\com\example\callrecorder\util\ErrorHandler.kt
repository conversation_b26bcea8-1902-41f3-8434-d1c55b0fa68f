package com.example.callrecorder.util

import android.content.Context
import android.os.Build
import android.util.Log
import com.example.callrecorder.permission.PermissionManager

/**
 * Comprehensive error handler for call recording app
 * Handles all edge cases and provides user-friendly error messages
 */
class ErrorHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "CallRecorderError"
    }
    
    /**
     * Handle permission-related errors
     */
    fun handlePermissionError(missingPermissions: List<String>): ErrorResult {
        val permissionManager = PermissionManager(context)
        val criticalPermissions = listOf(
            android.Manifest.permission.RECORD_AUDIO,
            android.Manifest.permission.READ_PHONE_STATE
        )
        
        val hasCriticalMissing = missingPermissions.any { it in criticalPermissions }
        
        return if (hasCriticalMissing) {
            ErrorResult(
                type = ErrorType.CRITICAL_PERMISSION_MISSING,
                title = "Critical Permissions Required",
                message = "The app cannot function without microphone and phone state permissions. " +
                        "Please grant these permissions in Settings.",
                actionText = "Open Settings",
                isRecoverable = true
            )
        } else {
            ErrorResult(
                type = ErrorType.OPTIONAL_PERMISSION_MISSING,
                title = "Additional Permissions Needed",
                message = "Some features may not work without all permissions. " +
                        "Grant remaining permissions for full functionality.",
                actionText = "Grant Permissions",
                isRecoverable = true
            )
        }
    }
    
    /**
     * Handle device compatibility errors
     */
    fun handleDeviceCompatibilityError(): ErrorResult {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                ErrorResult(
                    type = ErrorType.ANDROID_10_LIMITATION,
                    title = "Android 10+ Limitation",
                    message = "Android 10 and newer versions have restrictions on call recording. " +
                            "This app uses MediaProjection which may not capture call audio on all devices. " +
                            "Recording capability depends on your device manufacturer.",
                    actionText = "Continue Anyway",
                    isRecoverable = true
                )
            }
            
            Build.VERSION.SDK_INT < Build.VERSION_CODES.N -> {
                ErrorResult(
                    type = ErrorType.UNSUPPORTED_ANDROID_VERSION,
                    title = "Unsupported Android Version",
                    message = "This app requires Android 7.0 (API 24) or higher. " +
                            "Your device is running Android ${Build.VERSION.RELEASE}.",
                    actionText = null,
                    isRecoverable = false
                )
            }
            
            else -> {
                ErrorResult(
                    type = ErrorType.UNKNOWN_COMPATIBILITY,
                    title = "Device Compatibility Unknown",
                    message = "Call recording may not work properly on this device. " +
                            "Please test the functionality before relying on it.",
                    actionText = "Test Recording",
                    isRecoverable = true
                )
            }
        }
    }
    
    /**
     * Handle recording service errors
     */
    fun handleRecordingServiceError(exception: Exception): ErrorResult {
        Log.e(TAG, "Recording service error", exception)
        
        return when {
            exception.message?.contains("AudioRecord") == true -> {
                ErrorResult(
                    type = ErrorType.AUDIO_RECORD_FAILED,
                    title = "Audio Recording Failed",
                    message = "Failed to initialize audio recording. This may be due to:\n" +
                            "• Another app using the microphone\n" +
                            "• Hardware limitations\n" +
                            "• Android system restrictions",
                    actionText = "Retry",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("MediaProjection") == true -> {
                ErrorResult(
                    type = ErrorType.MEDIA_PROJECTION_FAILED,
                    title = "Screen Recording Permission Failed",
                    message = "Failed to obtain screen recording permission. " +
                            "This is required for call recording on Android 10+.",
                    actionText = "Grant Permission",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("Storage") == true -> {
                ErrorResult(
                    type = ErrorType.STORAGE_ERROR,
                    title = "Storage Error",
                    message = "Failed to save recording to storage. Check available space and permissions.",
                    actionText = "Check Storage",
                    isRecoverable = true
                )
            }
            
            else -> {
                ErrorResult(
                    type = ErrorType.UNKNOWN_RECORDING_ERROR,
                    title = "Recording Error",
                    message = "An unexpected error occurred during recording: ${exception.message}",
                    actionText = "Retry",
                    isRecoverable = true
                )
            }
        }
    }
    
    /**
     * Handle file system errors
     */
    fun handleFileSystemError(exception: Exception): ErrorResult {
        Log.e(TAG, "File system error", exception)
        
        return when {
            exception.message?.contains("No space") == true -> {
                ErrorResult(
                    type = ErrorType.INSUFFICIENT_STORAGE,
                    title = "Insufficient Storage",
                    message = "Not enough storage space to save recordings. " +
                            "Free up space or change storage location.",
                    actionText = "Manage Storage",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("Permission denied") == true -> {
                ErrorResult(
                    type = ErrorType.STORAGE_PERMISSION_DENIED,
                    title = "Storage Permission Denied",
                    message = "Cannot access storage location. Check app permissions.",
                    actionText = "Check Permissions",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("File not found") == true -> {
                ErrorResult(
                    type = ErrorType.FILE_NOT_FOUND,
                    title = "Recording File Missing",
                    message = "The recording file could not be found. It may have been deleted or moved.",
                    actionText = "Refresh List",
                    isRecoverable = true
                )
            }
            
            else -> {
                ErrorResult(
                    type = ErrorType.UNKNOWN_FILE_ERROR,
                    title = "File System Error",
                    message = "An error occurred accessing files: ${exception.message}",
                    actionText = "Retry",
                    isRecoverable = true
                )
            }
        }
    }
    
    /**
     * Handle network errors (for AI transcription)
     */
    fun handleNetworkError(exception: Exception): ErrorResult {
        Log.e(TAG, "Network error", exception)
        
        return when {
            exception.message?.contains("timeout") == true -> {
                ErrorResult(
                    type = ErrorType.NETWORK_TIMEOUT,
                    title = "Network Timeout",
                    message = "Request timed out. Check your internet connection and try again.",
                    actionText = "Retry",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("No network") == true -> {
                ErrorResult(
                    type = ErrorType.NO_NETWORK,
                    title = "No Internet Connection",
                    message = "Internet connection required for transcription. Check your connection.",
                    actionText = "Check Connection",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("401") == true || 
            exception.message?.contains("403") == true -> {
                ErrorResult(
                    type = ErrorType.API_AUTHENTICATION_FAILED,
                    title = "API Authentication Failed",
                    message = "Invalid API key or authentication failed. Check your OpenAI API configuration.",
                    actionText = "Check API Key",
                    isRecoverable = true
                )
            }
            
            exception.message?.contains("429") == true -> {
                ErrorResult(
                    type = ErrorType.API_RATE_LIMIT,
                    title = "API Rate Limit Exceeded",
                    message = "Too many requests to the transcription service. Please wait and try again.",
                    actionText = "Try Later",
                    isRecoverable = true
                )
            }
            
            else -> {
                ErrorResult(
                    type = ErrorType.UNKNOWN_NETWORK_ERROR,
                    title = "Network Error",
                    message = "Network error occurred: ${exception.message}",
                    actionText = "Retry",
                    isRecoverable = true
                )
            }
        }
    }
    
    /**
     * Handle database errors
     */
    fun handleDatabaseError(exception: Exception): ErrorResult {
        Log.e(TAG, "Database error", exception)
        
        return ErrorResult(
            type = ErrorType.DATABASE_ERROR,
            title = "Database Error",
            message = "Error accessing app database. Try restarting the app.",
            actionText = "Restart App",
            isRecoverable = true
        )
    }
    
    /**
     * Get device-specific limitations message
     */
    fun getDeviceLimitationsMessage(): String {
        return buildString {
            append("Device: ${Build.MANUFACTURER} ${Build.MODEL}\n")
            append("Android: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})\n\n")
            
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> {
                    append("Android 14+ Limitations:\n")
                    append("• Enhanced foreground service restrictions\n")
                    append("• Stricter audio recording policies\n")
                    append("• Additional privacy controls\n")
                }
                
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                    append("Android 13+ Limitations:\n")
                    append("• Runtime notification permission required\n")
                    append("• Granular media permissions\n")
                    append("• Enhanced privacy dashboard\n")
                }
                
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                    append("Android 12+ Limitations:\n")
                    append("• Exact alarm restrictions\n")
                    append("• Microphone/camera indicators\n")
                    append("• Privacy dashboard monitoring\n")
                }
                
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                    append("Android 11+ Limitations:\n")
                    append("• Package visibility restrictions\n")
                    append("• Scoped storage enforcement\n")
                    append("• Auto-reset permissions\n")
                }
                
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                    append("Android 10+ Limitations:\n")
                    append("• Cannot use AudioSource.VOICE_CALL\n")
                    append("• Must use MediaProjection API\n")
                    append("• Scoped storage restrictions\n")
                    append("• Background activity limits\n")
                }
                
                else -> {
                    append("Android 7-9 Support:\n")
                    append("• Better call recording support\n")
                    append("• Traditional storage access\n")
                    append("• Fewer privacy restrictions\n")
                }
            }
        }
    }
}

/**
 * Error result data class
 */
data class ErrorResult(
    val type: ErrorType,
    val title: String,
    val message: String,
    val actionText: String?,
    val isRecoverable: Boolean
)

/**
 * Error types enum
 */
enum class ErrorType {
    // Permission errors
    CRITICAL_PERMISSION_MISSING,
    OPTIONAL_PERMISSION_MISSING,
    STORAGE_PERMISSION_DENIED,
    
    // Device compatibility
    ANDROID_10_LIMITATION,
    UNSUPPORTED_ANDROID_VERSION,
    UNKNOWN_COMPATIBILITY,
    
    // Recording errors
    AUDIO_RECORD_FAILED,
    MEDIA_PROJECTION_FAILED,
    UNKNOWN_RECORDING_ERROR,
    
    // File system errors
    INSUFFICIENT_STORAGE,
    FILE_NOT_FOUND,
    STORAGE_ERROR,
    UNKNOWN_FILE_ERROR,
    
    // Network errors
    NETWORK_TIMEOUT,
    NO_NETWORK,
    API_AUTHENTICATION_FAILED,
    API_RATE_LIMIT,
    UNKNOWN_NETWORK_ERROR,
    
    // Database errors
    DATABASE_ERROR
}
