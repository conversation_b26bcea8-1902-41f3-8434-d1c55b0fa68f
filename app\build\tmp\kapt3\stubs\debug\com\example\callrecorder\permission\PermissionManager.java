package com.example.callrecorder.permission;

/**
 * Comprehensive permission manager for call recording app
 *
 * Android Version Specific Permissions:
 * - API 24-28: Basic permissions work normally
 * - API 29+: MediaProjection required for call recording
 * - API 30+: MANAGE_EXTERNAL_STORAGE for full storage access
 * - API 31+: Exact alarm permission for scheduling
 * - API 33+: POST_NOTIFICATIONS for foreground service
 * - API 34+: Enhanced foreground service restrictions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0015\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 )2\u00020\u0001:\u0001)B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0005\u001a\u00020\u0006J\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u000e\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\tJ\u0011\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\u0002\u0010\u000eJ\u008b\u0001\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u000e\u0010\u0013\u001a\n\u0012\u0006\b\u0001\u0012\u00020\t0\r2\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00100\u00172B\u0010\u0018\u001a>\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\f\b\u001a\u0012\b\b\u001b\u0012\u0004\b\b(\u001c\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\f\b\u001a\u0012\b\b\u001b\u0012\u0004\b\b(\u001d\u0012\u0004\u0012\u00020\u00100\u00192\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00100\u0017\u00a2\u0006\u0002\u0010\u001fJ\u0006\u0010 \u001a\u00020\u0006J\u0006\u0010!\u001a\u00020\u0006J\u0006\u0010\"\u001a\u00020\u0006J\u000e\u0010#\u001a\u00020\u00102\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020\u00102\u0006\u0010$\u001a\u00020%J\u000e\u0010\'\u001a\u00020\u00102\u0006\u0010$\u001a\u00020%J\u000e\u0010(\u001a\u00020\u00102\u0006\u0010$\u001a\u00020%R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/example/callrecorder/permission/PermissionManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "areAllPermissionsGranted", "", "getMissingPermissions", "", "", "getPermissionExplanation", "permission", "getRequiredPermissions", "", "()[Ljava/lang/String;", "handlePermissionResult", "", "requestCode", "", "permissions", "grantResults", "", "onAllGranted", "Lkotlin/Function0;", "onSomeGranted", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "granted", "denied", "onAllDenied", "(I[Ljava/lang/String;[ILkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V", "isAccessibilityServiceEnabled", "isManageExternalStorageGranted", "isSystemAlertWindowGranted", "openAccessibilitySettings", "activity", "Landroid/app/Activity;", "requestManageExternalStorage", "requestMissingPermissions", "requestSystemAlertWindow", "Companion", "app_debug"})
public final class PermissionManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    public static final int PERMISSION_REQUEST_CODE = 1001;
    public static final int MANAGE_EXTERNAL_STORAGE_REQUEST_CODE = 1002;
    public static final int SYSTEM_ALERT_WINDOW_REQUEST_CODE = 1003;
    public static final int ACCESSIBILITY_SERVICE_REQUEST_CODE = 1004;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] CORE_PERMISSIONS = {"android.permission.RECORD_AUDIO", "android.permission.READ_PHONE_STATE", "android.permission.READ_CALL_LOG", "android.permission.READ_CONTACTS"};
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] SERVICE_PERMISSIONS = {"android.permission.FOREGROUND_SERVICE", "android.permission.WAKE_LOCK"};
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] STORAGE_PERMISSIONS_LEGACY = {"android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"};
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] NETWORK_PERMISSIONS = {"android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"};
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.permission.PermissionManager.Companion Companion = null;
    
    public PermissionManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Check if all required permissions are granted
     */
    public final boolean areAllPermissionsGranted() {
        return false;
    }
    
    /**
     * Get list of required permissions based on Android version
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String[] getRequiredPermissions() {
        return null;
    }
    
    /**
     * Get list of missing permissions
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getMissingPermissions() {
        return null;
    }
    
    /**
     * Request all missing permissions
     */
    public final void requestMissingPermissions(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Check if MANAGE_EXTERNAL_STORAGE permission is needed and granted
     */
    public final boolean isManageExternalStorageGranted() {
        return false;
    }
    
    /**
     * Request MANAGE_EXTERNAL_STORAGE permission
     */
    public final void requestManageExternalStorage(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Check if System Alert Window permission is granted
     */
    public final boolean isSystemAlertWindowGranted() {
        return false;
    }
    
    /**
     * Request System Alert Window permission
     */
    public final void requestSystemAlertWindow(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Check if Accessibility Service is enabled
     */
    public final boolean isAccessibilityServiceEnabled() {
        return false;
    }
    
    /**
     * Open Accessibility Settings
     */
    public final void openAccessibilitySettings(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Get permission explanation text
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPermissionExplanation(@org.jetbrains.annotations.NotNull()
    java.lang.String permission) {
        return null;
    }
    
    /**
     * Handle permission request result
     */
    public final void handlePermissionResult(int requestCode, @org.jetbrains.annotations.NotNull()
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull()
    int[] grantResults, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAllGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.util.List<java.lang.String>, ? super java.util.List<java.lang.String>, kotlin.Unit> onSomeGranted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAllDenied) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\r\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\b\u0010\tR\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0019\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\r\u0010\tR\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\u0010\u0010\tR\u0019\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\n\n\u0002\u0010\n\u001a\u0004\b\u0012\u0010\tR\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/callrecorder/permission/PermissionManager$Companion;", "", "()V", "ACCESSIBILITY_SERVICE_REQUEST_CODE", "", "CORE_PERMISSIONS", "", "", "getCORE_PERMISSIONS", "()[Ljava/lang/String;", "[Ljava/lang/String;", "MANAGE_EXTERNAL_STORAGE_REQUEST_CODE", "NETWORK_PERMISSIONS", "getNETWORK_PERMISSIONS", "PERMISSION_REQUEST_CODE", "SERVICE_PERMISSIONS", "getSERVICE_PERMISSIONS", "STORAGE_PERMISSIONS_LEGACY", "getSTORAGE_PERMISSIONS_LEGACY", "SYSTEM_ALERT_WINDOW_REQUEST_CODE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getCORE_PERMISSIONS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getSERVICE_PERMISSIONS() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getSTORAGE_PERMISSIONS_LEGACY() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String[] getNETWORK_PERMISSIONS() {
            return null;
        }
    }
}