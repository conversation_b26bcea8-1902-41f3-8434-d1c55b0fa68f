%com.example.callrecorder.MainActivity7com.example.callrecorder.ai.TranscriptionResult.Success5com.example.callrecorder.ai.TranscriptionResult.Error,com.example.callrecorder.audio.PlaybackState&com.example.callrecorder.data.CallType3com.example.callrecorder.data.CallRecordingDatabase9com.example.callrecorder.service.CallAccessibilityService5com.example.callrecorder.service.CallRecordingService6com.example.callrecorder.service.MediaProjectionHelper=com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel'com.example.callrecorder.util.ErrorType                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       