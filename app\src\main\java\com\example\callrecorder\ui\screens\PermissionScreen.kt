package com.example.callrecorder.ui.screens

import android.app.Activity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.callrecorder.permission.PermissionManager

@Composable
fun PermissionScreen(
    onPermissionsGranted: () -> Unit
) {
    val context = LocalContext.current
    val activity = context as Activity
    val permissionManager = remember { PermissionManager(context) }
    
    var permissionStates by remember { mutableStateOf(mapOf<String, Boolean>()) }
    var isCheckingPermissions by remember { mutableStateOf(false) }
    
    // Check permissions on composition
    LaunchedEffect(Unit) {
        checkAllPermissions(permissionManager) { states ->
            permissionStates = states
            if (states.values.all { it }) {
                onPermissionsGranted()
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Permissions Required",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        Text(
            text = "This app needs the following permissions to record calls:",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(permissionManager.getRequiredPermissions().toList()) { permission ->
                PermissionItem(
                    permission = permission,
                    isGranted = permissionStates[permission] ?: false,
                    explanation = permissionManager.getPermissionExplanation(permission)
                )
            }
            
            // Special permissions
            item {
                SpecialPermissionItem(
                    title = "Manage External Storage",
                    explanation = "Required for saving recordings to external storage (Android 11+)",
                    isGranted = permissionManager.isManageExternalStorageGranted(),
                    onRequest = { permissionManager.requestManageExternalStorage(activity) }
                )
            }
            
            item {
                SpecialPermissionItem(
                    title = "System Alert Window",
                    explanation = "Required for overlay controls during calls",
                    isGranted = permissionManager.isSystemAlertWindowGranted(),
                    onRequest = { permissionManager.requestSystemAlertWindow(activity) }
                )
            }
            
            item {
                SpecialPermissionItem(
                    title = "Accessibility Service",
                    explanation = "Required as fallback for call detection",
                    isGranted = permissionManager.isAccessibilityServiceEnabled(),
                    onRequest = { permissionManager.openAccessibilitySettings(activity) }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            OutlinedButton(
                onClick = {
                    isCheckingPermissions = true
                    checkAllPermissions(permissionManager) { states ->
                        permissionStates = states
                        isCheckingPermissions = false
                        if (states.values.all { it }) {
                            onPermissionsGranted()
                        }
                    }
                },
                enabled = !isCheckingPermissions
            ) {
                if (isCheckingPermissions) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Check Again")
                }
            }
            
            Button(
                onClick = {
                    permissionManager.requestMissingPermissions(activity)
                }
            ) {
                Text("Grant Permissions")
            }
        }
    }
}

@Composable
fun PermissionItem(
    permission: String,
    isGranted: Boolean,
    explanation: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (isGranted) Icons.Default.Check else Icons.Default.Close,
                contentDescription = if (isGranted) "Granted" else "Not granted",
                tint = if (isGranted) 
                    MaterialTheme.colorScheme.onPrimaryContainer 
                else 
                    MaterialTheme.colorScheme.onErrorContainer
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = permission.substringAfterLast("."),
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = explanation,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
fun SpecialPermissionItem(
    title: String,
    explanation: String,
    isGranted: Boolean,
    onRequest: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isGranted) Icons.Default.Check else Icons.Default.Close,
                    contentDescription = if (isGranted) "Granted" else "Not granted",
                    tint = if (isGranted) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = explanation,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
            
            if (!isGranted) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onRequest,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Enable")
                }
            }
        }
    }
}

private fun checkAllPermissions(
    permissionManager: PermissionManager,
    onResult: (Map<String, Boolean>) -> Unit
) {
    val permissions = permissionManager.getRequiredPermissions()
    val states = mutableMapOf<String, Boolean>()
    
    permissions.forEach { permission ->
        states[permission] = permissionManager.areAllPermissionsGranted()
    }
    
    onResult(states)
}
