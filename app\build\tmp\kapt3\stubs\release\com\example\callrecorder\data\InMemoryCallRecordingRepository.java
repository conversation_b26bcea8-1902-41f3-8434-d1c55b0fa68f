package com.example.callrecorder.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u0004H\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u0004H\u0002J\u0010\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0004H\u0002J\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0012J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u0004H\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u0018J(\u0010\u0019\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\rH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\f\u0010\u001e\u001a\u00020\u0014*\u00020\u0007H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/example/callrecorder/data/InMemoryCallRecordingRepository;", "", "()V", "nextId", "", "recordings", "", "Lcom/example/callrecorder/data/CallRecording;", "deleteRecordingById", "", "id", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "formatDuration", "", "durationMs", "formatFileSize", "sizeBytes", "getAllRecordings", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/callrecorder/data/CallRecordingDisplay;", "getRecordingById", "insertRecording", "recording", "(Lcom/example/callrecorder/data/CallRecording;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTranscriptionStatus", "isTranscribed", "", "transcriptPath", "(JZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "toDisplayObject", "app_release"})
public final class InMemoryCallRecordingRepository {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.callrecorder.data.CallRecording> recordings = null;
    private long nextId = 1L;
    
    public InMemoryCallRecordingRepository() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecordingDisplay>> getAllRecordings() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getRecordingById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.callrecorder.data.CallRecording> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertRecording(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecording recording, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteRecordingById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateTranscriptionStatus(long id, boolean isTranscribed, @org.jetbrains.annotations.Nullable()
    java.lang.String transcriptPath, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Convert CallRecording to CallRecordingDisplay with formatted data
     */
    private final com.example.callrecorder.data.CallRecordingDisplay toDisplayObject(com.example.callrecorder.data.CallRecording $this$toDisplayObject) {
        return null;
    }
    
    /**
     * Format duration in milliseconds to readable string
     */
    private final java.lang.String formatDuration(long durationMs) {
        return null;
    }
    
    /**
     * Format file size in bytes to readable string
     */
    private final java.lang.String formatFileSize(long sizeBytes) {
        return null;
    }
}