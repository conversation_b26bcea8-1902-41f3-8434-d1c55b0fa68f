package com.example.callrecorder.ui

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.callrecorder.service.MediaProjectionHelper
import com.example.callrecorder.ui.viewmodel.CallRecordingsViewModel

@Composable
fun PermissionAndConsentScreen(
    onPermissionsGranted: () -> Unit
) {
    val context = LocalContext.current
    var showConsent by remember { mutableStateOf(true) }
    var permissionsGranted by remember { mutableStateOf(false) }
    val permissionList = listOf(
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.READ_CALL_LOG,
        Manifest.permission.FOREGROUND_SERVICE
    )
    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { results ->
        permissionsGranted = results.all { it.value }
    }

    if (showConsent) {
        AlertDialog(
            onDismissRequest = {},
            title = { Text("Recording Consent & Privacy Policy") },
            text = {
                Text("This app records calls for your convenience. Recording calls may be illegal in some regions. Always obtain consent and comply with local laws.\n\nBy continuing, you agree to the privacy policy and grant permission to record calls.")
            },
            confirmButton = {
                Button(onClick = {
                    showConsent = false
                    launcher.launch(permissionList.toTypedArray())
                }) { Text("I Agree & Continue") }
            },
            dismissButton = {
                Button(onClick = { (context as? Activity)?.finish() }) { Text("Decline") }
            }
        )
    } else if (!permissionsGranted) {
        SideEffect {
            launcher.launch(permissionList.toTypedArray())
        }
    } else {
        onPermissionsGranted()
    }
}
