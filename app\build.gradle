apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.example.callrecorder'
    compileSdkVersion 35
    defaultConfig {
        applicationId "com.example.callrecorder"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.10'
    }
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.22"
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.activity:activity-compose:1.9.0'
    implementation 'androidx.compose.ui:ui:1.6.7'
    implementation 'androidx.compose.material:material:1.6.7'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.6.7'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    implementation 'androidx.navigation:navigation-compose:2.7.7'
    implementation 'androidx.constraintlayout:constraintlayout-compose:1.1.0-alpha13'
    implementation 'androidx.activity:activity-ktx:1.9.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.compose.material3:material3:1.2.1'
    // Add more dependencies as needed
}
