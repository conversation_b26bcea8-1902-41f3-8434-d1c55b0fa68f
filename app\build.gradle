apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {
    namespace 'com.example.callrecorder'
    compileSdkVersion 35
    defaultConfig {
        applicationId "com.example.callrecorder"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.10'
    }
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.22"
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'

    // Jetpack Compose
    implementation 'androidx.activity:activity-compose:1.9.0'
    implementation 'androidx.compose.ui:ui:1.6.7'
    implementation 'androidx.compose.material:material:1.6.7'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.6.7'
    implementation 'androidx.compose.material3:material3:1.2.1'
    implementation 'androidx.navigation:navigation-compose:2.7.7'
    implementation 'androidx.constraintlayout:constraintlayout-compose:1.1.0-alpha13'

    // Lifecycle and ViewModel
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.activity:activity-ktx:1.9.0'

    // Room Database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Permission handling
    implementation 'androidx.activity:activity-compose:1.9.0'

    // Media and Audio
    implementation 'androidx.media:media:1.7.0'

    // Network for OpenAI API
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // File handling
    implementation 'androidx.documentfile:documentfile:1.0.1'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4:1.6.7'
    debugImplementation 'androidx.compose.ui:ui-tooling:1.6.7'
    debugImplementation 'androidx.compose.ui:ui-test-manifest:1.6.7'
}
