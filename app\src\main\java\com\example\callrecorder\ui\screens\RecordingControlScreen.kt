package com.example.callrecorder.ui.screens

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.callrecorder.service.CallRecordingService

@Composable
fun RecordingControlScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isRecording by remember { mutableStateOf(false) }
    var recordingStatus by remember { mutableStateOf("Ready to record") }
    
    // MediaProjection launcher for Android 10+
    val mediaProjectionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            startRecordingService(context, result.data)
            isRecording = true
            recordingStatus = "Recording active"
        }
    }
    
    Card(
        modifier = modifier.padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Call Recording Control",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // Status indicator
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = if (isRecording) 
                        MaterialTheme.colorScheme.errorContainer 
                    else 
                        MaterialTheme.colorScheme.primaryContainer
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            ) {
                Text(
                    text = recordingStatus,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(12.dp),
                    color = if (isRecording) 
                        MaterialTheme.colorScheme.onErrorContainer 
                    else 
                        MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            // Control buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                if (!isRecording) {
                    Button(
                        onClick = {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                // Request MediaProjection permission for Android 10+
                                val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) 
                                    as android.media.projection.MediaProjectionManager
                                mediaProjectionLauncher.launch(mediaProjectionManager.createScreenCaptureIntent())
                            } else {
                                // For older versions, start recording directly
                                startRecordingService(context, null)
                                isRecording = true
                                recordingStatus = "Recording active"
                            }
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Start Recording",
                            modifier = Modifier.padding(end = 8.dp)
                        )
                        Text("Start Recording")
                    }
                } else {
                    Button(
                        onClick = {
                            stopRecordingService(context)
                            isRecording = false
                            recordingStatus = "Recording stopped"
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Stop Recording",
                            modifier = Modifier.padding(end = 8.dp)
                        )
                        Text("Stop Recording")
                    }
                }
            }
            
            // Information text
            Text(
                text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    "Android 10+ Limitation: This app uses MediaProjection which may not capture call audio on all devices. " +
                    "Call recording depends on device manufacturer implementation."
                } else {
                    "Call recording will start automatically when you make or receive a call."
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
}

private fun startRecordingService(context: Context, mediaProjectionData: Intent?) {
    val intent = Intent(context, CallRecordingService::class.java).apply {
        action = CallRecordingService.ACTION_START_RECORDING
        mediaProjectionData?.let { data ->
            putExtra(CallRecordingService.EXTRA_MEDIA_PROJECTION_DATA, data)
        }
        putExtra(CallRecordingService.EXTRA_PHONE_NUMBER, "Manual Recording")
        putExtra(CallRecordingService.EXTRA_CALL_TYPE, "OUTGOING")
        putExtra(CallRecordingService.EXTRA_CONTACT_NAME, null)
    }
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        context.startForegroundService(intent)
    } else {
        context.startService(intent)
    }
}

private fun stopRecordingService(context: Context) {
    val intent = Intent(context, CallRecordingService::class.java).apply {
        action = CallRecordingService.ACTION_STOP_RECORDING
    }
    context.startService(intent)
}
