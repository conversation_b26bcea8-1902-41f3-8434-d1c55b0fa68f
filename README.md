# Call Recorder App (<PERSON><PERSON><PERSON>, Android API 24+, SDK 35)

This project is a modern call recording app for Android, built with Kotlin, Jetpack Compose, and MVVM architecture. It targets API 24+ (Android 7.0+) and targetSdk 35 (Android 14).

## Features
- Record incoming and outgoing calls (MediaProjection/AccessibilityService for Android 10+)
- Scoped storage for recordings
- Foreground service for recording
- Runtime permissions with user guidance
- Modern UI (Jetpack Compose)
- List, playback, share, and delete recordings
- Privacy policy and consent popup
- (Optional) Whisper API transcription

## Legal & Platform Limitations
- Android 10+ restricts native call recording. Only MediaProjection and AccessibilityService workarounds are possible.
- User consent and privacy policy are required.
- Some devices may block call recording entirely.

## How to Build
1. Open in Android Studio.
2. Sync Gradle and build the project.
3. Run on a real device (not emulator) for full functionality.

## Next Steps
- Implement call recording logic and UI.
- Add permission handling and privacy policy.
- (Optional) Integrate Whisper API for transcription.

---

**Disclaimer:** This app is for educational purposes. Recording calls may be illegal in some regions. Always obtain consent and comply with local laws.
