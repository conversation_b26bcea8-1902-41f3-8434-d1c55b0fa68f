# Call Recorder App (<PERSON><PERSON><PERSON>, Android API 24+, SDK 35)

A fully functional call recording Android app built with <PERSON><PERSON><PERSON>, Jetpack Compose, and modern Android architecture. This app targets API 24+ (Android 7.0+) with targetSdk 35 (Android 14) and implements comprehensive call recording functionality with AI transcription support.

## 🚀 Features

### Core Functionality
- ✅ **Call Recording**: Record both incoming and outgoing calls
- ✅ **MediaProjection API**: Android 10+ compatible recording method
- ✅ **AccessibilityService**: Fallback method for call detection
- ✅ **Scoped Storage**: Compliant with modern Android storage requirements
- ✅ **Foreground Service**: Background recording with persistent notification
- ✅ **Runtime Permissions**: Comprehensive permission handling with explanations

### User Interface
- ✅ **Modern UI**: Built with Jetpack Compose and Material 3
- ✅ **Recordings List**: Display with caller info, date, duration, and file size
- ✅ **Audio Playback**: Built-in media player with controls
- ✅ **Share & Delete**: Easy file management and sharing
- ✅ **Privacy Consent**: Legal compliance popup before recording

### AI Features (Bonus)
- ✅ **OpenAI Whisper Integration**: Automatic call transcription
- ✅ **Transcript Storage**: Save and view conversation transcripts
- ✅ **Cost Estimation**: Calculate transcription costs before processing
- ✅ **Batch Processing**: Queue multiple recordings for transcription

### Technical Features
- ✅ **MVVM Architecture**: Clean separation of concerns
- ✅ **Room Database**: Local storage for call metadata
- ✅ **Coroutines**: Asynchronous operations
- ✅ **Error Handling**: Comprehensive error management
- ✅ **File Management**: Proper file organization and cleanup

## 📱 Android Version Limitations

### Android 14+ (API 34+)
- **Enhanced Foreground Service Restrictions**: Stricter requirements for foreground services
- **Audio Recording Policies**: Additional privacy controls for microphone access
- **Notification Permissions**: Runtime permission required for notifications

### Android 13+ (API 33+)
- **Runtime Notification Permission**: Must request POST_NOTIFICATIONS permission
- **Granular Media Permissions**: More specific media access controls
- **Privacy Dashboard**: Enhanced monitoring of app permissions usage

### Android 12+ (API 31+)
- **Exact Alarm Restrictions**: Limited scheduling capabilities
- **Microphone/Camera Indicators**: Visual indicators when recording
- **Privacy Dashboard**: User can see all permission usage

### Android 11+ (API 30+)
- **Package Visibility**: Restrictions on querying other installed apps
- **Scoped Storage Enforcement**: Mandatory use of scoped storage
- **Auto-reset Permissions**: Unused app permissions automatically revoked

### Android 10+ (API 29+) - CRITICAL LIMITATIONS
- **AudioSource.VOICE_CALL Deprecated**: Cannot directly record call audio
- **MediaProjection Required**: Must use screen recording API for audio capture
- **Device-Dependent**: Recording success depends on manufacturer implementation
- **Background Restrictions**: Limited background execution
- **Scoped Storage**: Restricted file system access

### Android 7-9 (API 24-28)
- **Better Recording Support**: More reliable call audio capture
- **Traditional Storage**: Full external storage access
- **Fewer Restrictions**: Less privacy-focused limitations

## ⚠️ Important Legal Notice

**DISCLAIMER**: This app is provided for educational and personal use only. Users are solely responsible for complying with all applicable laws and regulations regarding call recording in their jurisdiction.

### Legal Requirements by Region

#### United States
- **One-party consent states**: Only one party (you) needs to consent
- **Two-party consent states**: All parties must be informed and consent
- **Federal law**: Generally allows recording if one party consents

#### European Union (GDPR)
- **Explicit consent required**: Must inform all parties before recording
- **Data protection**: Strict rules on storing and processing recordings
- **Right to deletion**: Participants can request deletion of recordings

#### Other Regions
- **Check local laws**: Recording laws vary significantly by country
- **Business use**: Additional restrictions may apply for commercial use
- **Court admissibility**: Recorded calls may not be admissible as evidence

### Best Practices
1. **Always inform participants** that the call is being recorded
2. **Obtain explicit consent** before starting recording
3. **Secure storage** of recorded files
4. **Regular deletion** of old recordings
5. **Respect privacy** of all participants

## 🛠️ Technical Implementation

### Architecture Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Layer      │    │  Domain Layer   │    │   Data Layer    │
│                 │    │                 │    │                 │
│ • Compose UI    │◄──►│ • ViewModels    │◄──►│ • Repository    │
│ • Screens       │    │ • Use Cases     │    │ • Room DB       │
│ • Dialogs       │    │ • Models        │    │ • File Storage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Service Layer   │
                    │                 │
                    │ • Recording     │
                    │ • Call Detection│
                    │ • Accessibility │
                    └─────────────────┘
```

### Key Components

#### 1. Call Detection System
- **PhoneStateListener**: Monitors call state changes
- **BroadcastReceiver**: Detects outgoing calls
- **CallLogObserver**: Monitors call log changes
- **AccessibilityService**: Fallback call detection method

#### 2. Recording Service
- **MediaProjection**: Primary recording method for Android 10+
- **AudioRecord**: Direct audio recording for older versions
- **Foreground Service**: Background recording with notification
- **File Management**: Organized storage with proper naming

#### 3. Permission Management
- **Runtime Permissions**: Dynamic permission requests
- **Permission Explanations**: User-friendly permission descriptions
- **Fallback Handling**: Graceful degradation when permissions denied
- **Special Permissions**: Handle system-level permissions

#### 4. Data Storage
- **Room Database**: Metadata storage (call info, file paths, transcripts)
- **Scoped Storage**: App-specific external storage for recordings
- **File Organization**: Structured folder hierarchy
- **Cleanup Management**: Automatic old file removal

#### 5. AI Transcription
- **OpenAI Whisper API**: High-quality speech-to-text
- **Batch Processing**: Queue multiple files for transcription
- **Cost Estimation**: Calculate API costs before processing
- **Error Handling**: Robust network error management

## 🚀 Installation & Setup

### Prerequisites
- Android Studio Arctic Fox or newer
- Android SDK 35 (Android 14)
- Minimum SDK 24 (Android 7.0)
- Kotlin 1.9.22+
- Gradle 8.4+

### Build Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/call-recorder-app.git
   cd call-recorder-app
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the cloned directory

3. **Configure API Keys (Optional)**
   - For OpenAI Whisper integration, add your API key to `local.properties`:
   ```properties
   openai.api.key=your_openai_api_key_here
   ```

4. **Build the project**
   ```bash
   ./gradlew build
   ```

5. **Install on device**
   ```bash
   ./gradlew installDebug
   ```

### Dependencies
The app uses the following key dependencies:
- **Jetpack Compose**: Modern UI toolkit
- **Room**: Local database
- **Retrofit**: Network requests
- **Coroutines**: Asynchronous programming
- **Material 3**: Design system

## 📖 Usage Instructions

### First Launch Setup

1. **Grant Permissions**
   - The app will request necessary permissions on first launch
   - Grant all permissions for full functionality
   - Some permissions can be granted later if needed

2. **Privacy Consent**
   - Read and accept the privacy notice
   - Understand your legal responsibilities
   - Consent is required before any recording

3. **Optional: Configure AI Transcription**
   - Enter your OpenAI API key in settings
   - Test the connection
   - Set transcription preferences

### Recording Calls

#### Automatic Recording (Recommended)
1. Enable automatic recording in settings
2. The app will detect incoming/outgoing calls
3. Recording starts automatically after consent
4. Notification shows recording status

#### Manual Recording
1. Open the app during a call
2. Tap "Start Recording" button
3. Grant MediaProjection permission if prompted
4. Recording begins immediately

### Managing Recordings

#### Viewing Recordings
- All recordings appear in the main list
- Shows caller info, date, duration, and file size
- Tap any recording to see options

#### Playing Recordings
- Tap the play button or select "Play" from menu
- Built-in audio player with seek controls
- Supports background playback

#### Sharing Recordings
- Select "Share" from the recording menu
- Choose sharing method (email, messaging, etc.)
- Files are shared securely using FileProvider

#### Deleting Recordings
- Select "Delete" from the recording menu
- Confirm deletion in the dialog
- Both audio file and transcript are removed

### AI Transcription

#### Automatic Transcription
1. Enable in settings
2. New recordings are automatically queued
3. Transcription happens in background
4. View transcripts when complete

#### Manual Transcription
1. Select a recording
2. Choose "Transcribe" from menu
3. Review cost estimate
4. Confirm to start transcription

#### Viewing Transcripts
- Transcribed recordings show a document icon
- Select "View Transcript" from menu
- Full text with timestamps (if available)
- Search within transcripts

## ⚙️ Configuration

### Settings Options

#### Recording Settings
- **Auto-record calls**: Enable/disable automatic recording
- **Recording quality**: Choose audio quality (affects file size)
- **Storage location**: Select where to save recordings
- **Auto-delete**: Set automatic cleanup period

#### Privacy Settings
- **Consent popup**: Show consent dialog for each call
- **Caller notification**: Automatically inform callers (where supported)
- **Secure storage**: Enable encryption for recordings

#### AI Settings
- **OpenAI API Key**: Configure Whisper API access
- **Auto-transcribe**: Enable automatic transcription
- **Language**: Set primary language for transcription
- **Cost limits**: Set spending limits for API usage

### Troubleshooting

#### Recording Not Working
1. **Check permissions**: Ensure all required permissions are granted
2. **Android version**: Verify compatibility with your Android version
3. **Device compatibility**: Some devices may not support call recording
4. **Other apps**: Close other apps that might use the microphone

#### Poor Recording Quality
1. **Check microphone**: Ensure microphone is not blocked
2. **Audio settings**: Adjust recording quality in settings
3. **Storage space**: Ensure sufficient storage available
4. **Background apps**: Close unnecessary background apps

#### Transcription Issues
1. **API key**: Verify OpenAI API key is correct and active
2. **Internet connection**: Ensure stable internet connection
3. **File format**: Check that audio format is supported
4. **File size**: Ensure file is under 25MB limit

#### Permission Issues
1. **Manual grant**: Go to Settings > Apps > Call Recorder > Permissions
2. **Special permissions**: Enable accessibility service if needed
3. **Battery optimization**: Disable battery optimization for the app
4. **Auto-start**: Enable auto-start permission on some devices

## 🔧 Development

### Project Structure
```
app/
├── src/main/java/com/example/callrecorder/
│   ├── ai/                 # AI transcription services
│   ├── audio/              # Audio playback management
│   ├── data/               # Data models and database
│   ├── permission/         # Permission handling
│   ├── service/            # Background services
│   ├── storage/            # File management
│   ├── ui/                 # User interface
│   │   ├── dialogs/        # Dialog components
│   │   ├── screens/        # Screen composables
│   │   └── viewmodel/      # ViewModels
│   └── util/               # Utility classes
├── src/main/res/
│   ├── xml/                # XML configurations
│   └── values/             # String resources
└── build.gradle            # App-level build configuration
```

### Key Files
- `MainActivity.kt`: Entry point
- `CallRecordingService.kt`: Main recording service
- `CallDetectionService.kt`: Call state monitoring
- `PermissionManager.kt`: Permission handling
- `OpenAIService.kt`: AI transcription
- `FileManager.kt`: File operations

### Testing
```bash
# Run unit tests
./gradlew test

# Run instrumented tests
./gradlew connectedAndroidTest

# Generate test coverage report
./gradlew jacocoTestReport
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is provided "as is" without warranty of any kind. The developers are not responsible for any legal consequences arising from the use of this application. Users must comply with all applicable laws and regulations regarding call recording in their jurisdiction.

## 🤝 Support

For support, please:
1. Check the troubleshooting section above
2. Search existing GitHub issues
3. Create a new issue with detailed information
4. Include device model, Android version, and error logs

## 🔄 Updates

### Version History
- **v1.0.0**: Initial release with basic recording functionality
- **v1.1.0**: Added AI transcription support
- **v1.2.0**: Enhanced UI and error handling
- **v1.3.0**: Improved Android 14 compatibility

### Planned Features
- [ ] Cloud storage integration
- [ ] Advanced audio filters
- [ ] Multi-language support
- [ ] Call analytics dashboard
- [ ] Export to various formats

## Legal & Platform Limitations
- Android 10+ restricts native call recording. Only MediaProjection and AccessibilityService workarounds are possible.
- User consent and privacy policy are required.
- Some devices may block call recording entirely.

## How to Build
1. Open in Android Studio.
2. Sync Gradle and build the project.
3. Run on a real device (not emulator) for full functionality.

## Next Steps
- Implement call recording logic and UI.
- Add permission handling and privacy policy.
- (Optional) Integrate Whisper API for transcription.

---

**Disclaimer:** This app is for educational purposes. Recording calls may be illegal in some regions. Always obtain consent and comply with local laws.
