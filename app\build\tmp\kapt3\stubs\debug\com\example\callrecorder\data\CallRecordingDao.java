package com.example.callrecorder.data;

/**
 * Data Access Object for CallRecording entity
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0018\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015H\'J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0018\u001a\u00020\u0019H\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001b\u001a\u00020\u001cH\'J\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001e\u001a\u00020\u001fH\'J\u0010\u0010 \u001a\u0004\u0018\u00010\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010!\u001a\u00020\u000b2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\"\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ0\u0010#\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u001c2\b\u0010$\u001a\u0004\u0018\u00010\u00192\u0006\u0010%\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010&\u00a8\u0006\'"}, d2 = {"Lcom/example/callrecorder/data/CallRecordingDao;", "", "deleteAllRecordings", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRecording", "recording", "Lcom/example/callrecorder/data/CallRecording;", "(Lcom/example/callrecorder/data/CallRecording;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRecordingById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllRecordings", "Lkotlinx/coroutines/flow/Flow;", "", "getRecordingById", "getRecordingCount", "", "getRecordingsByDateRange", "startDate", "Ljava/util/Date;", "endDate", "getRecordingsByPhoneNumber", "phoneNumber", "", "getRecordingsByTranscriptionStatus", "isTranscribed", "", "getRecordingsByType", "callType", "Lcom/example/callrecorder/data/CallType;", "getTotalFileSize", "insertRecording", "updateRecording", "updateTranscriptionStatus", "transcriptPath", "updatedAt", "(JZLjava/lang/String;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface CallRecordingDao {
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings ORDER BY callDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecording>> getAllRecordings();
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecordingById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.callrecorder.data.CallRecording> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings WHERE phoneNumber = :phoneNumber ORDER BY callDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecording>> getRecordingsByPhoneNumber(@org.jetbrains.annotations.NotNull()
    java.lang.String phoneNumber);
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings WHERE callType = :callType ORDER BY callDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecording>> getRecordingsByType(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallType callType);
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings WHERE callDate BETWEEN :startDate AND :endDate ORDER BY callDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecording>> getRecordingsByDateRange(@org.jetbrains.annotations.NotNull()
    java.util.Date startDate, @org.jetbrains.annotations.NotNull()
    java.util.Date endDate);
    
    @androidx.room.Query(value = "SELECT * FROM call_recordings WHERE isTranscribed = :isTranscribed ORDER BY callDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.callrecorder.data.CallRecording>> getRecordingsByTranscriptionStatus(boolean isTranscribed);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertRecording(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecording recording, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateRecording(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecording recording, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRecording(@org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallRecording recording, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM call_recordings WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRecordingById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM call_recordings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllRecordings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM call_recordings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecordingCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(fileSize) FROM call_recordings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalFileSize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "UPDATE call_recordings SET isTranscribed = :isTranscribed, transcriptPath = :transcriptPath, updatedAt = :updatedAt WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTranscriptionStatus(long id, boolean isTranscribed, @org.jetbrains.annotations.Nullable()
    java.lang.String transcriptPath, @org.jetbrains.annotations.NotNull()
    java.util.Date updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}