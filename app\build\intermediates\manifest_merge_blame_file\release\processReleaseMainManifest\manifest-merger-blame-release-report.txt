1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.callrecorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Core permissions for call recording -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_CALL_LOG" />
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:5-72
14-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:8:22-69
15
16    <!-- Service permissions -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:5-88
18-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:12:22-85
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:5-94
19-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:13:22-91
20    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:5-85
20-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:14:22-82
21
22    <!-- Storage permissions -->
23    <uses-permission
23-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:5-108
24        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
24-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:22-78
25        android:maxSdkVersion="28" />
25-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:17:79-105
26    <uses-permission
26-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:5-107
27        android:name="android.permission.READ_EXTERNAL_STORAGE"
27-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:22-77
28        android:maxSdkVersion="32" />
28-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:18:78-104
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:5-111
29-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:19:22-79
30
31    <!-- Notification permission for Android 13+ -->
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:5-77
32-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:22:22-74
33
34    <!-- Network for OpenAI API -->
35    <uses-permission android:name="android.permission.INTERNET" />
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:5-67
35-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:25:22-64
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:5-79
36-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:26:22-76
37
38    <!-- Contact access for caller name -->
39    <uses-permission android:name="android.permission.READ_CONTACTS" />
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:5-72
39-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:29:22-69
40
41    <!-- Wake lock for recording service -->
42    <uses-permission android:name="android.permission.WAKE_LOCK" />
42-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:5-68
42-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:32:22-65
43
44    <!-- System alert window for overlay (if needed) -->
45    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:5-78
45-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:35:22-75
46
47    <permission
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.callrecorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
52
53    <application
53-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:37:5-79:19
54        android:allowBackup="true"
54-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:38:9-35
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ae1fe1024e90fb6ad9a5df9a70112b97\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
56        android:extractNativeLibs="false"
57        android:icon="@mipmap/ic_launcher"
57-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:39:9-43
58        android:label="@string/app_name"
58-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:40:9-41
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:41:9-54
60        android:supportsRtl="true"
60-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:42:9-35
61        android:theme="@style/Theme.CallRecorder" >
61-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:43:9-50
62        <activity
62-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:44:9-51:20
63            android:name="com.example.callrecorder.MainActivity"
63-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:45:13-41
64            android:exported="true" >
64-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:46:13-36
65            <intent-filter>
65-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:47:13-50:29
66                <action android:name="android.intent.action.MAIN" />
66-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:17-69
66-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:48:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:17-77
68-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:49:27-74
69            </intent-filter>
70        </activity>
71
72        <service
72-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:52:9-56:74
73            android:name="com.example.callrecorder.service.CallRecordingService"
73-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:53:13-57
74            android:enabled="true"
74-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:54:13-35
75            android:exported="false"
75-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:55:13-37
76            android:foregroundServiceType="microphone|mediaProjection" />
76-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:56:13-71
77        <service
77-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:57:9-67:19
78            android:name="com.example.callrecorder.service.CallAccessibilityService"
78-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:58:13-61
79            android:exported="false"
79-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:60:13-37
80            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
80-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:59:13-79
81            <intent-filter>
81-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:61:13-63:29
82                <action android:name="android.accessibilityservice.AccessibilityService" />
82-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:17-92
82-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:62:25-89
83            </intent-filter>
84
85            <meta-data
85-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:64:13-66:72
86                android:name="android.accessibilityservice"
86-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:65:17-60
87                android:resource="@xml/accessibility_service_config" />
87-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:66:17-69
88        </service>
89
90        <!-- FileProvider for sharing files on Android 7.0+ -->
91        <provider
92            android:name="androidx.core.content.FileProvider"
92-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:71:13-62
93            android:authorities="com.example.callrecorder.fileprovider"
93-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:72:13-64
94            android:exported="false"
94-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:73:13-37
95            android:grantUriPermissions="true" >
95-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:74:13-47
96            <meta-data
96-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:75:13-77:54
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:76:17-67
98                android:resource="@xml/file_paths" />
98-->C:\Users\<USER>\AndroidStudioProjects\Call record\app\src\main\AndroidManifest.xml:77:17-51
99        </provider>
100        <provider
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
101            android:name="androidx.startup.InitializationProvider"
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
102            android:authorities="com.example.callrecorder.androidx-startup"
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
103            android:exported="false" >
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.emoji2.text.EmojiCompatInitializer"
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
106                android:value="androidx.startup" />
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\cae395696aa7b8bd3997c701a8eba835\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
108-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
109                android:value="androidx.startup" />
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\fcf8b2ada9e083d38fe55625b907d145\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
112                android:value="androidx.startup" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
113        </provider>
114
115        <service
115-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
116            android:name="androidx.room.MultiInstanceInvalidationService"
116-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
117            android:directBootAware="true"
117-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
118            android:exported="false" />
118-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ddf4af55fcb670458a431fc6362393a2\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
119
120        <receiver
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
121            android:name="androidx.profileinstaller.ProfileInstallReceiver"
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
122            android:directBootAware="false"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
123            android:enabled="true"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
124            android:exported="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
125            android:permission="android.permission.DUMP" >
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
127                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
128            </intent-filter>
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
130                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
131            </intent-filter>
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
133                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
136                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\2060ec2aff0606b577a2edfbac144866\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
137            </intent-filter>
138        </receiver>
139    </application>
140
141</manifest>
