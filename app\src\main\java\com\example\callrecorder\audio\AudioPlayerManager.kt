package com.example.callrecorder.audio

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

/**
 * Manager for audio playback functionality
 * Handles MediaPlayer lifecycle and provides playback state
 */
class AudioPlayerManager(private val context: Context) {
    
    private var mediaPlayer: MediaPlayer? = null
    
    private val _playbackState = MutableStateFlow(PlaybackState.IDLE)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    private val _currentPosition = MutableStateFlow(0)
    val currentPosition: StateFlow<Int> = _currentPosition.asStateFlow()
    
    private val _duration = MutableStateFlow(0)
    val duration: StateFlow<Int> = _duration.asStateFlow()
    
    private val _currentFile = MutableStateFlow<String?>(null)
    val currentFile: StateFlow<String?> = _currentFile.asStateFlow()
    
    private var positionUpdateRunnable: Runnable? = null
    
    /**
     * Play audio file
     */
    fun playFile(file: File) {
        try {
            stop() // Stop any current playback
            
            mediaPlayer = MediaPlayer().apply {
                setDataSource(file.absolutePath)
                prepareAsync()
                
                setOnPreparedListener { player ->
                    _duration.value = player.duration
                    _currentFile.value = file.absolutePath
                    _playbackState.value = PlaybackState.PLAYING
                    player.start()
                    startPositionUpdates()
                }
                
                setOnCompletionListener {
                    _playbackState.value = PlaybackState.COMPLETED
                    _currentPosition.value = _duration.value
                    stopPositionUpdates()
                }
                
                setOnErrorListener { _, what, extra ->
                    _playbackState.value = PlaybackState.ERROR
                    stopPositionUpdates()
                    true
                }
            }
            
            _playbackState.value = PlaybackState.PREPARING
            
        } catch (e: Exception) {
            _playbackState.value = PlaybackState.ERROR
        }
    }
    
    /**
     * Play audio from URI
     */
    fun playUri(uri: Uri) {
        try {
            stop() // Stop any current playback
            
            mediaPlayer = MediaPlayer().apply {
                setDataSource(context, uri)
                prepareAsync()
                
                setOnPreparedListener { player ->
                    _duration.value = player.duration
                    _currentFile.value = uri.toString()
                    _playbackState.value = PlaybackState.PLAYING
                    player.start()
                    startPositionUpdates()
                }
                
                setOnCompletionListener {
                    _playbackState.value = PlaybackState.COMPLETED
                    _currentPosition.value = _duration.value
                    stopPositionUpdates()
                }
                
                setOnErrorListener { _, what, extra ->
                    _playbackState.value = PlaybackState.ERROR
                    stopPositionUpdates()
                    true
                }
            }
            
            _playbackState.value = PlaybackState.PREPARING
            
        } catch (e: Exception) {
            _playbackState.value = PlaybackState.ERROR
        }
    }
    
    /**
     * Pause playback
     */
    fun pause() {
        mediaPlayer?.let { player ->
            if (player.isPlaying) {
                player.pause()
                _playbackState.value = PlaybackState.PAUSED
                stopPositionUpdates()
            }
        }
    }
    
    /**
     * Resume playback
     */
    fun resume() {
        mediaPlayer?.let { player ->
            if (!player.isPlaying && _playbackState.value == PlaybackState.PAUSED) {
                player.start()
                _playbackState.value = PlaybackState.PLAYING
                startPositionUpdates()
            }
        }
    }
    
    /**
     * Stop playback
     */
    fun stop() {
        mediaPlayer?.let { player ->
            try {
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            } catch (e: Exception) {
                // Ignore errors during cleanup
            }
        }
        
        mediaPlayer = null
        _playbackState.value = PlaybackState.IDLE
        _currentPosition.value = 0
        _duration.value = 0
        _currentFile.value = null
        stopPositionUpdates()
    }
    
    /**
     * Seek to position
     */
    fun seekTo(position: Int) {
        mediaPlayer?.let { player ->
            try {
                player.seekTo(position)
                _currentPosition.value = position
            } catch (e: Exception) {
                // Handle seek error
            }
        }
    }
    
    /**
     * Get current playback position
     */
    fun getCurrentPosition(): Int {
        return mediaPlayer?.currentPosition ?: 0
    }
    
    /**
     * Get duration
     */
    fun getDuration(): Int {
        return mediaPlayer?.duration ?: 0
    }
    
    /**
     * Check if currently playing
     */
    fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying ?: false
    }
    
    /**
     * Start position updates
     */
    private fun startPositionUpdates() {
        stopPositionUpdates()
        
        positionUpdateRunnable = object : Runnable {
            override fun run() {
                mediaPlayer?.let { player ->
                    if (player.isPlaying) {
                        _currentPosition.value = player.currentPosition
                        android.os.Handler(android.os.Looper.getMainLooper())
                            .postDelayed(this, 100) // Update every 100ms
                    }
                }
            }
        }
        
        android.os.Handler(android.os.Looper.getMainLooper())
            .post(positionUpdateRunnable!!)
    }
    
    /**
     * Stop position updates
     */
    private fun stopPositionUpdates() {
        positionUpdateRunnable?.let { runnable ->
            android.os.Handler(android.os.Looper.getMainLooper())
                .removeCallbacks(runnable)
        }
        positionUpdateRunnable = null
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stop()
    }
}

/**
 * Playback state enum
 */
enum class PlaybackState {
    IDLE,
    PREPARING,
    PLAYING,
    PAUSED,
    COMPLETED,
    ERROR
}

/**
 * Extension functions for formatting time
 */
fun Int.formatTime(): String {
    val totalSeconds = this / 1000
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60
    return String.format("%d:%02d", minutes, seconds)
}
