package com.example.callrecorder.storage;

/**
 * File manager for handling call recording storage with Scoped Storage compliance
 *
 * Android Version Storage Behavior:
 * - API 24-28: Traditional external storage with permissions
 * - API 29 (Android 10): Scoped Storage introduced, app-specific directories preferred
 * - API 30+ (Android 11+): Scoped Storage enforced, MANAGE_EXTERNAL_STORAGE for full access
 * - API 33+ (Android 13): Granular media permissions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 $2\u00020\u0001:\u0001$B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006J\u0016\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\u00020\t2\u0006\u0010\u000f\u001a\u00020\tJ\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u000f\u001a\u00020\tJ\u0006\u0010\u0012\u001a\u00020\u0013J\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0016\u001a\u00020\tJ\u000e\u0010\u0017\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\tJ\u0006\u0010\u0018\u001a\u00020\tJ\u0006\u0010\u0019\u001a\u00020\tJ\u0006\u0010\u001a\u001a\u00020\u0013J\u0006\u0010\u001b\u001a\u00020\tJ\u0006\u0010\u001c\u001a\u00020\u0011J\u0006\u0010\u001d\u001a\u00020\u0011J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u001f\u001a\u00020\tJ\u0010\u0010 \u001a\u0004\u0018\u00010!2\u0006\u0010\u000f\u001a\u00020\tJ\u0016\u0010\"\u001a\u00020\u00112\u0006\u0010\u001f\u001a\u00020\t2\u0006\u0010#\u001a\u00020\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/example/callrecorder/storage/FileManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "cleanupOldRecordings", "", "daysOld", "createRecordingFile", "Ljava/io/File;", "phoneNumber", "", "callType", "Lcom/example/callrecorder/data/CallType;", "createTranscriptFile", "recordingFile", "deleteRecording", "", "getAvailableStorageSpace", "", "getFileUri", "Landroid/net/Uri;", "file", "getFormattedFileSize", "getRecordingsDirectory", "getTempDirectory", "getTotalStorageUsed", "getTranscriptsDirectory", "isExternalStorageReadable", "isExternalStorageWritable", "readTranscript", "transcriptFile", "shareRecording", "Landroid/content/Intent;", "writeTranscript", "transcript", "Companion", "app_release"})
public final class FileManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String RECORDINGS_FOLDER = "recordings";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TRANSCRIPTS_FOLDER = "transcripts";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TEMP_FOLDER = "temp";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AUDIO_EXTENSION = ".m4a";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TRANSCRIPT_EXTENSION = ".txt";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String AUDIO_MIME_TYPE = "audio/mp4";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TEXT_MIME_TYPE = "text/plain";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.storage.FileManager.Companion Companion = null;
    
    public FileManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Get the recordings directory (app-specific external storage)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.io.File getRecordingsDirectory() {
        return null;
    }
    
    /**
     * Get the transcripts directory
     */
    @org.jetbrains.annotations.NotNull()
    public final java.io.File getTranscriptsDirectory() {
        return null;
    }
    
    /**
     * Get the temporary files directory
     */
    @org.jetbrains.annotations.NotNull()
    public final java.io.File getTempDirectory() {
        return null;
    }
    
    /**
     * Create a new recording file with proper naming convention
     */
    @org.jetbrains.annotations.NotNull()
    public final java.io.File createRecordingFile(@org.jetbrains.annotations.NotNull()
    java.lang.String phoneNumber, @org.jetbrains.annotations.NotNull()
    com.example.callrecorder.data.CallType callType) {
        return null;
    }
    
    /**
     * Create a transcript file for a recording
     */
    @org.jetbrains.annotations.NotNull()
    public final java.io.File createTranscriptFile(@org.jetbrains.annotations.NotNull()
    java.io.File recordingFile) {
        return null;
    }
    
    /**
     * Get file size in human-readable format
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFormattedFileSize(@org.jetbrains.annotations.NotNull()
    java.io.File file) {
        return null;
    }
    
    /**
     * Get total storage used by recordings
     */
    public final long getTotalStorageUsed() {
        return 0L;
    }
    
    /**
     * Delete a recording file and its associated transcript
     */
    public final boolean deleteRecording(@org.jetbrains.annotations.NotNull()
    java.io.File recordingFile) {
        return false;
    }
    
    /**
     * Share a recording file
     */
    @org.jetbrains.annotations.Nullable()
    public final android.content.Intent shareRecording(@org.jetbrains.annotations.NotNull()
    java.io.File recordingFile) {
        return null;
    }
    
    /**
     * Get URI for a file (for media player or sharing)
     */
    @org.jetbrains.annotations.Nullable()
    public final android.net.Uri getFileUri(@org.jetbrains.annotations.NotNull()
    java.io.File file) {
        return null;
    }
    
    /**
     * Check if external storage is available for writing
     */
    public final boolean isExternalStorageWritable() {
        return false;
    }
    
    /**
     * Check if external storage is available for reading
     */
    public final boolean isExternalStorageReadable() {
        return false;
    }
    
    /**
     * Clean up old recordings (older than specified days)
     */
    public final int cleanupOldRecordings(int daysOld) {
        return 0;
    }
    
    /**
     * Get available storage space
     */
    public final long getAvailableStorageSpace() {
        return 0L;
    }
    
    /**
     * Write transcript to file
     */
    public final boolean writeTranscript(@org.jetbrains.annotations.NotNull()
    java.io.File transcriptFile, @org.jetbrains.annotations.NotNull()
    java.lang.String transcript) {
        return false;
    }
    
    /**
     * Read transcript from file
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String readTranscript(@org.jetbrains.annotations.NotNull()
    java.io.File transcriptFile) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/callrecorder/storage/FileManager$Companion;", "", "()V", "AUDIO_EXTENSION", "", "AUDIO_MIME_TYPE", "RECORDINGS_FOLDER", "TEMP_FOLDER", "TEXT_MIME_TYPE", "TRANSCRIPTS_FOLDER", "TRANSCRIPT_EXTENSION", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}