package com.example.callrecorder.ai;

/**
 * OpenAI Whisper API integration for call transcription
 *
 * Features:
 * - Audio transcription using Whisper API
 * - Support for multiple audio formats
 * - Error handling and retry logic
 * - Cost estimation and usage tracking
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u0000 \u001b2\u00020\u0001:\u0001\u001bB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00060\u000eJ\u000e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\fJ.\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u000b\u001a\u00020\f2\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\u001aR\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/callrecorder/ai/OpenAIService;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "apiKey", "", "apiService", "Lcom/example/callrecorder/ai/OpenAIApiService;", "estimateTranscriptionCost", "Lcom/example/callrecorder/ai/CostEstimate;", "audioFile", "Ljava/io/File;", "getSupportedFormats", "", "initialize", "", "isFormatSupported", "", "file", "transcribeAudio", "Lcom/example/callrecorder/ai/TranscriptionResult;", "language", "prompt", "(Ljava/io/File;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateApiKey", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class OpenAIService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "OpenAIService";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = "https://api.openai.com/v1/";
    private static final int MAX_FILE_SIZE = 26214400;
    private static final long TIMEOUT_SECONDS = 60L;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String apiKey;
    @org.jetbrains.annotations.Nullable()
    private com.example.callrecorder.ai.OpenAIApiService apiService;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.ai.OpenAIService.Companion Companion = null;
    
    public OpenAIService(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Initialize the service with API key
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    java.lang.String apiKey) {
    }
    
    /**
     * Transcribe audio file using Whisper API
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object transcribeAudio(@org.jetbrains.annotations.NotNull()
    java.io.File audioFile, @org.jetbrains.annotations.Nullable()
    java.lang.String language, @org.jetbrains.annotations.Nullable()
    java.lang.String prompt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.callrecorder.ai.TranscriptionResult> $completion) {
        return null;
    }
    
    /**
     * Estimate transcription cost
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.callrecorder.ai.CostEstimate estimateTranscriptionCost(@org.jetbrains.annotations.NotNull()
    java.io.File audioFile) {
        return null;
    }
    
    /**
     * Check if API key is valid
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object validateApiKey(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get supported audio formats
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getSupportedFormats() {
        return null;
    }
    
    /**
     * Check if file format is supported
     */
    public final boolean isFormatSupported(@org.jetbrains.annotations.NotNull()
    java.io.File file) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/callrecorder/ai/OpenAIService$Companion;", "", "()V", "BASE_URL", "", "MAX_FILE_SIZE", "", "TAG", "TIMEOUT_SECONDS", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}