#Mon Jul 21 16:38:53 BDT 2025
base.0=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.10=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
base.11=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.12=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
base.2=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.3=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.4=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.5=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.6=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
base.7=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.8=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.9=C\:\\Users\\ASUS\\AndroidStudioProjects\\Call record\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.10=9/classes.dex
path.11=classes2.dex
path.12=classes3.dex
path.2=12/classes.dex
path.3=13/classes.dex
path.4=2/classes.dex
path.5=3/classes.dex
path.6=4/classes.dex
path.7=5/classes.dex
path.8=6/classes.dex
path.9=8/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.12=classes13.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
