package com.example.callrecorder.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u001a\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0002\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0006H\u0002\u00a8\u0006\n"}, d2 = {"RecordingControlScreen", "", "modifier", "Landroidx/compose/ui/Modifier;", "startRecordingService", "context", "Landroid/content/Context;", "mediaProjectionData", "Landroid/content/Intent;", "stopRecordingService", "app_debug"})
public final class RecordingControlScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void RecordingControlScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    private static final void startRecordingService(android.content.Context context, android.content.Intent mediaProjectionData) {
    }
    
    private static final void stopRecordingService(android.content.Context context) {
    }
}