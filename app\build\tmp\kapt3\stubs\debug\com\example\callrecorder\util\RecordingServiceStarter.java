package com.example.callrecorder.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\bJ\u000e\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\n"}, d2 = {"Lcom/example/callrecorder/util/RecordingServiceStarter;", "", "()V", "start", "", "context", "Landroid/content/Context;", "mediaProjectionData", "Landroid/content/Intent;", "stop", "app_debug"})
public final class RecordingServiceStarter {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.callrecorder.util.RecordingServiceStarter INSTANCE = null;
    
    private RecordingServiceStarter() {
        super();
    }
    
    public final void start(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.content.Intent mediaProjectionData) {
    }
    
    public final void stop(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
}