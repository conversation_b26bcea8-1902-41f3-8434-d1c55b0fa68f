package com.example.callrecorder.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * Entity representing a call recording in the database
 * 
 * Android Version Limitations:
 * - Android 10+ (API 29+): Cannot use AudioSource.VOICE_CALL, must use MediaProjection
 * - Android 11+ (API 30+): Additional restrictions on call audio capture
 * - Android 12+ (API 31+): Enhanced privacy controls
 * - Android 13+ (API 33+): Runtime notification permission required
 * - Android 14+ (API 34+): Stricter foreground service restrictions
 */
@Entity(tableName = "call_recordings")
data class CallRecording(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // Call information
    val phoneNumber: String,
    val contactName: String? = null,
    val callType: CallType,
    val callDate: Date,
    val duration: Long, // in milliseconds
    
    // Recording information
    val filePath: String,
    val fileSize: Long, // in bytes
    val recordingDuration: Long, // in milliseconds
    
    // Metadata
    val isTranscribed: Boolean = false,
    val transcriptPath: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class CallType {
    INCOMING,
    OUTGOING,
    MISSED
}

/**
 * Data class for displaying call recordings in UI
 */
data class CallRecordingDisplay(
    val id: Long,
    val phoneNumber: String,
    val contactName: String?,
    val callType: CallType,
    val callDate: Date,
    val duration: String, // formatted duration
    val filePath: String,
    val fileSize: String, // formatted file size
    val isTranscribed: Boolean,
    val transcriptPath: String?
)
