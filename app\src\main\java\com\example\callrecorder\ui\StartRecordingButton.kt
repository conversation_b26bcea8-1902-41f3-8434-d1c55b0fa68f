package com.example.callrecorder.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.example.callrecorder.util.RecordingServiceStarter

@Composable
fun StartRecordingButton(onStart: () -> Unit) {
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // Start the recording service with MediaProjection data
            RecordingServiceStarter.start(context, result.data)
            onStart()
        }
    }
    Button(onClick = {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val mpm = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as android.media.projection.MediaProjectionManager
            launcher.launch(mpm.createScreenCaptureIntent())
        } else {
            RecordingServiceStarter.start(context)
            onStart()
        }
    }) {
        Text("Start Call Recording")
    }
}
